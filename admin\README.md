# لوحة التحكم - متجر فساتين السهرة

## نظرة عامة

لوحة تحكم احترافية لإدارة متجر فساتين السهرة مصممة بتصميم يحاكي لوحة تحكم سلة (salla.sa) مع جميع الميزات المطلوبة لإدارة المنتجات بشكل شامل.

## الميزات الرئيسية

### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن للمديرين
- إدارة الجلسات
- حماية جميع الصفحات
- أدوار مختلفة للمستخدمين (مدير، مدير مساعد)

### 📊 لوحة التحكم الرئيسية
- إحصائيات شاملة للمتجر
- عدد المنتجات والطلبات
- إجمالي المبيعات
- طلبات جديدة تحتاج متابعة

### 🛍️ إدارة المنتجات الشاملة
- **إضافة منتجات جديدة** مع جميع التفاصيل:
  - عنوان المنتج
  - رابط المنتج (slug)
  - وصف مفصل
  - الأسعار (عادي وتخفيض)
  - التصنيف
  - حالة التوفر (متوفر/نفذ)
  - فيديو المنتج (YouTube/Vimeo)
  - منتج مميز

- **إدارة الصور**:
  - صور المنتج الأساسية
  - صور على الطبيعة (تاب منفصل)
  - رفع متعدد للصور
  - ترتيب وحذف الصور

- **الألوان والمقاسات**:
  - اختيار ألوان متعددة للمنتج
  - اختيار مقاسات متعددة
  - إدارة الألوان والمقاسات المتاحة

### 🎨 إدارة الألوان
- إضافة ألوان جديدة
- تحديد كود اللون (hex)
- تعديل وحذف الألوان
- معاينة الألوان

### 📏 إدارة المقاسات
- إضافة مقاسات جديدة
- ترتيب المقاسات
- تعديل وحذف المقاسات

### 🔍 البحث والفلترة
- البحث في المنتجات
- فلترة حسب التصنيف
- فلترة حسب حالة التوفر
- ترقيم الصفحات

## التثبيت والإعداد

### 1. متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

### 2. إعداد قاعدة البيانات
```bash
# تشغيل ملف إعداد قاعدة البيانات
http://localhost/your-project/fix_all_issues.php
```

### 3. إنشاء حساب المدير
```bash
# تشغيل ملف إنشاء المدير
http://localhost/your-project/admin/create_admin.php
```

### 4. بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

## هيكل الملفات

```
admin/
├── auth.php              # نظام المصادقة
├── login.php             # صفحة تسجيل الدخول
├── logout.php            # تسجيل الخروج
├── index.php             # لوحة التحكم الرئيسية
├── products.php          # إدارة المنتجات
├── add-product.php       # إضافة منتج جديد
├── edit-product.php      # تعديل منتج
├── colors.php            # إدارة الألوان
├── sizes.php             # إدارة المقاسات
├── create_admin.php      # إنشاء حساب مدير
└── README.md             # هذا الملف
```

## الاستخدام

### تسجيل الدخول
1. انتقل إلى `/admin/login.php`
2. أدخل بيانات تسجيل الدخول
3. ستتم إعادة توجيهك للوحة التحكم

### إضافة منتج جديد
1. انقر على "إضافة منتج جديد"
2. املأ جميع البيانات المطلوبة
3. اختر الألوان والمقاسات المتوفرة
4. احفظ المنتج

### إدارة الصور
- يمكن إضافة الصور بعد إنشاء المنتج
- استخدم صفحة التعديل لرفع الصور
- يمكن ترتيب الصور وحذفها

### إدارة الألوان والمقاسات
- أضف الألوان والمقاسات من الصفحات المخصصة
- ستظهر في نماذج إضافة المنتجات تلقائياً

## الأمان

### الحماية المطبقة
- تشفير كلمات المرور
- حماية من SQL Injection
- التحقق من الجلسات
- تنظيف البيانات المدخلة

### نصائح أمنية
- غيّر كلمة المرور الافتراضية
- احذف ملف `create_admin.php` بعد الإعداد
- استخدم HTTPS في الإنتاج
- قم بعمل نسخ احتياطية منتظمة

## التخصيص

### الألوان والتصميم
- يمكن تعديل ألوان التصميم من ملفات CSS
- التصميم متجاوب ويعمل على جميع الأجهزة

### إضافة ميزات جديدة
- يمكن إضافة صفحات جديدة باستخدام نفس البنية
- استخدم ملف `auth.php` للحماية
- اتبع نفس نمط التصميم

## الدعم الفني

### المشاكل الشائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تحقق من إعدادات `config/database.php`
2. **لا يمكن تسجيل الدخول**: تأكد من تشغيل `create_admin.php`
3. **الصور لا تظهر**: تحقق من صلاحيات مجلد `uploads`

### متطلبات الخادم
- تأكد من تفعيل `mod_rewrite` في Apache
- تأكد من صلاحيات الكتابة في مجلد `uploads`
- تأكد من تفعيل `GD extension` لمعالجة الصور

## الترقيات المستقبلية

### ميزات مخططة
- [ ] نظام رفع الصور المتقدم
- [ ] إدارة التصنيفات
- [ ] تقارير مفصلة
- [ ] إدارة الطلبات
- [ ] نظام الإشعارات
- [ ] إعدادات المتجر

---

**تم تطوير هذه اللوحة بعناية لتوفير تجربة إدارة احترافية تحاكي أفضل المنصات العالمية**
