/* Admin Panel Common Styles */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Main Content Layout */
.main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
}

@media (max-width: 768px) {
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.page-title h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.page-title p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.page-title i {
    margin-left: 0.5rem;
    font-size: 2.2rem;
}

/* Content Cards */
.content-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.card-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.3rem;
    font-weight: 600;
}

.card-header i {
    margin-left: 0.5rem;
    color: #6c757d;
}

.card-body {
    padding: 2rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
    color: #212529;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-2px);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.form-group label i {
    margin-left: 0.5rem;
    color: #6c757d;
    width: 20px;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #28a745;
}

/* Tables */
.table-responsive {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.table {
    margin: 0;
    width: 100%;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
}

.table td {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Mobile Toggle Button */
.mobile-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 10px;
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }
}

/* Bank Settings Specific Styles */
.bank-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-label {
    font-weight: 600;
    color: #495057;
    min-width: 100px;
}

.preview-value {
    font-family: 'Courier New', monospace;
    background: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
    flex: 1;
    margin-right: 1rem;
    word-break: break-all;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    flex-shrink: 0;
}

.stat-icon i {
    color: white;
    font-size: 1.5rem;
}

.stat-content h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #495057;
}

.stat-content span {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
    }

    .page-title h1 {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .preview-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .preview-value {
        margin-right: 0;
        width: 100%;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        margin-left: 0.75rem;
    }

    .stat-content h4 {
        font-size: 1.5rem;
    }
}
