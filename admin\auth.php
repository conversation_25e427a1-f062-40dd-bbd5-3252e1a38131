<?php
/**
 * ملف التحقق من صحة تسجيل الدخول
 * يجب تضمين هذا الملف في جميع صفحات لوحة التحكم
 */

session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
function checkAdminAuth() {
    if (!isset($_SESSION[ADMIN_SESSION_NAME]) || $_SESSION[ADMIN_SESSION_NAME] !== true) {
        header("Location: login.php");
        exit();
    }
    
    // التحقق من صحة بيانات الجلسة
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        session_destroy();
        header("Location: login.php");
        exit();
    }
    
    return true;
}

// الحصول على بيانات المدير الحالي
function getCurrentAdmin() {
    if (!checkAdminAuth()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['admin_id'],
        'username' => $_SESSION['admin_username'],
        'name' => $_SESSION['admin_name'] ?? '',
        'role' => $_SESSION['admin_role'] ?? 'admin'
    ];
}

// تسجيل الخروج
function adminLogout() {
    session_destroy();
    header("Location: login.php");
    exit();
}

// التحقق من الصلاحيات
function hasPermission($permission) {
    $admin = getCurrentAdmin();
    if (!$admin) return false;
    
    // المدير الرئيسي له جميع الصلاحيات
    if ($admin['role'] === 'admin') {
        return true;
    }
    
    // صلاحيات المدير المساعد
    $managerPermissions = [
        'view_products',
        'add_products', 
        'edit_products',
        'view_orders',
        'edit_orders'
    ];
    
    return in_array($permission, $managerPermissions);
}

// استدعاء التحقق من الجلسة
checkAdminAuth();
?>
