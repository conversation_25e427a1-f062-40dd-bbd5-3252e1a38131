<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $bank_name = $_POST['bank_name'] ?? '';
        $account_name = $_POST['account_name'] ?? '';
        $account_number = $_POST['account_number'] ?? '';
        $iban = $_POST['iban'] ?? '';
        $transfer_instructions = $_POST['transfer_instructions'] ?? '';
        
        // التحقق من وجود الإعدادات
        $check_sql = "SELECT COUNT(*) as count FROM site_settings WHERE setting_key = 'bank_name'";
        $stmt = $conn->prepare($check_sql);
        $stmt->execute();
        $exists = $stmt->fetch()['count'] > 0;
        
        $settings = [
            'bank_name' => $bank_name,
            'account_name' => $account_name,
            'account_number' => $account_number,
            'iban' => $iban,
            'transfer_instructions' => $transfer_instructions
        ];
        
        foreach ($settings as $key => $value) {
            if ($exists) {
                $sql = "UPDATE site_settings SET setting_value = ? WHERE setting_key = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$value, $key]);
            } else {
                $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$key, $value]);
            }
        }
        
        $success_message = 'تم حفظ إعدادات البنك بنجاح!';
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$settings_data = [];
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $sql = "SELECT setting_key, setting_value FROM site_settings WHERE setting_key IN ('bank_name', 'account_name', 'account_number', 'iban', 'transfer_instructions')";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings_data[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // استخدام القيم الافتراضية في حالة الخطأ
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات البنك - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/admin-styles.css" rel="stylesheet">
</head>

<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Include Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-title">
                <h1><i class="fas fa-university"></i> إعدادات البنك</h1>
                <p>إدارة بيانات الحساب البنكي للتحويلات</p>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Main Content Grid -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Bank Settings Form -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-edit"></i> تحديث بيانات البنك</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="bank-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="bank_name">
                                            <i class="fas fa-university"></i>
                                            اسم البنك
                                        </label>
                                        <input type="text" id="bank_name" name="bank_name" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($settings_data['bank_name'] ?? 'البنك الأهلي السعودي'); ?>" 
                                               required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="account_name">
                                            <i class="fas fa-user"></i>
                                            اسم صاحب الحساب
                                        </label>
                                        <input type="text" id="account_name" name="account_name" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($settings_data['account_name'] ?? 'فساتين السهرة'); ?>" 
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="account_number">
                                            <i class="fas fa-credit-card"></i>
                                            رقم الحساب
                                        </label>
                                        <input type="text" id="account_number" name="account_number" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($settings_data['account_number'] ?? '***************'); ?>" 
                                               required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="iban">
                                            <i class="fas fa-barcode"></i>
                                            رقم الآيبان (IBAN)
                                        </label>
                                        <input type="text" id="iban" name="iban" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($settings_data['iban'] ?? 'SA4980000***************'); ?>" 
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="transfer_instructions">
                                    <i class="fas fa-info-circle"></i>
                                    تعليمات التحويل
                                </label>
                                <textarea id="transfer_instructions" name="transfer_instructions" 
                                          class="form-control" rows="4" 
                                          placeholder="أدخل التعليمات التي ستظهر للعملاء عند اختيار التحويل البنكي"><?php echo htmlspecialchars($settings_data['transfer_instructions'] ?? 'يرجى إرسال إيصال التحويل بعد إتمام العملية'); ?></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Preview Card -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> معاينة البيانات</h3>
                    </div>
                    <div class="card-body">
                        <div class="bank-preview">
                            <div class="preview-item">
                                <span class="preview-label">البنك:</span>
                                <span class="preview-value" id="preview-bank"><?php echo htmlspecialchars($settings_data['bank_name'] ?? 'البنك الأهلي السعودي'); ?></span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">صاحب الحساب:</span>
                                <span class="preview-value" id="preview-account"><?php echo htmlspecialchars($settings_data['account_name'] ?? 'فساتين السهرة'); ?></span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">رقم الحساب:</span>
                                <span class="preview-value" id="preview-number"><?php echo htmlspecialchars($settings_data['account_number'] ?? '***************'); ?></span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">الآيبان:</span>
                                <span class="preview-value" id="preview-iban"><?php echo htmlspecialchars($settings_data['iban'] ?? 'SA4980000***************'); ?></span>
                            </div>
                            <div class="preview-item">
                                <span class="preview-label">التعليمات:</span>
                                <div class="preview-value" id="preview-instructions"><?php echo nl2br(htmlspecialchars($settings_data['transfer_instructions'] ?? 'يرجى إرسال إيصال التحويل بعد إتمام العملية')); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> إحصائيات</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            // عدد الطلبات بالتحويل البنكي
                            $bank_orders_sql = "SELECT COUNT(*) as count FROM orders WHERE payment_method = 'bank_transfer'";
                            $stmt = $conn->prepare($bank_orders_sql);
                            $stmt->execute();
                            $bank_orders_count = $stmt->fetch()['count'];

                            // عدد إيصالات التحويل
                            $receipts_sql = "SELECT COUNT(*) as count FROM transfer_receipts";
                            $stmt = $conn->prepare($receipts_sql);
                            $stmt->execute();
                            $receipts_count = $stmt->fetch()['count'];
                        } catch (Exception $e) {
                            $bank_orders_count = 0;
                            $receipts_count = 0;
                        }
                        ?>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-content">
                                    <h4><?php echo $bank_orders_count; ?></h4>
                                    <span>طلبات التحويل البنكي</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <div class="stat-content">
                                    <h4><?php echo $receipts_count; ?></h4>
                                    <span>إيصالات مرفوعة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Real-time preview updates
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = {
                'bank_name': 'preview-bank',
                'account_name': 'preview-account', 
                'account_number': 'preview-number',
                'iban': 'preview-iban',
                'transfer_instructions': 'preview-instructions'
            };

            Object.keys(inputs).forEach(inputId => {
                const input = document.getElementById(inputId);
                const preview = document.getElementById(inputs[inputId]);
                
                if (input && preview) {
                    input.addEventListener('input', function() {
                        if (inputId === 'transfer_instructions') {
                            preview.innerHTML = this.value.replace(/\n/g, '<br>') || 'غير محدد';
                        } else {
                            preview.textContent = this.value || 'غير محدد';
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
