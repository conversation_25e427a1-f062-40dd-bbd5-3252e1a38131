<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/SlugHelper.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة إضافة قسم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_category'])) {
    $name = trim($_POST['name'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $seo_title = trim($_POST['seo_title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $seo_content = trim($_POST['seo_content'] ?? '');
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    if (empty($name)) {
        $error_message = 'اسم القسم مطلوب';
    } elseif (empty($slug)) {
        $error_message = 'رابط القسم مطلوب';
    } else {
        // التأكد من فرادة الرابط
        $slug = SlugHelper::ensureUniqueSlug($slug, 'categories', 'slug');
        
        try {
            // معالجة رفع الصورة
            $image_path = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $image_path = uploadCategoryImage($_FILES['image']);
            }
            
            $stmt = $conn->prepare("INSERT INTO categories (name, slug, seo_title, description, seo_content, image, parent_id, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)");
            $stmt->execute([$name, $slug, $seo_title, $description, $seo_content, $image_path, $parent_id, $sort_order]);
            $success_message = 'تم إضافة القسم بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء إضافة القسم: ' . $e->getMessage();
        }
    }
}

// معالجة تحديث حالة القسم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_status'])) {
    $category_id = intval($_POST['category_id']);
    $new_status = intval($_POST['new_status']);
    
    try {
        $stmt = $conn->prepare("UPDATE categories SET is_active = ? WHERE id = ?");
        $stmt->execute([$new_status, $category_id]);
        $success_message = 'تم تحديث حالة القسم بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث حالة القسم';
    }
}

// معالجة حذف القسم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_category'])) {
    $category_id = intval($_POST['category_id']);
    
    try {
        // التحقق من وجود منتجات في هذا القسم
        $products_check = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
        $products_check->execute([$category_id]);
        $products_count = $products_check->fetch()['count'];
        
        // التحقق من وجود أقسام فرعية
        $subcategories_check = $conn->prepare("SELECT COUNT(*) as count FROM categories WHERE parent_id = ?");
        $subcategories_check->execute([$category_id]);
        $subcategories_count = $subcategories_check->fetch()['count'];
        
        if ($products_count > 0) {
            $error_message = 'لا يمكن حذف القسم لأنه يحتوي على ' . $products_count . ' منتج';
        } elseif ($subcategories_count > 0) {
            $error_message = 'لا يمكن حذف القسم لأنه يحتوي على ' . $subcategories_count . ' قسم فرعي';
        } else {
            // حذف صورة القسم إذا كانت موجودة
            $image_query = $conn->prepare("SELECT image FROM categories WHERE id = ?");
            $image_query->execute([$category_id]);
            $category = $image_query->fetch();
            
            if ($category && $category['image']) {
                $image_path = '../uploads/categories/' . $category['image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }
            
            $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $success_message = 'تم حذف القسم بنجاح';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف القسم';
    }
}

// جلب جميع الأقسام
try {
    $categories_query = "SELECT c.*, parent.name as parent_name, 
                        (SELECT COUNT(*) FROM categories sub WHERE sub.parent_id = c.id) as subcategories_count,
                        (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id) as products_count
                        FROM categories c 
                        LEFT JOIN categories parent ON c.parent_id = parent.id 
                        ORDER BY c.parent_id IS NULL DESC, c.sort_order ASC, c.name ASC";
    $categories_stmt = $conn->query($categories_query);
    $categories = $categories_stmt->fetchAll();
    
    // جلب الأقسام الرئيسية للقائمة المنسدلة
    $main_categories_query = "SELECT id, name FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY sort_order ASC, name ASC";
    $main_categories_stmt = $conn->query($main_categories_query);
    $main_categories = $main_categories_stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات";
    $categories = [];
    $main_categories = [];
}

// دالة رفع صورة القسم
function uploadCategoryImage($file) {
    $upload_dir = '../uploads/categories/';
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $max_file_size = 5 * 1024 * 1024; // 5MB
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        
        // التحقق من نوع الملف
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_types)) {
            return false;
        }
        
        // التحقق من حجم الملف
        if ($file_size > $max_file_size) {
            return false;
        }
        
        // إنشاء اسم ملف فريد
        $new_file_name = 'category_' . time() . '_' . rand(1000, 9999) . '.' . $file_ext;
        $file_path = $upload_dir . $new_file_name;
        
        // رفع الملف
        if (move_uploaded_file($file_tmp, $file_path)) {
            return $new_file_name;
        }
    }
    
    return false;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.5rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .nav-link i {
            margin-left: 1rem;
            width: 20px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 700;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #333;
        }
        
        .table td {
            border: none;
            vertical-align: middle;
        }
        
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        
        .category-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لافو</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link active">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-tags me-3"></i>إدارة الأقسام</h1>
            <p class="mb-0 text-muted">إدارة الأقسام الرئيسية والفرعية للمتجر</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- Add Category Form -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة قسم جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم القسم *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="slug" class="form-label">رابط القسم *</label>
                                <input type="text" class="form-control" id="slug" name="slug" required>
                                <div class="form-text">سيتم استخدامه في الرابط (مثال: evening-dresses)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="seo_title" class="form-label">عنوان SEO</label>
                                <input type="text" class="form-control" id="seo_title" name="seo_title" maxlength="200">
                                <div class="form-text">عنوان الصفحة في محركات البحث</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف القسم</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="seo_content" class="form-label">محتوى SEO</label>
                                <textarea class="form-control" id="seo_content" name="seo_content" rows="4"></textarea>
                                <div class="form-text">محتوى إضافي لتحسين SEO</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">القسم الرئيسي</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($main_categories as $main_cat): ?>
                                        <option value="<?php echo $main_cat['id']; ?>">
                                            <?php echo htmlspecialchars($main_cat['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                                <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">صورة القسم</label>
                                <div class="upload-area" onclick="document.getElementById('categoryImage').click()">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                    <p class="mb-1">انقر لاختيار صورة</p>
                                    <small class="text-muted">JPG, PNG, GIF, WEBP (حد أقصى 5MB)</small>
                                </div>
                                <input type="file" id="categoryImage" name="image" accept="image/*" style="display: none;">
                                <div id="imagePreview"></div>
                            </div>
                            
                            <button type="submit" name="add_category" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>إضافة القسم
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Categories List -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الأقسام</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم القسم</th>
                                        <th>الرابط</th>
                                        <th>القسم الرئيسي</th>
                                        <th>الأقسام الفرعية</th>
                                        <th>المنتجات</th>
                                        <th>الترتيب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($categories)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-4">
                                                <i class="fas fa-tags fa-3x mb-3"></i>
                                                <p>لا توجد أقسام بعد</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($categories as $category): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($category['image']): ?>
                                                        <img src="../uploads/categories/<?php echo htmlspecialchars($category['image']); ?>"
                                                             alt="<?php echo htmlspecialchars($category['name']); ?>"
                                                             class="category-image">
                                                    <?php else: ?>
                                                        <div class="category-image bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                                    <?php if ($category['seo_title']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($category['seo_title']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <code><?php echo htmlspecialchars($category['slug']); ?></code>
                                                </td>
                                                <td>
                                                    <?php if ($category['parent_name']): ?>
                                                        <span class="badge bg-info"><?php echo htmlspecialchars($category['parent_name']); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">قسم رئيسي</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $category['subcategories_count']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo $category['products_count']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark"><?php echo $category['sort_order']; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($category['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="edit-category.php?id=<?php echo $category['id']; ?>"
                                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>

                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                            <input type="hidden" name="new_status" value="<?php echo $category['is_active'] ? 0 : 1; ?>">
                                                            <button type="submit" name="toggle_status"
                                                                    class="btn btn-sm btn-outline-<?php echo $category['is_active'] ? 'warning' : 'success'; ?>"
                                                                    title="<?php echo $category['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                                <i class="fas fa-<?php echo $category['is_active'] ? 'eye-slash' : 'eye'; ?>"></i>
                                                            </button>
                                                        </form>

                                                        <?php if ($category['products_count'] == 0 && $category['subcategories_count'] == 0): ?>
                                                            <form method="POST" style="display: inline;"
                                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                                                <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                                <button type="submit" name="delete_category"
                                                                        class="btn btn-sm btn-outline-danger" title="حذف">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <button class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن الحذف - يحتوي على منتجات أو أقسام فرعية">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحويل اسم القسم إلى رابط تلقائياً
        document.getElementById('name').addEventListener('input', function() {
            const name = this.value;
            const slug = name.toLowerCase()
                .replace(/[أ-ي]/g, function(match) {
                    const arabicToEnglish = {
                        'أ': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
                        'د': 'd', 'ذ': 'th', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
                        'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
                        'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y'
                    };
                    return arabicToEnglish[match] || match;
                })
                .replace(/\s+/g, '-')
                .replace(/[^a-z0-9\-]/g, '');

            document.getElementById('slug').value = slug;
        });

        // معاينة الصورة
        document.getElementById('categoryImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('imagePreview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" class="image-preview" alt="معاينة الصورة">
                        <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeImage()">
                            <i class="fas fa-times"></i> إزالة الصورة
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '';
            }
        });

        function removeImage() {
            document.getElementById('categoryImage').value = '';
            document.getElementById('imagePreview').innerHTML = '';
        }
    </script>
</body>
</html>
