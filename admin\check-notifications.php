<?php
require_once 'auth.php';
require_once '../config/database.php';

// التأكد من أن الطلب AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    // إذا لم يكن طلب AJAX، تحقق من وجود المستخدم المسجل
    $admin = getCurrentAdmin();
}

$database = new Database();
$conn = $database->getConnection();

try {
    // عدد الطلبات الجديدة
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
    $new_orders = $stmt->fetch()['count'];
    
    // عدد الطلبات المؤكدة
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE status = 'confirmed'");
    $confirmed_orders = $stmt->fetch()['count'];
    
    // عدد الطلبات قيد التجهيز
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'");
    $processing_orders = $stmt->fetch()['count'];
    
    // عدد الطلبات المشحونة
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE status = 'shipped'");
    $shipped_orders = $stmt->fetch()['count'];
    
    // آخر طلب جديد
    $stmt = $conn->query("SELECT id, order_number, customer_name, total_amount, created_at 
                         FROM orders 
                         WHERE status = 'pending' 
                         ORDER BY created_at DESC 
                         LIMIT 1");
    $latest_order = $stmt->fetch();
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'new_orders' => (int)$new_orders,
        'confirmed_orders' => (int)$confirmed_orders,
        'processing_orders' => (int)$processing_orders,
        'shipped_orders' => (int)$shipped_orders,
        'latest_order' => $latest_order ? [
            'id' => $latest_order['id'],
            'order_number' => $latest_order['order_number'],
            'customer_name' => $latest_order['customer_name'],
            'total_amount' => $latest_order['total_amount'],
            'created_at' => $latest_order['created_at']
        ] : null,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'حدث خطأ أثناء جلب الإشعارات',
        'new_orders' => 0,
        'confirmed_orders' => 0,
        'processing_orders' => 0,
        'shipped_orders' => 0,
        'latest_order' => null,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

// إرسال الاستجابة كـ JSON
header('Content-Type: application/json');
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
