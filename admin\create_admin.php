<?php
/**
 * ملف إنشاء مستخدم مدير افتراضي
 * يجب تشغيل هذا الملف مرة واحدة فقط لإنشاء حساب المدير
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بيانات المدير الافتراضي
    $username = 'admin';
    $email = '<EMAIL>';
    $password = 'admin123'; // يجب تغييرها بعد أول تسجيل دخول
    $full_name = 'مدير النظام';
    $role = 'admin';
    
    // تشفير كلمة المرور
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // التحقق من عدم وجود المستخدم مسبقاً
    $check_stmt = $conn->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
    $check_stmt->execute([$username, $email]);
    
    if ($check_stmt->rowCount() > 0) {
        echo "<div style='color: orange; padding: 20px; font-family: Arial;'>";
        echo "<h3>تحذير!</h3>";
        echo "<p>المستخدم موجود بالفعل. لا حاجة لإنشاء حساب جديد.</p>";
        echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
        echo "<p>اسم المستخدم: <strong>admin</strong></p>";
        echo "<p>كلمة المرور: <strong>admin123</strong></p>";
        echo "<p><a href='login.php'>انتقل لصفحة تسجيل الدخول</a></p>";
        echo "</div>";
    } else {
        // إنشاء المستخدم
        $insert_stmt = $conn->prepare("INSERT INTO admin_users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
        $insert_stmt->execute([$username, $email, $hashed_password, $full_name, $role]);
        
        echo "<div style='color: green; padding: 20px; font-family: Arial;'>";
        echo "<h3>تم إنشاء حساب المدير بنجاح!</h3>";
        echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
        echo "<p>اسم المستخدم: <strong>admin</strong></p>";
        echo "<p>كلمة المرور: <strong>admin123</strong></p>";
        echo "<p style='color: red;'><strong>مهم:</strong> يرجى تغيير كلمة المرور بعد أول تسجيل دخول!</p>";
        echo "<p><a href='login.php'>انتقل لصفحة تسجيل الدخول</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إنشاء حساب المدير: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب المدير</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعداد لوحة التحكم</h1>
        
        <div class="info">
            <h3>معلومات مهمة:</h3>
            <ul>
                <li>هذا الملف ينشئ حساب مدير افتراضي للوصول إلى لوحة التحكم</li>
                <li>يجب تشغيل هذا الملف مرة واحدة فقط</li>
                <li>بعد إنشاء الحساب، يمكنك حذف هذا الملف لأسباب أمنية</li>
            </ul>
        </div>
        
        <div class="warning">
            <strong>تحذير أمني:</strong> تأكد من تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <p><a href="../index.php">العودة للموقع الرئيسي</a></p>
        </div>
    </div>
</body>
</html>
