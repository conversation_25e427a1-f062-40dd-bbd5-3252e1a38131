<?php
require_once 'auth.php';
require_once '../config/database.php';

header('Content-Type: application/json');

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'طريقة الطلب غير صحيحة';
    echo json_encode($response);
    exit;
}

$image_id = intval($_POST['image_id'] ?? 0);

if ($image_id <= 0) {
    $response['message'] = 'معرف الصورة غير صحيح';
    echo json_encode($response);
    exit;
}

try {
    // جلب معلومات الصورة
    $stmt = $conn->prepare("SELECT pi.*, p.id as product_id FROM product_images pi 
                           JOIN products p ON pi.product_id = p.id 
                           WHERE pi.id = ?");
    $stmt->execute([$image_id]);
    $image = $stmt->fetch();
    
    if (!$image) {
        $response['message'] = 'الصورة غير موجودة';
        echo json_encode($response);
        exit;
    }
    
    // حذف الملف من الخادم
    $file_path = '../uploads/' . $image['image_path'];
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    
    // حذف السجل من قاعدة البيانات
    $delete_stmt = $conn->prepare("DELETE FROM product_images WHERE id = ?");
    $delete_stmt->execute([$image_id]);
    
    // إذا كانت هذه الصورة المميزة، تحديث المنتج
    $product_stmt = $conn->prepare("SELECT featured_image FROM products WHERE id = ?");
    $product_stmt->execute([$image['product_id']]);
    $product = $product_stmt->fetch();
    
    if ($product && $product['featured_image'] === $image['image_path']) {
        // البحث عن صورة أخرى لتكون مميزة
        $new_featured_stmt = $conn->prepare("SELECT image_path FROM product_images WHERE product_id = ? AND image_type = 'gallery' ORDER BY sort_order LIMIT 1");
        $new_featured_stmt->execute([$image['product_id']]);
        $new_featured = $new_featured_stmt->fetch();
        
        $new_featured_image = $new_featured ? $new_featured['image_path'] : null;
        
        $update_stmt = $conn->prepare("UPDATE products SET featured_image = ? WHERE id = ?");
        $update_stmt->execute([$new_featured_image, $image['product_id']]);
    }
    
    $response['success'] = true;
    $response['message'] = 'تم حذف الصورة بنجاح';
    
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ أثناء حذف الصورة: ' . $e->getMessage();
}

echo json_encode($response);
?>
