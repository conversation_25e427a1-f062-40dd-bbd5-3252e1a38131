<?php
require_once 'auth.php';
require_once '../config/database.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $video_type = isset($_POST['video_type']) ? $_POST['video_type'] : '';
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    
    if ($product_id > 0 && in_array($video_type, ['file', 'url'])) {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            if ($video_type === 'file') {
                // حذف ملف الفيديو
                
                // جلب مسار الملف أولاً
                $video_query = "SELECT video_file FROM products WHERE id = ?";
                $video_stmt = $conn->prepare($video_query);
                $video_stmt->execute([$product_id]);
                $product = $video_stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($product && !empty($product['video_file'])) {
                    // حذف الملف من الخادم
                    $file_path = '../uploads/' . $product['video_file'];
                    if (file_exists($file_path)) {
                        unlink($file_path);
                    }
                    
                    // تحديث قاعدة البيانات
                    $update_query = "UPDATE products SET video_file = NULL WHERE id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    
                    if ($update_stmt->execute([$product_id])) {
                        $response['success'] = true;
                        $response['message'] = 'تم حذف ملف الفيديو بنجاح';
                    } else {
                        $response['message'] = 'فشل في تحديث قاعدة البيانات';
                    }
                } else {
                    $response['message'] = 'لا يوجد ملف فيديو لحذفه';
                }
                
            } elseif ($video_type === 'url') {
                // حذف رابط الفيديو
                $update_query = "UPDATE products SET video_url = NULL WHERE id = ?";
                $update_stmt = $conn->prepare($update_query);
                
                if ($update_stmt->execute([$product_id])) {
                    $response['success'] = true;
                    $response['message'] = 'تم حذف رابط الفيديو بنجاح';
                } else {
                    $response['message'] = 'فشل في تحديث قاعدة البيانات';
                }
            }
            
        } catch (Exception $e) {
            $response['message'] = 'حدث خطأ أثناء حذف الفيديو: ' . $e->getMessage();
        }
    } else {
        $response['message'] = 'معرف المنتج أو نوع الفيديو غير صحيح';
    }
} else {
    $response['message'] = 'طريقة الطلب غير صحيحة';
}

echo json_encode($response);
?>
