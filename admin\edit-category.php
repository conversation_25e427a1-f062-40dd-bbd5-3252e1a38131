<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/SlugHelper.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';
$category_id = intval($_GET['id'] ?? 0);

if ($category_id <= 0) {
    header("Location: categories.php");
    exit();
}

// جلب بيانات القسم
try {
    $stmt = $conn->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        header("Location: categories.php");
        exit();
    }
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب بيانات القسم";
}

// معالجة تحديث القسم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_category'])) {
    $name = trim($_POST['name'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $seo_title = trim($_POST['seo_title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $seo_content = trim($_POST['seo_content'] ?? '');
    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (empty($name)) {
        $error_message = 'اسم القسم مطلوب';
    } elseif (empty($slug)) {
        $error_message = 'رابط القسم مطلوب';
    } else {
        // التأكد من فرادة الرابط (باستثناء القسم الحالي)
        $slug = SlugHelper::ensureUniqueSlug($slug, 'categories', 'slug', $category_id);
        
        try {
            // معالجة رفع الصورة الجديدة
            $image_path = $category['image']; // الاحتفاظ بالصورة الحالية
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $new_image = uploadCategoryImage($_FILES['image']);
                if ($new_image) {
                    // حذف الصورة القديمة
                    if ($category['image']) {
                        $old_image_path = '../uploads/categories/' . $category['image'];
                        if (file_exists($old_image_path)) {
                            unlink($old_image_path);
                        }
                    }
                    $image_path = $new_image;
                }
            }
            
            $stmt = $conn->prepare("UPDATE categories SET name = ?, slug = ?, seo_title = ?, description = ?, seo_content = ?, image = ?, parent_id = ?, sort_order = ?, is_active = ? WHERE id = ?");
            $stmt->execute([$name, $slug, $seo_title, $description, $seo_content, $image_path, $parent_id, $sort_order, $is_active, $category_id]);
            
            // تحديث البيانات المعروضة
            $category['name'] = $name;
            $category['slug'] = $slug;
            $category['seo_title'] = $seo_title;
            $category['description'] = $description;
            $category['seo_content'] = $seo_content;
            $category['image'] = $image_path;
            $category['parent_id'] = $parent_id;
            $category['sort_order'] = $sort_order;
            $category['is_active'] = $is_active;
            
            $success_message = 'تم تحديث القسم بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث القسم: ' . $e->getMessage();
        }
    }
}

// معالجة حذف الصورة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_image'])) {
    try {
        if ($category['image']) {
            $image_path = '../uploads/categories/' . $category['image'];
            if (file_exists($image_path)) {
                unlink($image_path);
            }
            
            $stmt = $conn->prepare("UPDATE categories SET image = NULL WHERE id = ?");
            $stmt->execute([$category_id]);
            
            $category['image'] = null;
            $success_message = 'تم حذف الصورة بنجاح';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حذف الصورة';
    }
}

// جلب الأقسام الرئيسية للقائمة المنسدلة (باستثناء القسم الحالي وأقسامه الفرعية)
try {
    $main_categories_query = "SELECT id, name FROM categories WHERE parent_id IS NULL AND is_active = 1 AND id != ? ORDER BY sort_order ASC, name ASC";
    $main_categories_stmt = $conn->prepare($main_categories_query);
    $main_categories_stmt->execute([$category_id]);
    $main_categories = $main_categories_stmt->fetchAll();
} catch (Exception $e) {
    $main_categories = [];
}

// دالة رفع صورة القسم
function uploadCategoryImage($file) {
    $upload_dir = '../uploads/categories/';
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $max_file_size = 5 * 1024 * 1024; // 5MB
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        
        // التحقق من نوع الملف
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_types)) {
            return false;
        }
        
        // التحقق من حجم الملف
        if ($file_size > $max_file_size) {
            return false;
        }
        
        // إنشاء اسم ملف فريد
        $new_file_name = 'category_' . time() . '_' . rand(1000, 9999) . '.' . $file_ext;
        $file_path = $upload_dir . $new_file_name;
        
        // رفع الملف
        if (move_uploaded_file($file_tmp, $file_path)) {
            return $new_file_name;
        }
    }
    
    return false;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل القسم - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.5rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .nav-link i {
            margin-left: 1rem;
            width: 20px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 700;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .current-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لافو</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link active">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-edit me-3"></i>تعديل القسم</h1>
                    <p class="mb-0 text-muted">تعديل بيانات القسم: <?php echo htmlspecialchars($category['name']); ?></p>
                </div>
                <a href="categories.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Edit Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل بيانات القسم</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم القسم *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($category['name']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="slug" class="form-label">رابط القسم *</label>
                                <input type="text" class="form-control" id="slug" name="slug" 
                                       value="<?php echo htmlspecialchars($category['slug']); ?>" required>
                                <div class="form-text">سيتم استخدامه في الرابط (مثال: evening-dresses)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="seo_title" class="form-label">عنوان SEO</label>
                        <input type="text" class="form-control" id="seo_title" name="seo_title" 
                               value="<?php echo htmlspecialchars($category['seo_title']); ?>" maxlength="200">
                        <div class="form-text">عنوان الصفحة في محركات البحث</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القسم</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($category['description']); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="seo_content" class="form-label">محتوى SEO</label>
                        <textarea class="form-control" id="seo_content" name="seo_content" rows="4"><?php echo htmlspecialchars($category['seo_content']); ?></textarea>
                        <div class="form-text">محتوى إضافي لتحسين SEO</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">القسم الرئيسي</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($main_categories as $main_cat): ?>
                                        <option value="<?php echo $main_cat['id']; ?>" 
                                                <?php echo $category['parent_id'] == $main_cat['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($main_cat['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?php echo $category['sort_order']; ?>" min="0">
                                <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">صورة القسم</label>
                        
                        <?php if ($category['image']): ?>
                            <div class="current-image-section mb-3">
                                <p class="text-muted mb-2">الصورة الحالية:</p>
                                <img src="../uploads/categories/<?php echo htmlspecialchars($category['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($category['name']); ?>" 
                                     class="current-image">
                                <br>
                                <form method="POST" style="display: inline;" 
                                      onsubmit="return confirm('هل أنت متأكد من حذف الصورة؟')">
                                    <button type="submit" name="delete_image" class="btn btn-sm btn-outline-danger mt-2">
                                        <i class="fas fa-trash me-1"></i>حذف الصورة الحالية
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                        
                        <div class="upload-area" onclick="document.getElementById('categoryImage').click()">
                            <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                            <p class="mb-1">انقر لاختيار صورة جديدة</p>
                            <small class="text-muted">JPG, PNG, GIF, WEBP (حد أقصى 5MB)</small>
                        </div>
                        <input type="file" id="categoryImage" name="image" accept="image/*" style="display: none;">
                        <div id="imagePreview"></div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   <?php echo $category['is_active'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                القسم نشط
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-3">
                        <button type="submit" name="update_category" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="categories.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحويل اسم القسم إلى رابط تلقائياً
        document.getElementById('name').addEventListener('input', function() {
            const name = this.value;
            const slug = name.toLowerCase()
                .replace(/[أ-ي]/g, function(match) {
                    const arabicToEnglish = {
                        'أ': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
                        'د': 'd', 'ذ': 'th', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
                        'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
                        'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y'
                    };
                    return arabicToEnglish[match] || match;
                })
                .replace(/\s+/g, '-')
                .replace(/[^a-z0-9\-]/g, '');
            
            document.getElementById('slug').value = slug;
        });

        // معاينة الصورة الجديدة
        document.getElementById('categoryImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('imagePreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <p class="text-muted mb-2 mt-3">معاينة الصورة الجديدة:</p>
                        <img src="${e.target.result}" class="image-preview" alt="معاينة الصورة">
                        <button type="button" class="btn btn-sm btn-danger mt-2 d-block" onclick="removeImage()">
                            <i class="fas fa-times"></i> إزالة الصورة الجديدة
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '';
            }
        });

        function removeImage() {
            document.getElementById('categoryImage').value = '';
            document.getElementById('imagePreview').innerHTML = '';
        }
    </script>
</body>
</html>
