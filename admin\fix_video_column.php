<?php
/**
 * ملف إصلاح عمود الفيديو في جدول المنتجات
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div style='font-family: Arial; padding: 20px; max-width: 800px; margin: 0 auto;'>";
    echo "<h2>إصلاح عمود الفيديو في جدول المنتجات</h2>";
    
    // التحقق من وجود العمود أولاً
    $check_column = "SHOW COLUMNS FROM products LIKE 'video_file'";
    $result = $conn->query($check_column);
    
    if ($result->rowCount() == 0) {
        // العمود غير موجود، سنقوم بإضافته
        echo "<div style='color: orange;'>⚠️ عمود video_file غير موجود، جاري إضافته...</div>";
        
        $add_column = "ALTER TABLE products ADD COLUMN video_file VARCHAR(255) DEFAULT NULL AFTER video_url";
        
        try {
            $conn->exec($add_column);
            echo "<div style='color: green;'>✅ تم إضافة عمود video_file بنجاح!</div>";
        } catch (Exception $e) {
            echo "<div style='color: red;'>❌ خطأ في إضافة العمود: " . $e->getMessage() . "</div>";
            throw $e;
        }
    } else {
        echo "<div style='color: blue;'>ℹ️ عمود video_file موجود بالفعل</div>";
    }
    
    // عرض هيكل الجدول الحالي
    echo "<h3>هيكل جدول المنتجات الحالي:</h3>";
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>اسم العمود</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>النوع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>Null</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>Default</th>";
    echo "</tr>";
    
    $columns = $conn->query("SHOW COLUMNS FROM products");
    while ($column = $columns->fetch()) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // إنشاء مجلد رفع الفيديو إذا لم يكن موجوداً
    $video_dir = '../uploads/videos/';
    if (!file_exists($video_dir)) {
        if (mkdir($video_dir, 0755, true)) {
            echo "<div style='color: green;'>✅ تم إنشاء مجلد رفع الفيديو: $video_dir</div>";
        } else {
            echo "<div style='color: red;'>❌ فشل في إنشاء مجلد رفع الفيديو</div>";
        }
    } else {
        echo "<div style='color: blue;'>ℹ️ مجلد رفع الفيديو موجود بالفعل: $video_dir</div>";
    }
    
    // إنشاء ملف .htaccess لحماية مجلد الفيديو
    $htaccess_content = "# منع تنفيذ ملفات PHP في مجلد الفيديو
<Files *.php>
    Order Deny,Allow
    Deny from all
</Files>

# السماح بأنواع ملفات الفيديو فقط
<FilesMatch \"\\.(mp4|avi|mov|wmv|flv|webm|mkv)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع الوصول المباشر للملفات غير المسموحة
<FilesMatch \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">
    Order Deny,Allow
    Deny from all
</FilesMatch>";
    
    $htaccess_file = $video_dir . '.htaccess';
    if (!file_exists($htaccess_file)) {
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            echo "<div style='color: green;'>✅ تم إنشاء ملف .htaccess لحماية مجلد الفيديو</div>";
        } else {
            echo "<div style='color: orange;'>⚠️ تعذر إنشاء ملف .htaccess</div>";
        }
    } else {
        echo "<div style='color: blue;'>ℹ️ ملف .htaccess موجود بالفعل</div>";
    }
    
    // اختبار إدراج بيانات تجريبية
    echo "<h3>اختبار النظام:</h3>";
    try {
        $test_query = "SELECT id, title, video_url, video_file FROM products LIMIT 1";
        $test_result = $conn->query($test_query);
        echo "<div style='color: green;'>✅ تم اختبار الاستعلام بنجاح - النظام يعمل!</div>";
        
        if ($test_result->rowCount() > 0) {
            $test_product = $test_result->fetch();
            echo "<div style='background: #f0f9ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>مثال على منتج:</strong><br>";
            echo "ID: {$test_product['id']}<br>";
            echo "العنوان: {$test_product['title']}<br>";
            echo "رابط الفيديو: " . ($test_product['video_url'] ?: 'غير محدد') . "<br>";
            echo "ملف الفيديو: " . ($test_product['video_file'] ?: 'غير محدد') . "<br>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في اختبار النظام: " . $e->getMessage() . "</div>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #2d5a2d;'>✅ تم إصلاح المشكلة بنجاح!</h3>";
    echo "<ul style='color: #2d5a2d;'>";
    echo "<li>✅ عمود video_file متوفر في جدول المنتجات</li>";
    echo "<li>✅ مجلد رفع الفيديو جاهز</li>";
    echo "<li>✅ ملفات الحماية في مكانها</li>";
    echo "<li>✅ النظام جاهز للاستخدام</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #0c4a6e;'>الخطوات التالية:</h3>";
    echo "<ol style='color: #0c4a6e;'>";
    echo "<li>انتقل إلى صفحة إضافة المنتج</li>";
    echo "<li>جرب إضافة منتج جديد مع فيديو</li>";
    echo "<li>تأكد من عمل النظام بشكل صحيح</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إصلاح عمود الفيديو: " . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح عمود الفيديو</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            text-decoration: none;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
        .nav-links a.success {
            background: #28a745;
        }
        .nav-links a.success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح عمود الفيديو</h1>
        
        <div class="nav-links">
            <h3>جرب النظام الآن:</h3>
            <a href="add-product.php" class="success">إضافة منتج جديد</a>
            <a href="products.php">إدارة المنتجات</a>
            <a href="../test_image_video_features.html">اختبار الميزات</a>
            <a href="../index.php">الموقع الرئيسي</a>
        </div>
    </div>
</body>
</html>
