<?php
require_once 'auth.php';
require_once '../includes/SlugHelper.php';

header('Content-Type: application/json');

$response = ['success' => false, 'slug' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['title'])) {
    $title = trim($_POST['title']);
    $excludeId = isset($_POST['exclude_id']) ? intval($_POST['exclude_id']) : null;
    
    if (!empty($title)) {
        $slug = SlugHelper::createProductSlug($title, $excludeId);
        $response['success'] = true;
        $response['slug'] = $slug;
    } else {
        $response['message'] = 'العنوان مطلوب';
    }
} else {
    $response['message'] = 'طلب غير صحيح';
}

echo json_encode($response);
?>
