<?php
// التحقق من الجلسة
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Sidebar -->
<div class="sidebar" id="adminSidebar">
    <div class="logo">
        <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
    </div>
    
    <nav class="sidebar-nav">
        <div class="nav-item">
            <a href="index.php" class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>">
                <i class="fas fa-home"></i>
                الرئيسية
            </a>
        </div>
        
        <div class="nav-item">
            <a href="orders.php" class="nav-link <?php echo ($current_page == 'orders.php') ? 'active' : ''; ?>">
                <i class="fas fa-shopping-cart"></i>
                إدارة الطلبات
            </a>
        </div>
        
        <div class="nav-item">
            <a href="products.php" class="nav-link <?php echo ($current_page == 'products.php') ? 'active' : ''; ?>">
                <i class="fas fa-tshirt"></i>
                إدارة المنتجات
            </a>
        </div>
        
        <div class="nav-item">
            <a href="add-product.php" class="nav-link <?php echo ($current_page == 'add-product.php') ? 'active' : ''; ?>">
                <i class="fas fa-plus"></i>
                إضافة منتج جديد
            </a>
        </div>
        
        <div class="nav-item">
            <a href="categories.php" class="nav-link <?php echo ($current_page == 'categories.php') ? 'active' : ''; ?>">
                <i class="fas fa-tags"></i>
                إدارة الفئات
            </a>
        </div>
        
        <div class="nav-item">
            <a href="colors.php" class="nav-link <?php echo ($current_page == 'colors.php') ? 'active' : ''; ?>">
                <i class="fas fa-palette"></i>
                إدارة الألوان
            </a>
        </div>
        
        <div class="nav-item">
            <a href="sizes.php" class="nav-link <?php echo ($current_page == 'sizes.php') ? 'active' : ''; ?>">
                <i class="fas fa-ruler"></i>
                إدارة المقاسات
            </a>
        </div>
        
        <div class="nav-item">
            <a href="settings.php" class="nav-link <?php echo ($current_page == 'settings.php') ? 'active' : ''; ?>">
                <i class="fas fa-cog"></i>
                إعدادات الموقع
            </a>
        </div>
        
        <div class="nav-item">
            <a href="bank_settings.php" class="nav-link <?php echo ($current_page == 'bank_settings.php') ? 'active' : ''; ?>">
                <i class="fas fa-university"></i>
                إعدادات البنك
            </a>
        </div>
        
        <div class="nav-item">
            <a href="logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </a>
        </div>
    </nav>
</div>

<style>
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar .logo {
    padding: 2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar .logo h3 {
    color: white;
    margin: 0;
    font-weight: 600;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-item {
    margin: 0.25rem 1rem;
}

.sidebar-nav .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    background: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 0.75rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}
</style>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('show');
}
</script>
