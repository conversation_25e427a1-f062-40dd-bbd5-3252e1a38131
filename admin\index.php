<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

// إحصائيات سريعة
try {
    // عدد المنتجات
    $stmt = $conn->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
    $products_count = $stmt->fetch()['count'];
    
    // عدد الطلبات
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders");
    $orders_count = $stmt->fetch()['count'];
    
    // عدد الطلبات الجديدة
    $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
    $new_orders_count = $stmt->fetch()['count'];
    
    // إجمالي المبيعات
    $stmt = $conn->query("SELECT SUM(total_amount) as total FROM orders WHERE status IN ('completed', 'shipped')");
    $total_sales = $stmt->fetch()['total'] ?? 0;
    
} catch (Exception $e) {
    $products_count = 0;
    $orders_count = 0;
    $new_orders_count = 0;
    $total_sales = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - متجر فساتين السهرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../css/admin.css" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar .logo p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }

        .notification-badge {
            background: #ff4757;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            margin-right: 0.5rem;
            min-width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .nav-link-with-badge {
            justify-content: space-between;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .top-bar {
            background: white;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .welcome-text h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        
        .welcome-text p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .admin-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-card.products .stat-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-card.orders .stat-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.new-orders .stat-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.sales .stat-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .stat-card h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: #333;
        }
        
        .stat-card p {
            margin: 0;
            color: #666;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
            <p>متجر فساتين السهرة</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link active">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="orders.php" class="nav-link nav-link-with-badge">
                    <span>
                        <i class="fas fa-shopping-cart"></i>
                        الطلبات
                    </span>
                    <?php if ($new_orders_count > 0): ?>
                        <span class="notification-badge"><?php echo $new_orders_count; ?></span>
                    <?php endif; ?>
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="bank_settings.php" class="nav-link">
                    <i class="fas fa-university"></i>
                    إعدادات البنك
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="welcome-text">
                <h4>مرحباً بك، <?php echo htmlspecialchars($admin['name']); ?></h4>
                <p>إليك نظرة سريعة على أداء متجرك</p>
            </div>
            <div class="admin-info">
                <div class="admin-avatar">
                    <?php echo strtoupper(substr($admin['name'], 0, 1)); ?>
                </div>
                <span><?php echo htmlspecialchars($admin['name']); ?></span>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card products">
                <div class="stat-icon">
                    <i class="fas fa-tshirt"></i>
                </div>
                <h3><?php echo number_format($products_count); ?></h3>
                <p>إجمالي المنتجات</p>
            </div>
            
            <div class="stat-card orders">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3><?php echo number_format($orders_count); ?></h3>
                <p>إجمالي الطلبات</p>
            </div>
            
            <div class="stat-card new-orders">
                <div class="stat-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3><?php echo number_format($new_orders_count); ?></h3>
                <p>طلبات جديدة</p>
            </div>
            
            <div class="stat-card sales">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3><?php echo number_format($total_sales, 2); ?> ر.س</h3>
                <p>إجمالي المبيعات</p>
            </div>
        </div>

        <!-- Recent Orders and Notifications -->
        <?php if ($new_orders_count > 0): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            طلبات جديدة تحتاج للمراجعة (<?php echo $new_orders_count; ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // جلب آخر 5 طلبات جديدة
                        $recent_orders_query = "SELECT o.id, o.order_number, o.customer_name, o.customer_phone,
                                                      o.total_amount, o.created_at,
                                                      c.name as city_name, a.name as area_name
                                               FROM orders o
                                               LEFT JOIN cities c ON o.city_id = c.id
                                               LEFT JOIN areas a ON o.area_id = a.id
                                               WHERE o.status = 'pending'
                                               ORDER BY o.created_at DESC
                                               LIMIT 5";

                        $recent_orders_stmt = $conn->query($recent_orders_query);
                        $recent_orders = $recent_orders_stmt->fetchAll();
                        ?>

                        <?php if (!empty($recent_orders)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>العميل</th>
                                            <th>المدينة</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <strong>#<?php echo htmlspecialchars($order['order_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($order['customer_name']); ?><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($order['city_name'] . ' - ' . $order['area_name']); ?></td>
                                                <td>
                                                    <span class="fw-bold text-success">
                                                        <?php echo number_format($order['total_amount'], 2); ?> ر.س
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>
                                                </td>
                                                <td>
                                                    <a href="order-details.php?id=<?php echo $order['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary me-1">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <a href="update-order-status.php?id=<?php echo $order['id']; ?>"
                                                       class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-check"></i> تأكيد
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="text-center mt-3">
                                <a href="orders.php?status=pending" class="btn btn-primary">
                                    <i class="fas fa-list me-1"></i>عرض جميع الطلبات الجديدة
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-plus fa-2x text-primary mb-3"></i>
                        <h6>إضافة منتج جديد</h6>
                        <a href="add-product.php" class="btn btn-primary btn-sm">إضافة</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-shopping-cart fa-2x text-success mb-3"></i>
                        <h6>إدارة الطلبات</h6>
                        <a href="orders.php" class="btn btn-success btn-sm">عرض</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-tshirt fa-2x text-info mb-3"></i>
                        <h6>إدارة المنتجات</h6>
                        <a href="products.php" class="btn btn-info btn-sm">عرض</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-cog fa-2x text-secondary mb-3"></i>
                        <h6>الإعدادات</h6>
                        <a href="settings.php" class="btn btn-secondary btn-sm">تعديل</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الإشعارات كل 30 ثانية
        setInterval(function() {
            fetch('check-notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.new_orders > 0) {
                        const badge = document.querySelector('.notification-badge');
                        if (badge) {
                            badge.textContent = data.new_orders;
                        }

                        // إظهار إشعار في المتصفح
                        if (Notification.permission === 'granted') {
                            new Notification('طلب جديد!', {
                                body: `لديك ${data.new_orders} طلب جديد في انتظار المراجعة`,
                                icon: '../images/logo.png'
                            });
                        }
                    }
                })
                .catch(error => console.log('Error checking notifications:', error));
        }, 30000);

        // طلب إذن الإشعارات
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    </script>
</body>
</html>
