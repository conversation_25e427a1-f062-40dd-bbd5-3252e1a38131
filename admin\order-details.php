<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: orders.php');
    exit();
}

$order_id = (int)$_GET['id'];

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes'] ?? '');
    
    try {
        $stmt = $conn->prepare("UPDATE orders SET status = ?, notes = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_status, $notes, $order_id]);
        $success_message = 'تم تحديث حالة الطلب بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث حالة الطلب';
    }
}

// جلب تفاصيل الطلب
$order_query = "SELECT o.*, 
                       c.name as city_name, 
                       a.name as area_name,
                       co.name as country_name
                FROM orders o 
                LEFT JOIN cities c ON o.city_id = c.id 
                LEFT JOIN areas a ON o.area_id = a.id 
                LEFT JOIN countries co ON c.country_id = co.id
                WHERE o.id = ?";

$order_stmt = $conn->prepare($order_query);
$order_stmt->execute([$order_id]);
$order = $order_stmt->fetch();

if (!$order) {
    header('Location: orders.php');
    exit();
}

// جلب عناصر الطلب
$items_query = "SELECT oi.*, 
                       p.title as product_title,
                       p.featured_image,
                       c.name as color_name,
                       s.name as size_name
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                LEFT JOIN colors c ON oi.color_id = c.id
                LEFT JOIN sizes s ON oi.size_id = s.id
                WHERE oi.order_id = ?
                ORDER BY oi.id";

$items_stmt = $conn->prepare($items_query);
$items_stmt->execute([$order_id]);
$order_items = $items_stmt->fetchAll();

// دالة لترجمة حالة الطلب
function getStatusText($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التجهيز',
        'shipped' => 'تم الشحن',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي'
    ];
    return $statuses[$status] ?? $status;
}

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'confirmed' => 'info',
        'processing' => 'primary',
        'shipped' => 'success',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

// دالة لترجمة طريقة الدفع
function getPaymentMethodText($method) {
    $methods = [
        'cod' => 'الدفع عند الاستلام',
        'bank_transfer' => 'تحويل بنكي',
        'online' => 'دفع إلكتروني'
    ];
    return $methods[$method] ?? $method;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #<?php echo htmlspecialchars($order['order_number']); ?> - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar .logo p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-card h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .product-item {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: box-shadow 0.3s ease;
        }
        
        .product-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .btn-action {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            border-radius: 8px;
            margin: 0 0.25rem;
        }
        
        .total-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
        }
        
        .total-row.final {
            border-top: 2px solid rgba(255,255,255,0.3);
            margin-top: 1rem;
            padding-top: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .product-details {
            margin-top: 0.5rem;
        }

        .product-details .badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
            <p>متجر فساتين السهرة</p>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="orders.php" class="nav-link active">
                    <i class="fas fa-shopping-cart"></i>
                    الطلبات
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="bank_settings.php" class="nav-link">
                    <i class="fas fa-university"></i>
                    إعدادات البنك
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <h1>
                    <i class="fas fa-file-invoice"></i>
                    تفاصيل الطلب #<?php echo htmlspecialchars($order['order_number']); ?>
                </h1>
                <div>
                    <a href="orders.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-1"></i>العودة للطلبات
                    </a>
                    <a href="print-order.php?id=<?php echo $order['id']; ?>" target="_blank" class="btn btn-primary">
                        <i class="fas fa-print me-1"></i>طباعة
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Order Information -->
            <div class="col-md-8">
                <!-- Order Status -->
                <div class="info-card">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات الطلب</h5>
                    <div class="info-row">
                        <span class="info-label">رقم الطلب:</span>
                        <span class="info-value">#<?php echo htmlspecialchars($order['order_number']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الحالة:</span>
                        <span class="badge bg-<?php echo getStatusColor($order['status']); ?> status-badge">
                            <?php echo getStatusText($order['status']); ?>
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تاريخ الطلب:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">آخر تحديث:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($order['updated_at'])); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">طريقة الدفع:</span>
                        <span class="info-value"><?php echo getPaymentMethodText($order['payment_method']); ?></span>
                    </div>

                    <?php if ($order['payment_method'] === 'bank_transfer'): ?>
                    <!-- Bank Transfer Details -->
                    <div class="bank-transfer-section">
                        <h5><i class="fas fa-university me-2"></i>تفاصيل التحويل البنكي</h5>

                        <?php
                        // جلب بيانات البنك
                        try {
                            $bank_sql = "SELECT setting_key, setting_value FROM bank_settings";
                            $bank_stmt = $conn->prepare($bank_sql);
                            $bank_stmt->execute();
                            $bank_settings = $bank_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                        } catch (Exception $e) {
                            $bank_settings = [];
                        }

                        // جلب إيصال التحويل
                        try {
                            $receipt_sql = "SELECT * FROM transfer_receipts WHERE order_id = ?";
                            $receipt_stmt = $conn->prepare($receipt_sql);
                            $receipt_stmt->execute([$order_id]);
                            $receipt = $receipt_stmt->fetch();
                        } catch (Exception $e) {
                            $receipt = null;
                        }
                        ?>

                        <div class="bank-details-display">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="bank-info-card">
                                        <h6>بيانات الحساب المحول إليه:</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>اسم البنك:</strong></td>
                                                <td><?php echo htmlspecialchars($bank_settings['bank_name'] ?? 'الراجحي'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>اسم الحساب:</strong></td>
                                                <td><?php echo htmlspecialchars($bank_settings['account_name'] ?? 'مؤسسة اكنان'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم الحساب:</strong></td>
                                                <td><code><?php echo htmlspecialchars($bank_settings['account_number'] ?? '***************'); ?></code></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الآيبان:</strong></td>
                                                <td><code><?php echo htmlspecialchars($bank_settings['iban'] ?? '************************'); ?></code></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="receipt-info-card">
                                        <h6>إيصال التحويل:</h6>
                                        <?php if ($receipt): ?>
                                            <div class="receipt-details">
                                                <p><strong>اسم الملف:</strong> <?php echo htmlspecialchars($receipt['original_filename']); ?></p>
                                                <p><strong>حجم الملف:</strong> <?php echo number_format($receipt['file_size'] / 1024, 2); ?> كيلوبايت</p>
                                                <p><strong>تاريخ الرفع:</strong> <?php echo date('Y-m-d H:i:s', strtotime($receipt['upload_date'])); ?></p>

                                                <div class="receipt-actions">
                                                    <a href="uploads/transfer_receipts/<?php echo htmlspecialchars($receipt['receipt_filename']); ?>"
                                                       target="_blank" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i>عرض الإيصال
                                                    </a>
                                                    <a href="uploads/transfer_receipts/<?php echo htmlspecialchars($receipt['receipt_filename']); ?>"
                                                       download class="btn btn-success btn-sm">
                                                        <i class="fas fa-download me-1"></i>تحميل
                                                    </a>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                لم يتم رفع إيصال التحويل بعد
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Customer Information -->
                <div class="info-card">
                    <h5><i class="fas fa-user me-2"></i>معلومات العميل</h5>
                    <div class="info-row">
                        <span class="info-label">الاسم:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">البريد الإلكتروني:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_email']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">رقم الهاتف:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_phone']); ?></span>
                    </div>
                    <?php if ($order['customer_phone_alt']): ?>
                    <div class="info-row">
                        <span class="info-label">رقم هاتف بديل:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_phone_alt']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Shipping Information -->
                <div class="info-card">
                    <h5><i class="fas fa-truck me-2"></i>معلومات الشحن</h5>
                    <div class="info-row">
                        <span class="info-label">المدينة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['city_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">المنطقة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['area_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">العنوان:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['shipping_address']); ?></span>
                    </div>
                    <?php if ($order['landmark']): ?>
                    <div class="info-row">
                        <span class="info-label">علامة مميزة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['landmark']); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if ($order['delivery_time']): ?>
                    <div class="info-row">
                        <span class="info-label">وقت التسليم المفضل:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['delivery_time']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Order Items -->
                <div class="info-card">
                    <h5><i class="fas fa-box me-2"></i>عناصر الطلب</h5>
                    <?php foreach ($order_items as $item): ?>
                        <div class="product-item">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <?php if ($item['featured_image']): ?>
                                        <img src="../uploads/<?php echo htmlspecialchars($item['featured_image']); ?>"
                                             alt="<?php echo htmlspecialchars($item['title']); ?>"
                                             class="product-image">
                                    <?php else: ?>
                                        <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="mb-2"><?php echo htmlspecialchars($item['title']); ?></h6>
                                    <div class="product-details">
                                        <?php if ($item['color_name']): ?>
                                            <span class="badge bg-primary me-2">
                                                <i class="fas fa-palette me-1"></i>
                                                اللون: <?php echo htmlspecialchars($item['color_name']); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($item['size_name']): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-ruler me-1"></i>
                                                المقاس: <?php echo htmlspecialchars($item['size_name']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong>الكمية: <?php echo $item['quantity']; ?></strong>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="fw-bold"><?php echo number_format($item['price'], 2); ?> ر.س</div>
                                    <small class="text-muted">الإجمالي: <?php echo number_format($item['total'], 2); ?> ر.س</small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Notes -->
                <?php if ($order['notes']): ?>
                <div class="info-card">
                    <h5><i class="fas fa-sticky-note me-2"></i>ملاحظات</h5>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Order Summary -->
                <div class="info-card">
                    <h5><i class="fas fa-calculator me-2"></i>ملخص الطلب</h5>
                    <div class="info-row">
                        <span class="info-label">المجموع الفرعي:</span>
                        <span class="info-value"><?php echo number_format($order['subtotal'], 2); ?> ر.س</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تكلفة الشحن:</span>
                        <span class="info-value"><?php echo number_format($order['shipping_cost'], 2); ?> ر.س</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الضريبة:</span>
                        <span class="info-value"><?php echo number_format($order['tax_amount'], 2); ?> ر.س</span>
                    </div>
                    <?php if ($order['discount_amount'] > 0): ?>
                    <div class="info-row">
                        <span class="info-label">الخصم:</span>
                        <span class="info-value text-success">-<?php echo number_format($order['discount_amount'], 2); ?> ر.س</span>
                    </div>
                    <?php endif; ?>
                    <div class="total-summary">
                        <div class="total-row final">
                            <span>المجموع الإجمالي:</span>
                            <span><?php echo number_format($order['total_amount'], 2); ?> ر.س</span>
                        </div>
                    </div>
                </div>

                <!-- Update Status -->
                <div class="info-card">
                    <h5><i class="fas fa-edit me-2"></i>تحديث حالة الطلب</h5>
                    <form method="POST">
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="confirmed" <?php echo $order['status'] === 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                                <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>قيد التجهيز</option>
                                <option value="shipped" <?php echo $order['status'] === 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                                <option value="delivered" <?php echo $order['status'] === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="أضف ملاحظات حول تحديث الحالة..."><?php echo htmlspecialchars($order['notes']); ?></textarea>
                        </div>
                        <button type="submit" name="update_status" class="btn btn-primary w-100">
                            <i class="fas fa-save me-1"></i>تحديث الحالة
                        </button>
                    </form>
                </div>

                <!-- Quick Actions -->
                <div class="info-card">
                    <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                    <div class="d-grid gap-2">
                        <?php if ($order['status'] === 'pending'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="status" value="confirmed">
                                <button type="submit" name="update_status" class="btn btn-success w-100"
                                        onclick="return confirm('هل تريد تأكيد هذا الطلب؟')">
                                    <i class="fas fa-check me-1"></i>تأكيد الطلب
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if ($order['status'] === 'confirmed'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="status" value="processing">
                                <button type="submit" name="update_status" class="btn btn-info w-100"
                                        onclick="return confirm('هل تريد تحويل هذا الطلب لقيد التجهيز؟')">
                                    <i class="fas fa-cog me-1"></i>بدء التجهيز
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if ($order['status'] === 'processing'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="status" value="shipped">
                                <button type="submit" name="update_status" class="btn btn-primary w-100"
                                        onclick="return confirm('هل تم شحن هذا الطلب؟')">
                                    <i class="fas fa-truck me-1"></i>تم الشحن
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if ($order['status'] === 'shipped'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="status" value="delivered">
                                <button type="submit" name="update_status" class="btn btn-success w-100"
                                        onclick="return confirm('هل تم تسليم هذا الطلب؟')">
                                    <i class="fas fa-check-circle me-1"></i>تم التسليم
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if (in_array($order['status'], ['pending', 'confirmed'])): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" name="update_status" class="btn btn-danger w-100"
                                        onclick="return confirm('هل تريد إلغاء هذا الطلب؟')">
                                    <i class="fas fa-times me-1"></i>إلغاء الطلب
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <style>
    .bank-transfer-section {
        margin-top: 20px;
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
    }

    .bank-transfer-section h5 {
        color: #1e293b;
        margin-bottom: 20px;
        font-weight: 600;
    }

    .bank-details-display {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .bank-info-card, .receipt-info-card {
        background: #f8fafc;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        height: 100%;
    }

    .bank-info-card h6, .receipt-info-card h6 {
        color: #374151;
        margin-bottom: 15px;
        font-weight: 600;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 8px;
    }

    .bank-info-card table {
        margin: 0;
    }

    .bank-info-card td {
        padding: 8px 12px;
        border: none;
        border-bottom: 1px solid #f1f5f9;
    }

    .bank-info-card code {
        background: #e2e8f0;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
    }

    .receipt-details p {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .receipt-actions {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }

    .receipt-actions .btn {
        font-size: 13px;
        padding: 6px 12px;
    }

    .alert-warning {
        font-size: 14px;
        margin: 0;
    }
    </style>
</body>
</html>
