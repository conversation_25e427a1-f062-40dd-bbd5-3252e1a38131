<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    
    try {
        $stmt = $conn->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_status, $order_id]);
        $success_message = 'تم تحديث حالة الطلب بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث حالة الطلب';
    }
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = ["1=1"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.customer_phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد الطلبات الإجمالي
$count_query = "SELECT COUNT(*) as total FROM orders o WHERE $where_clause";
$count_stmt = $conn->prepare($count_query);
$count_stmt->execute($params);
$total_orders = $count_stmt->fetch()['total'];
$total_pages = ceil($total_orders / $per_page);

// جلب الطلبات
$query = "SELECT o.*,
                 c.name as city_name,
                 a.name as area_name,
                 (SELECT COUNT(*) FROM order_items oi WHERE oi.order_id = o.id) as items_count
          FROM orders o
          LEFT JOIN cities c ON o.city_id = c.id
          LEFT JOIN areas a ON o.area_id = a.id
          WHERE $where_clause
          ORDER BY o.created_at DESC
          LIMIT $per_page OFFSET $offset";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$orders = $stmt->fetchAll();

// جلب تفاصيل المنتجات لكل طلب
if (!empty($orders)) {
    foreach ($orders as $key => $order) {
        $items_query = "SELECT oi.title, oi.quantity,
                               COALESCE(col.name, 'غير محدد') as color_name,
                               COALESCE(s.name, 'غير محدد') as size_name
                        FROM order_items oi
                        LEFT JOIN colors col ON oi.color_id = col.id
                        LEFT JOIN sizes s ON oi.size_id = s.id
                        WHERE oi.order_id = ?
                        ORDER BY oi.id
                        LIMIT 3";

        $items_stmt = $conn->prepare($items_query);
        $items_stmt->execute([$order['id']]);
        $items = $items_stmt->fetchAll();

        $items_preview = [];
        if (!empty($items)) {
            foreach ($items as $item) {
                $item_text = $item['title'];
                if ($item['color_name'] && $item['color_name'] !== 'غير محدد') {
                    $item_text .= ' - ' . $item['color_name'];
                }
                if ($item['size_name'] && $item['size_name'] !== 'غير محدد') {
                    $item_text .= ' - ' . $item['size_name'];
                }
                $item_text .= ' (الكمية: ' . $item['quantity'] . ')';
                $items_preview[] = $item_text;
            }
        }

        $orders[$key]['items_preview'] = !empty($items_preview) ? implode(' | ', $items_preview) : 'لا توجد تفاصيل';
    }
}

// إحصائيات سريعة
$stats_query = "SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders,
    COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
    SUM(CASE WHEN status IN ('delivered', 'shipped') THEN total_amount ELSE 0 END) as total_revenue
FROM orders";
$stats_stmt = $conn->query($stats_query);
$stats = $stats_stmt->fetch();

// دالة لترجمة حالة الطلب
function getStatusText($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التجهيز',
        'shipped' => 'تم الشحن',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي'
    ];
    return $statuses[$status] ?? $status;
}

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'confirmed' => 'info',
        'processing' => 'primary',
        'shipped' => 'success',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar .logo p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }

        .notification-badge {
            background: #ff4757;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            margin-right: 0.5rem;
            min-width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .nav-link-with-badge {
            justify-content: space-between;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.25rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }
        
        .stat-card .stat-number {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .orders-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .order-row {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
            transition: background-color 0.3s ease;
        }
        
        .order-row:hover {
            background-color: #f8f9fa;
        }
        
        .order-row:last-child {
            border-bottom: none;
        }
        
        .order-number {
            font-weight: 600;
            color: #667eea;
        }
        
        .customer-info {
            font-size: 0.9rem;
            color: #666;
        }

        .items-preview {
            margin-top: 0.5rem;
            padding: 0.6rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            border-left: 4px solid #28a745;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .items-preview small {
            font-size: 0.85rem;
            line-height: 1.5;
            color: #495057;
            font-weight: 500;
        }

        .items-preview i {
            color: #28a745;
        }
        
        .order-total {
            font-weight: 600;
            font-size: 1.1rem;
            color: #333;
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .btn-action {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            border-radius: 6px;
            margin: 0 0.2rem;
        }
        
        .pagination-wrapper {
            margin-top: 2rem;
            display: flex;
            justify-content: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ddd;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
            <p>متجر فساتين السهرة</p>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="orders.php" class="nav-link active nav-link-with-badge">
                    <span>
                        <i class="fas fa-shopping-cart"></i>
                        الطلبات
                    </span>
                    <?php if ($stats['pending_orders'] > 0): ?>
                        <span class="notification-badge"><?php echo $stats['pending_orders']; ?></span>
                    <?php endif; ?>
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="bank_settings.php" class="nav-link">
                    <i class="fas fa-university"></i>
                    إعدادات البنك
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1>
                <i class="fas fa-shopping-cart"></i>
                إدارة الطلبات
            </h1>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number text-primary"><?php echo number_format($stats['total_orders']); ?></div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-warning"><?php echo number_format($stats['pending_orders']); ?></div>
                <div class="stat-label">طلبات في الانتظار</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-info"><?php echo number_format($stats['confirmed_orders']); ?></div>
                <div class="stat-label">طلبات مؤكدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success"><?php echo number_format($stats['shipped_orders']); ?></div>
                <div class="stat-label">طلبات مشحونة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success"><?php echo number_format($stats['delivered_orders']); ?></div>
                <div class="stat-label">طلبات مسلمة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-danger"><?php echo number_format($stats['cancelled_orders']); ?></div>
                <div class="stat-label">طلبات ملغية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success"><?php echo number_format($stats['total_revenue'], 2); ?> ر.س</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-card">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="رقم الطلب، اسم العميل، البريد الإلكتروني...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                        <option value="confirmed" <?php echo $status_filter === 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                        <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>قيد التجهيز</option>
                        <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                        <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <a href="orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Orders Table -->
        <div class="orders-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الطلبات (<?php echo number_format($total_orders); ?> طلب)
                </h5>
            </div>

            <?php if (empty($orders)): ?>
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <h5>لا توجد طلبات</h5>
                    <p>لم يتم العثور على طلبات تطابق معايير البحث</p>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <div class="order-row">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="order-number">#<?php echo htmlspecialchars($order['order_number']); ?></div>
                                <div class="customer-info">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo htmlspecialchars($order['customer_name']); ?>
                                </div>
                                <div class="customer-info">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($order['customer_phone']); ?>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="customer-info">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($order['city_name'] . ' - ' . $order['area_name']); ?>
                                </div>
                                <div class="customer-info">
                                    <i class="fas fa-box me-1"></i>
                                    <?php echo $order['items_count']; ?> منتج
                                </div>
                                <?php if (!empty($order['items_preview'])): ?>
                                <div class="items-preview">
                                    <small class="text-muted">
                                        <i class="fas fa-list me-1"></i>
                                        <?php
                                        $preview = $order['items_preview'];
                                        if (strlen($preview) > 100) {
                                            echo htmlspecialchars(substr($preview, 0, 100)) . '...';
                                        } else {
                                            echo htmlspecialchars($preview);
                                        }
                                        ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-2">
                                <div class="order-total"><?php echo number_format($order['total_amount'], 2); ?> ر.س</div>
                                <div class="customer-info">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <span class="badge bg-<?php echo getStatusColor($order['status']); ?> status-badge">
                                    <?php echo getStatusText($order['status']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex flex-wrap gap-1">
                                    <a href="order-details.php?id=<?php echo $order['id']; ?>"
                                       class="btn btn-outline-primary btn-action">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>

                                    <?php if ($order['status'] === 'pending'): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="status" value="confirmed">
                                            <button type="submit" name="update_status"
                                                    class="btn btn-outline-success btn-action"
                                                    onclick="return confirm('هل تريد تأكيد هذا الطلب؟')">
                                                <i class="fas fa-check"></i> تأكيد
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if ($order['status'] === 'confirmed'): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="status" value="processing">
                                            <button type="submit" name="update_status"
                                                    class="btn btn-outline-info btn-action"
                                                    onclick="return confirm('هل تريد تحويل هذا الطلب لقيد التجهيز؟')">
                                                <i class="fas fa-cog"></i> تجهيز
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if ($order['status'] === 'processing'): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="status" value="shipped">
                                            <button type="submit" name="update_status"
                                                    class="btn btn-outline-primary btn-action"
                                                    onclick="return confirm('هل تم شحن هذا الطلب؟')">
                                                <i class="fas fa-truck"></i> شحن
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if ($order['status'] === 'shipped'): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="status" value="delivered">
                                            <button type="submit" name="update_status"
                                                    class="btn btn-outline-success btn-action"
                                                    onclick="return confirm('هل تم تسليم هذا الطلب؟')">
                                                <i class="fas fa-check-circle"></i> تسليم
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if (in_array($order['status'], ['pending', 'confirmed'])): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <input type="hidden" name="status" value="cancelled">
                                            <button type="submit" name="update_status"
                                                    class="btn btn-outline-danger btn-action"
                                                    onclick="return confirm('هل تريد إلغاء هذا الطلب؟')">
                                                <i class="fas fa-times"></i> إلغاء
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <a href="print-order.php?id=<?php echo $order['id']; ?>"
                                       target="_blank" class="btn btn-outline-secondary btn-action">
                                        <i class="fas fa-print"></i> طباعة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination-wrapper">
                <nav aria-label="تنقل بين الصفحات">
                    <ul class="pagination">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $start = max(1, $page - 2);
                        $end = min($total_pages, $page + 2);

                        for ($i = $start; $i <= $end; $i++):
                        ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
