<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: orders.php');
    exit();
}

$order_id = (int)$_GET['id'];

// جلب تفاصيل الطلب
$order_query = "SELECT o.*, 
                       c.name as city_name, 
                       a.name as area_name,
                       co.name as country_name
                FROM orders o 
                LEFT JOIN cities c ON o.city_id = c.id 
                LEFT JOIN areas a ON o.area_id = a.id 
                LEFT JOIN countries co ON c.country_id = co.id
                WHERE o.id = ?";

$order_stmt = $conn->prepare($order_query);
$order_stmt->execute([$order_id]);
$order = $order_stmt->fetch();

if (!$order) {
    header('Location: orders.php');
    exit();
}

// جلب عناصر الطلب
$items_query = "SELECT oi.*, 
                       p.title as product_title,
                       c.name as color_name,
                       s.name as size_name
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                LEFT JOIN colors c ON oi.color_id = c.id
                LEFT JOIN sizes s ON oi.size_id = s.id
                WHERE oi.order_id = ?
                ORDER BY oi.id";

$items_stmt = $conn->prepare($items_query);
$items_stmt->execute([$order_id]);
$order_items = $items_stmt->fetchAll();

// جلب إعدادات الموقع
$settings_query = "SELECT setting_key, setting_value FROM settings";
$settings_stmt = $conn->query($settings_query);
$settings = [];
while ($row = $settings_stmt->fetch()) {
    $settings[$row['setting_key']] = $row['setting_value'];
}

// دالة لترجمة حالة الطلب
function getStatusText($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التجهيز',
        'shipped' => 'تم الشحن',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي'
    ];
    return $statuses[$status] ?? $status;
}

// دالة لترجمة طريقة الدفع
function getPaymentMethodText($method) {
    $methods = [
        'cod' => 'الدفع عند الاستلام',
        'bank_transfer' => 'تحويل بنكي',
        'online' => 'دفع إلكتروني'
    ];
    return $methods[$method] ?? $method;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الطلب #<?php echo htmlspecialchars($order['order_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: white;
            color: #333;
        }
        
        .print-header {
            border-bottom: 3px solid #667eea;
            padding-bottom: 2rem;
            margin-bottom: 2rem;
        }
        
        .company-logo {
            max-height: 80px;
            margin-bottom: 1rem;
        }
        
        .company-info h2 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .company-info p {
            margin: 0;
            color: #666;
        }
        
        .order-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .order-title h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-section h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
            flex: 1;
        }
        
        .info-value {
            color: #333;
            flex: 2;
            text-align: left;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }
        
        .items-table th,
        .items-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .items-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        .items-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
        }
        
        .total-row.final {
            border-top: 2px solid rgba(255,255,255,0.3);
            margin-top: 1rem;
            padding-top: 1rem;
            font-size: 1.3rem;
            font-weight: 700;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d4edda; color: #155724; }
        .status-delivered { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .print-footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 2px solid #eee;
            text-align: center;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }
            
            .no-print {
                display: none !important;
            }
            
            .print-header,
            .info-section,
            .total-section {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Header -->
        <div class="print-header">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <?php if (isset($settings['site_logo']) && $settings['site_logo']): ?>
                        <img src="../uploads/<?php echo htmlspecialchars($settings['site_logo']); ?>" 
                             alt="شعار الشركة" class="company-logo">
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <div class="company-info text-center">
                        <h2><?php echo htmlspecialchars($settings['site_name'] ?? 'متجر فساتين السهرة'); ?></h2>
                        <p><?php echo htmlspecialchars($settings['site_description'] ?? 'أجمل وأحدث تصاميم فساتين السهرة'); ?></p>
                        <?php if (isset($settings['contact_phone']) && $settings['contact_phone']): ?>
                            <p>هاتف: <?php echo htmlspecialchars($settings['contact_phone']); ?></p>
                        <?php endif; ?>
                        <?php if (isset($settings['contact_email']) && $settings['contact_email']): ?>
                            <p>بريد إلكتروني: <?php echo htmlspecialchars($settings['contact_email']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3 text-end">
                    <p><strong>تاريخ الطباعة:</strong><br><?php echo date('Y-m-d H:i:s'); ?></p>
                </div>
            </div>
        </div>

        <!-- Order Title -->
        <div class="order-title">
            <h1>فاتورة الطلب #<?php echo htmlspecialchars($order['order_number']); ?></h1>
        </div>

        <div class="row">
            <!-- Order Information -->
            <div class="col-md-6">
                <div class="info-section">
                    <h5>معلومات الطلب</h5>
                    <div class="info-row">
                        <span class="info-label">رقم الطلب:</span>
                        <span class="info-value">#<?php echo htmlspecialchars($order['order_number']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">تاريخ الطلب:</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            <span class="status-badge status-<?php echo $order['status']; ?>">
                                <?php echo getStatusText($order['status']); ?>
                            </span>
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">طريقة الدفع:</span>
                        <span class="info-value"><?php echo getPaymentMethodText($order['payment_method']); ?></span>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="col-md-6">
                <div class="info-section">
                    <h5>معلومات العميل</h5>
                    <div class="info-row">
                        <span class="info-label">الاسم:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">البريد الإلكتروني:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_email']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">رقم الهاتف:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_phone']); ?></span>
                    </div>
                    <?php if ($order['customer_phone_alt']): ?>
                    <div class="info-row">
                        <span class="info-label">رقم هاتف بديل:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['customer_phone_alt']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Shipping Information -->
        <div class="info-section">
            <h5>معلومات الشحن</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">المدينة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['city_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">المنطقة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['area_name']); ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">العنوان:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['shipping_address']); ?></span>
                    </div>
                    <?php if ($order['landmark']): ?>
                    <div class="info-row">
                        <span class="info-label">علامة مميزة:</span>
                        <span class="info-value"><?php echo htmlspecialchars($order['landmark']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php if ($order['delivery_time']): ?>
            <div class="info-row">
                <span class="info-label">وقت التسليم المفضل:</span>
                <span class="info-value"><?php echo htmlspecialchars($order['delivery_time']); ?></span>
            </div>
            <?php endif; ?>
        </div>

        <!-- Order Items -->
        <div class="info-section">
            <h5>عناصر الطلب</h5>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>اللون</th>
                        <th>المقاس</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order_items as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['title']); ?></td>
                            <td><?php echo htmlspecialchars($item['color_name'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($item['size_name'] ?? '-'); ?></td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td><?php echo number_format($item['price'], 2); ?> ر.س</td>
                            <td><?php echo number_format($item['total'], 2); ?> ر.س</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Order Total -->
        <div class="total-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?php echo number_format($order['subtotal'], 2); ?> ر.س</span>
            </div>
            <div class="total-row">
                <span>تكلفة الشحن:</span>
                <span><?php echo number_format($order['shipping_cost'], 2); ?> ر.س</span>
            </div>
            <div class="total-row">
                <span>الضريبة:</span>
                <span><?php echo number_format($order['tax_amount'], 2); ?> ر.س</span>
            </div>
            <?php if ($order['discount_amount'] > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span>-<?php echo number_format($order['discount_amount'], 2); ?> ر.س</span>
            </div>
            <?php endif; ?>
            <div class="total-row final">
                <span>المجموع الإجمالي:</span>
                <span><?php echo number_format($order['total_amount'], 2); ?> ر.س</span>
            </div>
        </div>

        <!-- Notes -->
        <?php if ($order['notes']): ?>
        <div class="info-section">
            <h5>ملاحظات</h5>
            <p><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- Print Footer -->
        <div class="print-footer">
            <p>شكراً لتسوقكم معنا</p>
            <p>هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>
        </div>

        <!-- Print Button -->
        <div class="text-center mt-4 no-print">
            <button onclick="window.print()" class="btn btn-primary btn-lg">
                <i class="fas fa-print me-2"></i>طباعة الفاتورة
            </button>
            <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-arrow-right me-2"></i>العودة لتفاصيل الطلب
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
