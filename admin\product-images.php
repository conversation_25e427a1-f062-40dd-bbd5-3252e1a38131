<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$product_id = intval($_GET['id'] ?? 0);

if ($product_id <= 0) {
    header("Location: products.php");
    exit();
}

// جلب بيانات المنتج
try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        header("Location: products.php");
        exit();
    }
} catch (Exception $e) {
    header("Location: products.php");
    exit();
}

// جلب صور المنتج
try {
    $gallery_stmt = $conn->prepare("SELECT * FROM product_images WHERE product_id = ? AND image_type = 'gallery' ORDER BY sort_order");
    $gallery_stmt->execute([$product_id]);
    $gallery_images = $gallery_stmt->fetchAll();
    
    $lifestyle_stmt = $conn->prepare("SELECT * FROM product_images WHERE product_id = ? AND image_type = 'lifestyle' ORDER BY sort_order");
    $lifestyle_stmt->execute([$product_id]);
    $lifestyle_images = $lifestyle_stmt->fetchAll();
    
} catch (Exception $e) {
    $gallery_images = [];
    $lifestyle_images = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صور المنتج - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h2 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }
        
        .images-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .image-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: move;
        }
        
        .image-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
        
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-item:hover .image-overlay {
            opacity: 1;
        }
        
        .image-action {
            background: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .image-action:hover {
            transform: scale(1.1);
        }
        
        .image-action.featured {
            background: #ffd700;
            color: #333;
        }
        
        .image-action.delete {
            background: #dc3545;
            color: white;
        }
        
        .featured-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: #f8f9fa;
            color: #666;
            margin-left: 0.25rem;
        }
        
        .nav-tabs .nav-link.active {
            background: white;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 10px 10px;
            padding: 1.5rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .sortable-ghost {
            opacity: 0.5;
        }
        
        .sortable-chosen {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link active">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-images me-2"></i>إدارة صور المنتج</h2>
                    <p class="mb-0 text-muted"><?php echo htmlspecialchars($product['title']); ?></p>
                </div>
                <div>
                    <a href="edit-product.php?id=<?php echo $product_id; ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-edit me-2"></i>تعديل المنتج
                    </a>
                    <a href="products.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Images Management -->
        <div class="images-card">
            <!-- Tabs -->
            <ul class="nav nav-tabs" id="imagesTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="gallery-tab" data-bs-toggle="tab" data-bs-target="#gallery" type="button" role="tab">
                        <i class="fas fa-images me-2"></i>صور المعرض (<?php echo count($gallery_images); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="lifestyle-tab" data-bs-toggle="tab" data-bs-target="#lifestyle" type="button" role="tab">
                        <i class="fas fa-camera me-2"></i>صور على الطبيعة (<?php echo count($lifestyle_images); ?>)
                    </button>
                </li>
            </ul>
            
            <!-- Tab Content -->
            <div class="tab-content" id="imagesTabContent">
                <!-- Gallery Images Tab -->
                <div class="tab-pane fade show active" id="gallery" role="tabpanel">
                    <!-- Upload Area -->
                    <div class="upload-area" onclick="document.getElementById('galleryFiles').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>اسحب الصور هنا أو انقر للاختيار</h5>
                        <p class="text-muted">يمكنك رفع عدة صور في نفس الوقت (JPG, PNG, GIF, WEBP - حد أقصى 5MB لكل صورة)</p>
                        <input type="file" id="galleryFiles" multiple accept="image/*" style="display: none;">
                    </div>

                    <!-- Loading -->
                    <div class="loading" id="galleryLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الرفع...</span>
                        </div>
                        <p class="mt-2">جاري رفع الصور...</p>
                    </div>

                    <!-- Images Grid -->
                    <div class="images-grid" id="galleryGrid">
                        <?php foreach ($gallery_images as $image): ?>
                            <div class="image-item" data-id="<?php echo $image['id']; ?>">
                                <img src="../uploads/<?php echo htmlspecialchars($image['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($image['alt_text']); ?>">

                                <?php if ($product['featured_image'] === $image['image_path']): ?>
                                    <div class="featured-badge">مميزة</div>
                                <?php endif; ?>

                                <div class="image-overlay">
                                    <button class="image-action featured" onclick="setFeatured(<?php echo $image['id']; ?>)" title="تعيين كصورة مميزة">
                                        <i class="fas fa-star"></i>
                                    </button>
                                    <button class="image-action" onclick="viewImage('<?php echo htmlspecialchars($image['image_path']); ?>')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="image-action delete" onclick="deleteImage(<?php echo $image['id']; ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (empty($gallery_images)): ?>
                        <div class="text-center py-5" id="galleryEmpty">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد صور في المعرض</h5>
                            <p class="text-muted">ابدأ برفع أول صورة للمنتج</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Lifestyle Images Tab -->
                <div class="tab-pane fade" id="lifestyle" role="tabpanel">
                    <!-- Upload Area -->
                    <div class="upload-area" onclick="document.getElementById('lifestyleFiles').click()">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5>اسحب صور الطبيعة هنا أو انقر للاختيار</h5>
                        <p class="text-muted">صور المنتج في بيئة طبيعية أو على موديل (JPG, PNG, GIF, WEBP - حد أقصى 5MB لكل صورة)</p>
                        <input type="file" id="lifestyleFiles" multiple accept="image/*" style="display: none;">
                    </div>

                    <!-- Loading -->
                    <div class="loading" id="lifestyleLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الرفع...</span>
                        </div>
                        <p class="mt-2">جاري رفع الصور...</p>
                    </div>

                    <!-- Images Grid -->
                    <div class="images-grid" id="lifestyleGrid">
                        <?php foreach ($lifestyle_images as $image): ?>
                            <div class="image-item" data-id="<?php echo $image['id']; ?>">
                                <img src="../uploads/<?php echo htmlspecialchars($image['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($image['alt_text']); ?>">

                                <div class="image-overlay">
                                    <button class="image-action" onclick="viewImage('<?php echo htmlspecialchars($image['image_path']); ?>')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="image-action delete" onclick="deleteImage(<?php echo $image['id']; ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (empty($lifestyle_images)): ?>
                        <div class="text-center py-5" id="lifestyleEmpty">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد صور على الطبيعة</h5>
                            <p class="text-muted">أضف صور للمنتج في بيئة طبيعية</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Viewer Modal -->
    <div class="modal fade" id="imageViewerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عرض الصورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="viewerImage" src="" alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        const productId = <?php echo $product_id; ?>;

        // إعداد Sortable للترتيب
        const galleryGrid = document.getElementById('galleryGrid');
        const lifestyleGrid = document.getElementById('lifestyleGrid');

        if (galleryGrid) {
            new Sortable(galleryGrid, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                onEnd: function(evt) {
                    updateImageOrder('gallery');
                }
            });
        }

        if (lifestyleGrid) {
            new Sortable(lifestyleGrid, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                onEnd: function(evt) {
                    updateImageOrder('lifestyle');
                }
            });
        }

        // معالجة رفع الصور
        document.getElementById('galleryFiles').addEventListener('change', function(e) {
            uploadImages(e.target.files, 'gallery');
        });

        document.getElementById('lifestyleFiles').addEventListener('change', function(e) {
            uploadImages(e.target.files, 'lifestyle');
        });

        // Drag and Drop
        setupDragAndDrop('gallery');
        setupDragAndDrop('lifestyle');

        function setupDragAndDrop(type) {
            const uploadArea = document.querySelector(`#${type} .upload-area`);

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                uploadImages(e.dataTransfer.files, type);
            });
        }

        function uploadImages(files, type) {
            if (files.length === 0) return;

            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('image_type', type);

            for (let i = 0; i < files.length; i++) {
                formData.append('images[]', files[i]);
            }

            const loadingElement = document.getElementById(`${type}Loading`);
            loadingElement.style.display = 'block';

            fetch('upload_images.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingElement.style.display = 'none';

                if (data.success) {
                    showAlert('success', data.message);
                    addImagesToGrid(data.images, type);
                    updateTabCounts();
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                loadingElement.style.display = 'none';
                showAlert('error', 'حدث خطأ أثناء رفع الصور');
                console.error('Error:', error);
            });
        }

        function addImagesToGrid(images, type) {
            const grid = document.getElementById(`${type}Grid`);
            const emptyElement = document.getElementById(`${type}Empty`);

            if (emptyElement) {
                emptyElement.style.display = 'none';
            }

            images.forEach(image => {
                const imageElement = createImageElement(image, type);
                grid.appendChild(imageElement);
            });
        }

        function createImageElement(image, type) {
            const div = document.createElement('div');
            div.className = 'image-item';
            div.setAttribute('data-id', image.id);

            let featuredBadge = '';
            let featuredButton = '';

            if (type === 'gallery') {
                featuredButton = `<button class="image-action featured" onclick="setFeatured(${image.id})" title="تعيين كصورة مميزة">
                    <i class="fas fa-star"></i>
                </button>`;
            }

            div.innerHTML = `
                <img src="${image.url}" alt="${image.alt_text}">
                ${featuredBadge}
                <div class="image-overlay">
                    ${featuredButton}
                    <button class="image-action" onclick="viewImage('${image.path}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="image-action delete" onclick="deleteImage(${image.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            return div;
        }

        function deleteImage(imageId) {
            if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
                return;
            }

            const formData = new FormData();
            formData.append('image_id', imageId);

            fetch('delete_image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    const imageElement = document.querySelector(`[data-id="${imageId}"]`);
                    if (imageElement) {
                        imageElement.remove();
                    }
                    updateTabCounts();
                    checkEmptyGrids();
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                showAlert('error', 'حدث خطأ أثناء حذف الصورة');
                console.error('Error:', error);
            });
        }

        function setFeatured(imageId) {
            const formData = new FormData();
            formData.append('image_id', imageId);

            fetch('set_featured_image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    // إزالة جميع شارات "مميزة" الموجودة
                    document.querySelectorAll('.featured-badge').forEach(badge => badge.remove());
                    // إضافة شارة "مميزة" للصورة الجديدة
                    const imageElement = document.querySelector(`[data-id="${imageId}"]`);
                    if (imageElement) {
                        const badge = document.createElement('div');
                        badge.className = 'featured-badge';
                        badge.textContent = 'مميزة';
                        imageElement.appendChild(badge);
                    }
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                showAlert('error', 'حدث خطأ أثناء تعيين الصورة المميزة');
                console.error('Error:', error);
            });
        }

        function viewImage(imagePath) {
            const modal = new bootstrap.Modal(document.getElementById('imageViewerModal'));
            document.getElementById('viewerImage').src = '../uploads/' + imagePath;
            modal.show();
        }

        function updateImageOrder(type) {
            const grid = document.getElementById(`${type}Grid`);
            const imageIds = Array.from(grid.children).map(item => item.getAttribute('data-id'));

            const formData = new FormData();
            formData.append('image_ids', JSON.stringify(imageIds));

            fetch('sort_images.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'تم تحديث ترتيب الصور بنجاح');
                } else {
                    showAlert('error', data.message);
                }
            })
            .catch(error => {
                showAlert('error', 'حدث خطأ أثناء تحديث الترتيب');
                console.error('Error:', error);
            });
        }

        function updateTabCounts() {
            const galleryCount = document.getElementById('galleryGrid').children.length;
            const lifestyleCount = document.getElementById('lifestyleGrid').children.length;

            document.querySelector('#gallery-tab').innerHTML = `<i class="fas fa-images me-2"></i>صور المعرض (${galleryCount})`;
            document.querySelector('#lifestyle-tab').innerHTML = `<i class="fas fa-camera me-2"></i>صور على الطبيعة (${lifestyleCount})`;
        }

        function checkEmptyGrids() {
            const galleryGrid = document.getElementById('galleryGrid');
            const lifestyleGrid = document.getElementById('lifestyleGrid');
            const galleryEmpty = document.getElementById('galleryEmpty');
            const lifestyleEmpty = document.getElementById('lifestyleEmpty');

            if (galleryGrid.children.length === 0 && galleryEmpty) {
                galleryEmpty.style.display = 'block';
            }

            if (lifestyleGrid.children.length === 0 && lifestyleEmpty) {
                lifestyleEmpty.style.display = 'block';
            }
        }

        function showAlert(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            const container = document.querySelector('.main-content');
            const pageHeader = document.querySelector('.page-header');
            pageHeader.insertAdjacentHTML('afterend', alertHtml);

            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
