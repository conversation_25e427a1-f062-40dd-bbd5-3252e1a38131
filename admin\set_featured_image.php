<?php
require_once 'auth.php';
require_once '../config/database.php';

header('Content-Type: application/json');

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'طريقة الطلب غير صحيحة';
    echo json_encode($response);
    exit;
}

$image_id = intval($_POST['image_id'] ?? 0);

if ($image_id <= 0) {
    $response['message'] = 'معرف الصورة غير صحيح';
    echo json_encode($response);
    exit;
}

try {
    // جلب معلومات الصورة
    $stmt = $conn->prepare("SELECT pi.*, p.id as product_id FROM product_images pi 
                           JOIN products p ON pi.product_id = p.id 
                           WHERE pi.id = ?");
    $stmt->execute([$image_id]);
    $image = $stmt->fetch();
    
    if (!$image) {
        $response['message'] = 'الصورة غير موجودة';
        echo json_encode($response);
        exit;
    }
    
    // تحديث الصورة المميزة للمنتج
    $update_stmt = $conn->prepare("UPDATE products SET featured_image = ? WHERE id = ?");
    $update_stmt->execute([$image['image_path'], $image['product_id']]);
    
    $response['success'] = true;
    $response['message'] = 'تم تعيين الصورة كصورة مميزة بنجاح';
    
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ أثناء تعيين الصورة المميزة: ' . $e->getMessage();
}

echo json_encode($response);
?>
