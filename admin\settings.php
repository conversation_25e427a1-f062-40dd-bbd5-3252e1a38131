<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة رفع الشعار
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upload_logo'])) {
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $logo_path = uploadLogo($_FILES['logo']);
        if ($logo_path) {
            // حفظ مسار الشعار في قاعدة البيانات أو ملف الإعدادات
            try {
                // إنشاء جدول الإعدادات إذا لم يكن موجوداً
                $conn->exec("CREATE TABLE IF NOT EXISTS settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                
                // حفظ أو تحديث الشعار
                $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('site_logo', ?) 
                                       ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP");
                $stmt->execute([$logo_path, $logo_path]);
                
                $success_message = 'تم رفع الشعار بنجاح';
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء حفظ الشعار: ' . $e->getMessage();
            }
        } else {
            $error_message = 'فشل في رفع الشعار. تأكد من نوع الملف وحجمه.';
        }
    } else {
        $error_message = 'يرجى اختيار ملف الشعار';
    }
}

// معالجة حفظ الإعدادات العامة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_settings'])) {
    $site_name = trim($_POST['site_name'] ?? '');
    $site_description = trim($_POST['site_description'] ?? '');
    $site_keywords = trim($_POST['site_keywords'] ?? '');
    $contact_email = trim($_POST['contact_email'] ?? '');
    $contact_phone = trim($_POST['contact_phone'] ?? '');
    $facebook_url = trim($_POST['facebook_url'] ?? '');
    $instagram_url = trim($_POST['instagram_url'] ?? '');
    $twitter_url = trim($_POST['twitter_url'] ?? '');
    
    try {
        // إنشاء جدول الإعدادات إذا لم يكن موجوداً
        $conn->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        $settings = [
            'site_name' => $site_name,
            'site_description' => $site_description,
            'site_keywords' => $site_keywords,
            'contact_email' => $contact_email,
            'contact_phone' => $contact_phone,
            'facebook_url' => $facebook_url,
            'instagram_url' => $instagram_url,
            'twitter_url' => $twitter_url
        ];
        
        foreach ($settings as $key => $value) {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                   ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP");
            $stmt->execute([$key, $value, $value]);
        }
        
        $success_message = 'تم حفظ الإعدادات بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$current_settings = [];
try {
    $settings_stmt = $conn->query("SELECT setting_key, setting_value FROM settings");
    while ($row = $settings_stmt->fetch()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // الجدول غير موجود بعد
}

// دالة رفع الشعار
function uploadLogo($file) {
    $upload_dir = '../uploads/';
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    $max_file_size = 5 * 1024 * 1024; // 5MB
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        
        // التحقق من نوع الملف
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_types)) {
            return false;
        }
        
        // التحقق من حجم الملف
        if ($file_size > $max_file_size) {
            return false;
        }
        
        // حذف الشعار القديم إذا كان موجوداً
        $old_logos = glob($upload_dir . 'logo.*');
        foreach ($old_logos as $old_logo) {
            unlink($old_logo);
        }
        
        // إنشاء اسم ملف للشعار
        $new_file_name = 'logo.' . $file_ext;
        $file_path = $upload_dir . $new_file_name;
        
        // رفع الملف
        if (move_uploaded_file($file_tmp, $file_path)) {
            return $new_file_name;
        }
    }
    
    return false;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الموقع - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
        }
        
        .sidebar .logo {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.5rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .nav-link i {
            margin-left: 1rem;
            width: 20px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 700;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .current-logo {
            max-width: 200px;
            max-height: 100px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لافو</h3>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link active">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>
            </div>
            <div class="nav-item">
                <a href="orders.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة الطلبات
                </a>
            </div>
            <div class="nav-item">
                <a href="bank_settings.php" class="nav-link">
                    <i class="fas fa-university"></i>
                    إعدادات البنك
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-cog me-3"></i>إعدادات الموقع</h1>
            <p class="mb-0 text-muted">إدارة الإعدادات العامة للموقع والشعار</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- Logo Upload -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-image me-2"></i>شعار الموقع</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($current_settings['site_logo']) && file_exists('../uploads/' . $current_settings['site_logo'])): ?>
                            <div class="text-center mb-3">
                                <p class="text-muted">الشعار الحالي:</p>
                                <img src="../uploads/<?php echo htmlspecialchars($current_settings['site_logo']); ?>" 
                                     alt="شعار الموقع" class="current-logo">
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label class="form-label">رفع شعار جديد</label>
                                <div class="upload-area" onclick="document.getElementById('logoFile').click()">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                    <p class="mb-1">انقر لاختيار الشعار</p>
                                    <small class="text-muted">JPG, PNG, GIF, WEBP, SVG (حد أقصى 5MB)</small>
                                </div>
                                <input type="file" id="logoFile" name="logo" accept="image/*" style="display: none;">
                                <div id="logoPreview"></div>
                            </div>
                            
                            <button type="submit" name="upload_logo" class="btn btn-primary w-100">
                                <i class="fas fa-upload me-2"></i>رفع الشعار
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- General Settings -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-globe me-2"></i>الإعدادات العامة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="site_name" class="form-label">اسم الموقع</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                       value="<?php echo htmlspecialchars($current_settings['site_name'] ?? 'متجر فساتين السهرة'); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_description" class="form-label">وصف الموقع</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($current_settings['site_description'] ?? 'أجمل وأحدث تصاميم فساتين السهرة الأنيقة والراقية'); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_keywords" class="form-label">الكلمات المفتاحية</label>
                                <input type="text" class="form-control" id="site_keywords" name="site_keywords" 
                                       value="<?php echo htmlspecialchars($current_settings['site_keywords'] ?? 'فساتين سهرة, فساتين أنيقة, فساتين راقية'); ?>">
                            </div>
                            
                            <button type="submit" name="save_settings" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact & Social Media Settings -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-address-book me-2"></i>معلومات التواصل ووسائل التواصل الاجتماعي</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="<?php echo htmlspecialchars($current_settings['contact_email'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="<?php echo htmlspecialchars($current_settings['contact_phone'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">رابط فيسبوك</label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="<?php echo htmlspecialchars($current_settings['facebook_url'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="instagram_url" class="form-label">رابط إنستغرام</label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                               value="<?php echo htmlspecialchars($current_settings['instagram_url'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">رابط تويتر</label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="<?php echo htmlspecialchars($current_settings['twitter_url'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" name="save_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ جميع الإعدادات
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الشعار
        document.getElementById('logoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('logoPreview');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <div class="text-center mt-3">
                            <p class="text-muted">معاينة الشعار الجديد:</p>
                            <img src="${e.target.result}" class="current-logo" alt="معاينة الشعار">
                            <button type="button" class="btn btn-sm btn-danger d-block mx-auto mt-2" onclick="removeLogo()">
                                <i class="fas fa-times"></i> إزالة
                            </button>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '';
            }
        });

        function removeLogo() {
            document.getElementById('logoFile').value = '';
            document.getElementById('logoPreview').innerHTML = '';
        }
    </script>
</body>
</html>
