<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة إنشاء الأقسام الأساسية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_basic_categories'])) {
    try {
        $conn->beginTransaction();
        
        // الأقسام الأساسية لمتجر فساتين السهرة
        $basic_categories = [
            [
                'name' => 'فساتين السهرة',
                'slug' => 'evening-dresses',
                'seo_title' => 'فساتين السهرة الفاخرة - مجموعة حصرية من أجمل الفساتين',
                'description' => 'مجموعة رائعة من فساتين السهرة الأنيقة والفاخرة لجميع المناسبات الخاصة',
                'seo_content' => 'اكتشفي مجموعتنا الحصرية من فساتين السهرة الفاخرة المصممة خصيصاً للمرأة العصرية. نقدم لك أحدث صيحات الموضة في عالم فساتين السهرة بأجود الخامات وأرقى التصاميم.',
                'sort_order' => 1,
                'subcategories' => [
                    [
                        'name' => 'فساتين طويلة',
                        'slug' => 'long-evening-dresses',
                        'seo_title' => 'فساتين سهرة طويلة - أناقة وفخامة لا مثيل لها',
                        'description' => 'فساتين سهرة طويلة أنيقة ومميزة للمناسبات الرسمية',
                        'sort_order' => 1
                    ],
                    [
                        'name' => 'فساتين قصيرة',
                        'slug' => 'short-evening-dresses',
                        'seo_title' => 'فساتين سهرة قصيرة - جمال وأناقة في تصميم عصري',
                        'description' => 'فساتين سهرة قصيرة عصرية ومناسبة للحفلات والمناسبات',
                        'sort_order' => 2
                    ],
                    [
                        'name' => 'فساتين كوكتيل',
                        'slug' => 'cocktail-dresses',
                        'seo_title' => 'فساتين كوكتيل أنيقة - للمناسبات الخاصة والحفلات',
                        'description' => 'فساتين كوكتيل أنيقة ومميزة للمناسبات الخاصة',
                        'sort_order' => 3
                    ]
                ]
            ],
            [
                'name' => 'فساتين الزفاف',
                'slug' => 'wedding-dresses',
                'seo_title' => 'فساتين زفاف فاخرة - ليوم أحلامك الخاص',
                'description' => 'مجموعة حصرية من فساتين الزفاف الفاخرة لتكوني أميرة في يومك الخاص',
                'seo_content' => 'اختاري فستان زفافك المثالي من مجموعتنا الحصرية. فساتين زفاف فاخرة بتصاميم عالمية وخامات راقية لتجعل يوم زفافك لا يُنسى.',
                'sort_order' => 2,
                'subcategories' => [
                    [
                        'name' => 'فساتين زفاف كلاسيكية',
                        'slug' => 'classic-wedding-dresses',
                        'seo_title' => 'فساتين زفاف كلاسيكية - أناقة خالدة لا تتأثر بالزمن',
                        'description' => 'فساتين زفاف بتصاميم كلاسيكية خالدة',
                        'sort_order' => 1
                    ],
                    [
                        'name' => 'فساتين زفاف عصرية',
                        'slug' => 'modern-wedding-dresses',
                        'seo_title' => 'فساتين زفاف عصرية - أحدث صيحات الموضة',
                        'description' => 'فساتين زفاف بتصاميم عصرية وحديثة',
                        'sort_order' => 2
                    ]
                ]
            ],
            [
                'name' => 'فساتين المناسبات',
                'slug' => 'occasion-dresses',
                'seo_title' => 'فساتين المناسبات - أناقة لكل مناسبة خاصة',
                'description' => 'فساتين أنيقة ومميزة لجميع المناسبات الخاصة والاحتفالات',
                'seo_content' => 'مجموعة متنوعة من فساتين المناسبات لتناسب جميع الأذواق والمناسبات. من الحفلات العائلية إلى المناسبات الرسمية.',
                'sort_order' => 3,
                'subcategories' => [
                    [
                        'name' => 'فساتين التخرج',
                        'slug' => 'graduation-dresses',
                        'seo_title' => 'فساتين التخرج - احتفلي بإنجازك بأناقة',
                        'description' => 'فساتين أنيقة لحفلات التخرج والمناسبات الأكاديمية',
                        'sort_order' => 1
                    ],
                    [
                        'name' => 'فساتين الحفلات',
                        'slug' => 'party-dresses',
                        'seo_title' => 'فساتين الحفلات - كوني نجمة الحفل',
                        'description' => 'فساتين مميزة للحفلات والمناسبات الاجتماعية',
                        'sort_order' => 2
                    ]
                ]
            ],
            [
                'name' => 'الإكسسوارات',
                'slug' => 'accessories',
                'seo_title' => 'إكسسوارات فاخرة - لمسة أناقة تكمل إطلالتك',
                'description' => 'مجموعة متنوعة من الإكسسوارات الفاخرة لتكملة إطلالتك',
                'seo_content' => 'اكتشفي مجموعتنا من الإكسسوارات الفاخرة التي تضيف لمسة من الأناقة والجمال لإطلالتك.',
                'sort_order' => 4,
                'subcategories' => [
                    [
                        'name' => 'المجوهرات',
                        'slug' => 'jewelry',
                        'seo_title' => 'مجوهرات فاخرة - بريق يضيء جمالك',
                        'description' => 'مجوهرات فاخرة وأنيقة لتكملة إطلالتك',
                        'sort_order' => 1
                    ],
                    [
                        'name' => 'الحقائب',
                        'slug' => 'bags',
                        'seo_title' => 'حقائب أنيقة - أناقة وعملية في آن واحد',
                        'description' => 'حقائب أنيقة ومميزة للمناسبات الخاصة',
                        'sort_order' => 2
                    ],
                    [
                        'name' => 'الأحذية',
                        'slug' => 'shoes',
                        'seo_title' => 'أحذية فاخرة - خطوات واثقة بأناقة',
                        'description' => 'أحذية فاخرة وأنيقة للمناسبات الخاصة',
                        'sort_order' => 3
                    ]
                ]
            ]
        ];
        
        foreach ($basic_categories as $category_data) {
            // إنشاء القسم الرئيسي
            $stmt = $conn->prepare("INSERT INTO categories (name, slug, seo_title, description, seo_content, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
            $stmt->execute([
                $category_data['name'],
                $category_data['slug'],
                $category_data['seo_title'],
                $category_data['description'],
                $category_data['seo_content'],
                $category_data['sort_order']
            ]);
            
            $parent_id = $conn->lastInsertId();
            
            // إنشاء الأقسام الفرعية
            if (isset($category_data['subcategories'])) {
                foreach ($category_data['subcategories'] as $subcategory) {
                    $sub_stmt = $conn->prepare("INSERT INTO categories (name, slug, seo_title, description, parent_id, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
                    $sub_stmt->execute([
                        $subcategory['name'],
                        $subcategory['slug'],
                        $subcategory['seo_title'],
                        $subcategory['description'],
                        $parent_id,
                        $subcategory['sort_order']
                    ]);
                }
            }
        }
        
        $conn->commit();
        $success_message = 'تم إنشاء الأقسام الأساسية بنجاح! تم إضافة ' . count($basic_categories) . ' أقسام رئيسية مع أقسامها الفرعية.';
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error_message = 'حدث خطأ أثناء إنشاء الأقسام: ' . $e->getMessage();
    }
}

// التحقق من وجود أقسام
try {
    $check_stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
    $categories_count = $check_stmt->fetch()['count'];
} catch (Exception $e) {
    $categories_count = 0;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد الأقسام الأساسية - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 800px;
            margin: 2rem auto;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .category-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .subcategory {
            margin-right: 2rem;
            padding: 0.5rem;
            background: white;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="mb-0"><i class="fas fa-magic me-3"></i>إعداد الأقسام الأساسية</h1>
            </div>
            <div class="card-body">
                <p class="text-muted">هذه الصفحة تساعدك على إنشاء الأقسام الأساسية لمتجر فساتين السهرة بنقرة واحدة.</p>
                
                <?php if ($categories_count > 0): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يوجد حالياً <strong><?php echo $categories_count; ?></strong> قسم في قاعدة البيانات.
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Categories Preview -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة الأقسام التي سيتم إنشاؤها</h5>
            </div>
            <div class="card-body">
                <div class="category-preview">
                    <h6><i class="fas fa-tag me-2"></i>فساتين السهرة</h6>
                    <div class="subcategory">• فساتين طويلة</div>
                    <div class="subcategory">• فساتين قصيرة</div>
                    <div class="subcategory">• فساتين كوكتيل</div>
                </div>
                
                <div class="category-preview">
                    <h6><i class="fas fa-tag me-2"></i>فساتين الزفاف</h6>
                    <div class="subcategory">• فساتين زفاف كلاسيكية</div>
                    <div class="subcategory">• فساتين زفاف عصرية</div>
                </div>
                
                <div class="category-preview">
                    <h6><i class="fas fa-tag me-2"></i>فساتين المناسبات</h6>
                    <div class="subcategory">• فساتين التخرج</div>
                    <div class="subcategory">• فساتين الحفلات</div>
                </div>
                
                <div class="category-preview">
                    <h6><i class="fas fa-tag me-2"></i>الإكسسوارات</h6>
                    <div class="subcategory">• المجوهرات</div>
                    <div class="subcategory">• الحقائب</div>
                    <div class="subcategory">• الأحذية</div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
            <div class="card-body text-center">
                <form method="POST" action="">
                    <button type="submit" name="create_basic_categories" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-magic me-2"></i>إنشاء الأقسام الأساسية
                    </button>
                    <a href="categories.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-list me-2"></i>عرض الأقسام الحالية
                    </a>
                </form>
                
                <p class="text-muted mt-3 mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    سيتم إنشاء 4 أقسام رئيسية مع 10 أقسام فرعية مع محتوى SEO محسن
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
