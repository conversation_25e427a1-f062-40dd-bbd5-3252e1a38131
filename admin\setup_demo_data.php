<?php
/**
 * ملف إضافة بيانات تجريبية للوحة التحكم
 * يضيف ألوان ومقاسات أساسية لتسهيل الاختبار
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div style='font-family: Arial; padding: 20px; max-width: 800px; margin: 0 auto;'>";
    echo "<h2>إعداد البيانات التجريبية</h2>";
    
    // إضافة الألوان الأساسية
    $colors = [
        ['name' => 'أسود', 'hex_code' => '#000000'],
        ['name' => 'أبيض', 'hex_code' => '#FFFFFF'],
        ['name' => 'أحمر', 'hex_code' => '#FF0000'],
        ['name' => 'أزرق', 'hex_code' => '#0000FF'],
        ['name' => 'وردي', 'hex_code' => '#FFC0CB'],
        ['name' => 'ذهبي', 'hex_code' => '#FFD700'],
        ['name' => 'فضي', 'hex_code' => '#C0C0C0'],
        ['name' => 'بنفسجي', 'hex_code' => '#800080'],
        ['name' => 'أخضر', 'hex_code' => '#008000'],
        ['name' => 'بني', 'hex_code' => '#A52A2A']
    ];
    
    echo "<h3>إضافة الألوان:</h3>";
    foreach ($colors as $color) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO colors (name, hex_code, is_active) VALUES (?, ?, 1)");
            $stmt->execute([$color['name'], $color['hex_code']]);
            echo "<div style='color: green;'>✅ تم إضافة اللون: {$color['name']}</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ اللون {$color['name']} موجود بالفعل</div>";
        }
    }
    
    // إضافة المقاسات الأساسية
    $sizes = [
        ['name' => 'XS', 'sort_order' => 1],
        ['name' => 'S', 'sort_order' => 2],
        ['name' => 'M', 'sort_order' => 3],
        ['name' => 'L', 'sort_order' => 4],
        ['name' => 'XL', 'sort_order' => 5],
        ['name' => 'XXL', 'sort_order' => 6],
        ['name' => '36', 'sort_order' => 10],
        ['name' => '38', 'sort_order' => 11],
        ['name' => '40', 'sort_order' => 12],
        ['name' => '42', 'sort_order' => 13],
        ['name' => '44', 'sort_order' => 14],
        ['name' => '46', 'sort_order' => 15]
    ];
    
    echo "<h3>إضافة المقاسات:</h3>";
    foreach ($sizes as $size) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO sizes (name, sort_order, is_active) VALUES (?, ?, 1)");
            $stmt->execute([$size['name'], $size['sort_order']]);
            echo "<div style='color: green;'>✅ تم إضافة المقاس: {$size['name']}</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ المقاس {$size['name']} موجود بالفعل</div>";
        }
    }
    
    // التحقق من وجود تصنيفات
    $categories_count = $conn->query("SELECT COUNT(*) FROM categories WHERE is_active = 1")->fetchColumn();
    
    if ($categories_count == 0) {
        echo "<h3>إضافة تصنيفات أساسية:</h3>";
        $categories = [
            ['name' => 'فساتين سهرة', 'slug' => 'evening-dresses'],
            ['name' => 'فساتين زفاف', 'slug' => 'wedding-dresses'],
            ['name' => 'فساتين كوكتيل', 'slug' => 'cocktail-dresses'],
            ['name' => 'فساتين رسمية', 'slug' => 'formal-dresses']
        ];
        
        foreach ($categories as $category) {
            try {
                $stmt = $conn->prepare("INSERT INTO categories (name, slug, is_active) VALUES (?, ?, 1)");
                $stmt->execute([$category['name'], $category['slug']]);
                echo "<div style='color: green;'>✅ تم إضافة التصنيف: {$category['name']}</div>";
            } catch (Exception $e) {
                echo "<div style='color: red;'>❌ خطأ في إضافة التصنيف: {$category['name']}</div>";
            }
        }
    } else {
        echo "<h3>التصنيفات:</h3>";
        echo "<div style='color: blue;'>ℹ️ يوجد $categories_count تصنيف في النظام</div>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #2d5a2d;'>تم الانتهاء من إعداد البيانات التجريبية!</h3>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li><a href='login.php'>تسجيل الدخول للوحة التحكم</a></li>";
    echo "<li><a href='add-product.php'>إضافة منتج جديد</a></li>";
    echo "<li><a href='colors.php'>إدارة الألوان</a></li>";
    echo "<li><a href='sizes.php'>إدارة المقاسات</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #856404;'>ملاحظات مهمة:</h4>";
    echo "<ul style='color: #856404;'>";
    echo "<li>تأكد من إنشاء حساب المدير أولاً من <a href='create_admin.php'>هنا</a></li>";
    echo "<li>يمكنك حذف هذا الملف بعد الانتهاء من الإعداد</li>";
    echo "<li>لإضافة المزيد من الألوان والمقاسات، استخدم لوحة التحكم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إعداد البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من تشغيل ملف إعداد قاعدة البيانات أولاً: <a href='../fix_all_issues.php'>fix_all_issues.php</a></p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد البيانات التجريبية</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            text-decoration: none;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعداد البيانات التجريبية</h1>
        
        <div class="info">
            <h3>ما يتم إضافته:</h3>
            <ul>
                <li><strong>الألوان:</strong> 10 ألوان أساسية مع أكواد الألوان</li>
                <li><strong>المقاسات:</strong> مقاسات حروف (XS-XXL) ومقاسات أرقام (36-46)</li>
                <li><strong>التصنيفات:</strong> تصنيفات أساسية للفساتين</li>
            </ul>
        </div>
        
        <div class="nav-links">
            <h3>الخطوات التالية:</h3>
            <a href="create_admin.php">إنشاء حساب المدير</a>
            <a href="login.php">تسجيل الدخول</a>
            <a href="add-product.php">إضافة منتج</a>
            <a href="../index.php">الموقع الرئيسي</a>
        </div>
    </div>
</body>
</html>
