<?php
/**
 * ملف إعداد جدول صور المنتجات
 * يجب تشغيل هذا الملف مرة واحدة لإنشاء جدول product_images
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div style='font-family: Arial; padding: 20px; max-width: 800px; margin: 0 auto;'>";
    echo "<h2>إعداد جدول صور المنتجات</h2>";
    
    // إنشاء جدول product_images
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS product_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        image_path VARCHAR(255) NOT NULL,
        image_type ENUM('gallery', 'lifestyle') DEFAULT 'gallery',
        sort_order INT DEFAULT 0,
        alt_text VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        INDEX idx_product_type (product_id, image_type),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_table_sql);
    echo "<div style='color: green;'>✅ تم إنشاء جدول product_images بنجاح</div>";
    
    // التحقق من وجود عمود featured_image في جدول products
    $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'featured_image'");
    if ($check_column->rowCount() == 0) {
        $add_column_sql = "ALTER TABLE products ADD COLUMN featured_image VARCHAR(255) AFTER description";
        $conn->exec($add_column_sql);
        echo "<div style='color: green;'>✅ تم إضافة عمود featured_image إلى جدول products</div>";
    } else {
        echo "<div style='color: blue;'>ℹ️ عمود featured_image موجود بالفعل في جدول products</div>";
    }
    
    // إنشاء مجلدات الرفع
    $upload_dirs = [
        '../uploads',
        '../uploads/products'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<div style='color: green;'>✅ تم إنشاء مجلد: $dir</div>";
        } else {
            echo "<div style='color: blue;'>ℹ️ المجلد موجود بالفعل: $dir</div>";
        }
    }
    
    // إنشاء ملف .htaccess لحماية مجلد uploads
    $htaccess_content = "# منع تنفيذ ملفات PHP في مجلد الرفع
<Files *.php>
    Order Deny,Allow
    Deny from all
</Files>

# السماح بالصور فقط
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>";
    
    $htaccess_file = '../uploads/.htaccess';
    if (!file_exists($htaccess_file)) {
        file_put_contents($htaccess_file, $htaccess_content);
        echo "<div style='color: green;'>✅ تم إنشاء ملف .htaccess للحماية</div>";
    } else {
        echo "<div style='color: blue;'>ℹ️ ملف .htaccess موجود بالفعل</div>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #2d5a2d;'>تم الانتهاء من إعداد نظام الصور!</h3>";
    echo "<p>الآن يمكنك:</p>";
    echo "<ul>";
    echo "<li><a href='products.php'>إدارة المنتجات</a> - انقر على أيقونة الصور لإدارة صور أي منتج</li>";
    echo "<li><a href='add-product.php'>إضافة منتج جديد</a> ثم إدارة صوره</li>";
    echo "<li>رفع صور متعددة بتقنية السحب والإفلات</li>";
    echo "<li>ترتيب الصور بالسحب والإفلات</li>";
    echo "<li>تعيين الصورة المميزة للمنتج</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #856404;'>ميزات نظام الصور:</h4>";
    echo "<ul style='color: #856404;'>";
    echo "<li><strong>صور المعرض:</strong> الصور الأساسية للمنتج</li>";
    echo "<li><strong>صور الطبيعة:</strong> صور المنتج في بيئة طبيعية أو على موديل</li>";
    echo "<li><strong>رفع متعدد:</strong> رفع عدة صور في نفس الوقت</li>";
    echo "<li><strong>تحسين تلقائي:</strong> تقليل حجم الصور تلقائياً</li>";
    echo "<li><strong>ترتيب سهل:</strong> ترتيب الصور بالسحب والإفلات</li>";
    echo "<li><strong>صورة مميزة:</strong> تعيين الصورة الرئيسية للمنتج</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #0c5460;'>الأنواع المدعومة:</h4>";
    echo "<p style='color: #0c5460;'>JPG, JPEG, PNG, GIF, WEBP - حد أقصى 5MB لكل صورة</p>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إعداد جدول الصور: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من تشغيل ملف إعداد قاعدة البيانات أولاً: <a href='../fix_all_issues.php'>fix_all_issues.php</a></p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام الصور</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            text-decoration: none;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعداد نظام إدارة الصور</h1>
        
        <div class="info">
            <h3>ما يتم إعداده:</h3>
            <ul>
                <li><strong>جدول product_images:</strong> لحفظ معلومات صور المنتجات</li>
                <li><strong>مجلدات الرفع:</strong> مجلدات آمنة لحفظ الصور</li>
                <li><strong>ملفات الحماية:</strong> منع تنفيذ ملفات خطيرة</li>
                <li><strong>عمود featured_image:</strong> للصورة المميزة في جدول المنتجات</li>
            </ul>
        </div>
        
        <div class="nav-links">
            <h3>الخطوات التالية:</h3>
            <a href="login.php">تسجيل الدخول</a>
            <a href="products.php">إدارة المنتجات</a>
            <a href="add-product.php">إضافة منتج</a>
            <a href="../index.php">الموقع الرئيسي</a>
        </div>
    </div>
</body>
</html>
