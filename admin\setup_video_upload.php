<?php
/**
 * ملف إعداد نظام رفع الفيديو
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div style='font-family: Arial; padding: 20px; max-width: 800px; margin: 0 auto;'>";
    echo "<h2>إعداد نظام رفع الفيديو</h2>";
    
    // إضافة عمود video_file للمنتجات
    $add_video_column = "ALTER TABLE products ADD COLUMN IF NOT EXISTS video_file VARCHAR(255) DEFAULT NULL AFTER video_url";
    
    try {
        $conn->exec($add_video_column);
        echo "<div style='color: green;'>✅ تم إضافة عمود video_file لجدول المنتجات</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<div style='color: blue;'>ℹ️ عمود video_file موجود بالفعل</div>";
        } else {
            echo "<div style='color: red;'>❌ خطأ في إضافة عمود video_file: " . $e->getMessage() . "</div>";
        }
    }
    
    // إنشاء مجلد رفع الفيديو
    $video_dir = '../uploads/videos/';
    if (!file_exists($video_dir)) {
        if (mkdir($video_dir, 0755, true)) {
            echo "<div style='color: green;'>✅ تم إنشاء مجلد رفع الفيديو: $video_dir</div>";
        } else {
            echo "<div style='color: red;'>❌ فشل في إنشاء مجلد رفع الفيديو</div>";
        }
    } else {
        echo "<div style='color: blue;'>ℹ️ مجلد رفع الفيديو موجود بالفعل: $video_dir</div>";
    }
    
    // إنشاء ملف .htaccess لحماية مجلد الفيديو
    $htaccess_content = "# منع تنفيذ ملفات PHP في مجلد الفيديو
<Files *.php>
    Order Deny,Allow
    Deny from all
</Files>

# السماح بأنواع ملفات الفيديو فقط
<FilesMatch \"\\.(mp4|avi|mov|wmv|flv|webm|mkv)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع الوصول المباشر للملفات غير المسموحة
<FilesMatch \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">
    Order Deny,Allow
    Deny from all
</FilesMatch>";
    
    $htaccess_file = $video_dir . '.htaccess';
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        echo "<div style='color: green;'>✅ تم إنشاء ملف .htaccess لحماية مجلد الفيديو</div>";
    } else {
        echo "<div style='color: orange;'>⚠️ تعذر إنشاء ملف .htaccess</div>";
    }
    
    // عرض معلومات النظام
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #2d5a2d;'>تم إعداد نظام رفع الفيديو بنجاح!</h3>";
    echo "<ul style='color: #2d5a2d;'>";
    echo "<li>✅ عمود video_file في جدول المنتجات</li>";
    echo "<li>✅ مجلد رفع الفيديو: uploads/videos/</li>";
    echo "<li>✅ حماية أمنية بملف .htaccess</li>";
    echo "<li>✅ دعم أنواع الفيديو: MP4, AVI, MOV, WMV, FLV, WEBM, MKV</li>";
    echo "</ul>";
    echo "</div>";
    
    // معلومات الاستخدام
    echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #0c4a6e;'>كيفية الاستخدام:</h3>";
    echo "<ol style='color: #0c4a6e;'>";
    echo "<li>انتقل إلى صفحة إضافة/تعديل المنتج</li>";
    echo "<li>ستجد قسم رفع الفيديو في النموذج</li>";
    echo "<li>يمكنك رفع ملف فيديو أو إدخال رابط YouTube/Vimeo</li>";
    echo "<li>الفيديو سيظهر في صفحة المنتج تحت صور الطبيعة</li>";
    echo "</ol>";
    echo "</div>";
    
    // إحصائيات
    $products_with_video_url = $conn->query("SELECT COUNT(*) FROM products WHERE video_url IS NOT NULL AND video_url != ''")->fetchColumn();
    $products_with_video_file = $conn->query("SELECT COUNT(*) FROM products WHERE video_file IS NOT NULL AND video_file != ''")->fetchColumn();
    
    echo "<div style='background: #fef3c7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #92400e;'>الإحصائيات:</h3>";
    echo "<ul style='color: #92400e;'>";
    echo "<li>📹 منتجات بروابط فيديو: $products_with_video_url</li>";
    echo "<li>🎬 منتجات بملفات فيديو مرفوعة: $products_with_video_file</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إعداد نظام رفع الفيديو: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام رفع الفيديو</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            text-decoration: none;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 إعداد نظام رفع الفيديو</h1>
        
        <div class="nav-links">
            <h3>الخطوات التالية:</h3>
            <a href="add-product.php">إضافة منتج جديد</a>
            <a href="products.php">إدارة المنتجات</a>
            <a href="../index.php">الموقع الرئيسي</a>
            <a href="setup_images_table.php">إعداد نظام الصور</a>
        </div>
    </div>
</body>
</html>
