<?php
require_once 'auth.php';
require_once '../config/database.php';

header('Content-Type: application/json');

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'طريقة الطلب غير صحيحة';
    echo json_encode($response);
    exit;
}

$image_ids_json = $_POST['image_ids'] ?? '';
$image_ids = json_decode($image_ids_json, true);

if (empty($image_ids) || !is_array($image_ids)) {
    $response['message'] = 'بيانات الترتيب غير صحيحة';
    echo json_encode($response);
    exit;
}

try {
    $conn->beginTransaction();
    
    // تحديث ترتيب الصور
    $stmt = $conn->prepare("UPDATE product_images SET sort_order = ? WHERE id = ?");
    
    foreach ($image_ids as $index => $image_id) {
        $sort_order = $index + 1;
        $stmt->execute([$sort_order, intval($image_id)]);
    }
    
    $conn->commit();
    
    $response['success'] = true;
    $response['message'] = 'تم تحديث ترتيب الصور بنجاح';
    
} catch (Exception $e) {
    $conn->rollBack();
    $response['message'] = 'حدث خطأ أثناء تحديث الترتيب: ' . $e->getMessage();
}

echo json_encode($response);
?>
