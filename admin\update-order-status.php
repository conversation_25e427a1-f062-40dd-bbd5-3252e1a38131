<?php
require_once 'auth.php';
require_once '../config/database.php';

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: orders.php');
    exit();
}

$order_id = (int)$_GET['id'];

// جلب تفاصيل الطلب
$order_query = "SELECT o.*, 
                       c.name as city_name, 
                       a.name as area_name
                FROM orders o 
                LEFT JOIN cities c ON o.city_id = c.id 
                LEFT JOIN areas a ON o.area_id = a.id 
                WHERE o.id = ?";

$order_stmt = $conn->prepare($order_query);
$order_stmt->execute([$order_id]);
$order = $order_stmt->fetch();

if (!$order) {
    header('Location: orders.php');
    exit();
}

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes'] ?? '');
    $tracking_number = trim($_POST['tracking_number'] ?? '');
    
    try {
        // تحديث حالة الطلب
        $update_query = "UPDATE orders SET 
                        status = ?, 
                        notes = ?, 
                        tracking_number = ?,
                        updated_at = NOW() 
                        WHERE id = ?";
        
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->execute([$new_status, $notes, $tracking_number, $order_id]);
        
        // إضافة سجل في تاريخ الطلب
        $history_query = "INSERT INTO order_status_history 
                         (order_id, status, notes, admin_id, created_at) 
                         VALUES (?, ?, ?, ?, NOW())";
        
        $history_stmt = $conn->prepare($history_query);
        $history_stmt->execute([$order_id, $new_status, $notes, $admin['id']]);
        
        $success_message = 'تم تحديث حالة الطلب بنجاح';
        
        // تحديث بيانات الطلب
        $order['status'] = $new_status;
        $order['notes'] = $notes;
        $order['tracking_number'] = $tracking_number;
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث حالة الطلب: ' . $e->getMessage();
    }
}

// جلب تاريخ حالات الطلب
$history_query = "SELECT osh.*, au.username as admin_name
                  FROM order_status_history osh
                  LEFT JOIN admin_users au ON osh.admin_id = au.id
                  WHERE osh.order_id = ?
                  ORDER BY osh.created_at DESC";

$history_stmt = $conn->prepare($history_query);
$history_stmt->execute([$order_id]);
$status_history = $history_stmt->fetchAll();

// دالة لترجمة حالة الطلب
function getStatusText($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التجهيز',
        'shipped' => 'تم الشحن',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي'
    ];
    return $statuses[$status] ?? $status;
}

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'confirmed' => 'info',
        'processing' => 'primary',
        'shipped' => 'success',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

// دالة لتحديد أيقونة الحالة
function getStatusIcon($status) {
    $icons = [
        'pending' => 'fas fa-clock',
        'confirmed' => 'fas fa-check',
        'processing' => 'fas fa-cog',
        'shipped' => 'fas fa-truck',
        'delivered' => 'fas fa-check-circle',
        'cancelled' => 'fas fa-times'
    ];
    return $icons[$status] ?? 'fas fa-question';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث حالة الطلب #<?php echo htmlspecialchars($order['order_number']); ?> - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar .logo {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar .logo p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .sidebar-nav .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0;
            color: #333;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-card h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .status-timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .status-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.75rem;
            top: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #dee2e6;
        }
        
        .timeline-item.current::before {
            background: #28a745;
            box-shadow: 0 0 0 3px #28a745;
        }
        
        .timeline-date {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .timeline-status {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .timeline-admin {
            font-size: 0.85rem;
            color: #666;
        }
        
        .form-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }
        
        .form-section h5 {
            color: white;
            margin-bottom: 1.5rem;
        }
        
        .form-section .form-label {
            color: rgba(255,255,255,0.9);
            font-weight: 500;
        }
        
        .form-section .form-control,
        .form-section .form-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
        }
        
        .form-section .form-control::placeholder {
            color: rgba(255,255,255,0.7);
        }
        
        .form-section .form-control:focus,
        .form-section .form-select:focus {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-crown me-2"></i>لوحة التحكم</h3>
            <p>متجر فساتين السهرة</p>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    الرئيسية
                </a>
            </div>
            <div class="nav-item">
                <a href="products.php" class="nav-link">
                    <i class="fas fa-tshirt"></i>
                    إدارة المنتجات
                </a>
            </div>
            <div class="nav-item">
                <a href="add-product.php" class="nav-link">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="categories.php" class="nav-link">
                    <i class="fas fa-tags"></i>
                    إدارة الأقسام
                </a>
            </div>
            <div class="nav-item">
                <a href="orders.php" class="nav-link active">
                    <i class="fas fa-shopping-cart"></i>
                    الطلبات
                </a>
            </div>
            <div class="nav-item">
                <a href="colors.php" class="nav-link">
                    <i class="fas fa-palette"></i>
                    إدارة الألوان
                </a>
            </div>
            <div class="nav-item">
                <a href="sizes.php" class="nav-link">
                    <i class="fas fa-ruler"></i>
                    إدارة المقاسات
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </div>
            <div class="nav-item">
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <h1>
                    <i class="fas fa-edit"></i>
                    تحديث حالة الطلب #<?php echo htmlspecialchars($order['order_number']); ?>
                </h1>
                <div>
                    <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                    <a href="orders.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للطلبات
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Current Order Info -->
            <div class="col-md-4">
                <div class="info-card">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات الطلب الحالية</h5>
                    <div class="mb-3">
                        <strong>رقم الطلب:</strong><br>
                        #<?php echo htmlspecialchars($order['order_number']); ?>
                    </div>
                    <div class="mb-3">
                        <strong>العميل:</strong><br>
                        <?php echo htmlspecialchars($order['customer_name']); ?><br>
                        <small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                    </div>
                    <div class="mb-3">
                        <strong>العنوان:</strong><br>
                        <?php echo htmlspecialchars($order['city_name'] . ' - ' . $order['area_name']); ?><br>
                        <small class="text-muted"><?php echo htmlspecialchars($order['shipping_address']); ?></small>
                    </div>
                    <div class="mb-3">
                        <strong>الحالة الحالية:</strong><br>
                        <span class="badge bg-<?php echo getStatusColor($order['status']); ?> status-badge">
                            <i class="<?php echo getStatusIcon($order['status']); ?> me-1"></i>
                            <?php echo getStatusText($order['status']); ?>
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>المجموع:</strong><br>
                        <span class="h5 text-success"><?php echo number_format($order['total_amount'], 2); ?> ر.س</span>
                    </div>
                    <?php if ($order['tracking_number']): ?>
                    <div class="mb-3">
                        <strong>رقم التتبع:</strong><br>
                        <code><?php echo htmlspecialchars($order['tracking_number']); ?></code>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Update Form -->
            <div class="col-md-8">
                <div class="form-section">
                    <h5><i class="fas fa-edit me-2"></i>تحديث حالة الطلب</h5>
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة الجديدة</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>
                                            في الانتظار
                                        </option>
                                        <option value="confirmed" <?php echo $order['status'] === 'confirmed' ? 'selected' : ''; ?>>
                                            مؤكد
                                        </option>
                                        <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>
                                            قيد التجهيز
                                        </option>
                                        <option value="shipped" <?php echo $order['status'] === 'shipped' ? 'selected' : ''; ?>>
                                            تم الشحن
                                        </option>
                                        <option value="delivered" <?php echo $order['status'] === 'delivered' ? 'selected' : ''; ?>>
                                            تم التسليم
                                        </option>
                                        <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>
                                            ملغي
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tracking_number" class="form-label">رقم التتبع (اختياري)</label>
                                    <input type="text" class="form-control" id="tracking_number" name="tracking_number"
                                           value="<?php echo htmlspecialchars($order['tracking_number']); ?>"
                                           placeholder="أدخل رقم التتبع للشحنة">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات التحديث</label>
                            <textarea class="form-control" id="notes" name="notes" rows="4"
                                      placeholder="أضف ملاحظات حول تحديث الحالة..."><?php echo htmlspecialchars($order['notes']); ?></textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" name="update_status" class="btn btn-light btn-lg">
                                <i class="fas fa-save me-1"></i>تحديث الحالة
                            </button>
                            <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Status History -->
        <?php if (!empty($status_history)): ?>
        <div class="info-card">
            <h5><i class="fas fa-history me-2"></i>تاريخ حالات الطلب</h5>
            <div class="status-timeline">
                <?php foreach ($status_history as $index => $history): ?>
                    <div class="timeline-item <?php echo $index === 0 ? 'current' : ''; ?>">
                        <div class="timeline-date">
                            <?php echo date('Y-m-d H:i:s', strtotime($history['created_at'])); ?>
                        </div>
                        <div class="timeline-status">
                            <i class="<?php echo getStatusIcon($history['status']); ?> me-2 text-<?php echo getStatusColor($history['status']); ?>"></i>
                            <?php echo getStatusText($history['status']); ?>
                        </div>
                        <?php if ($history['notes']): ?>
                            <div class="timeline-notes text-muted">
                                <?php echo nl2br(htmlspecialchars($history['notes'])); ?>
                            </div>
                        <?php endif; ?>
                        <div class="timeline-admin">
                            بواسطة: <?php echo htmlspecialchars($history['admin_name'] ?? 'غير محدد'); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="info-card">
            <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
            <div class="row">
                <?php if ($order['status'] === 'pending'): ?>
                    <div class="col-md-3">
                        <form method="POST" class="d-grid">
                            <input type="hidden" name="status" value="confirmed">
                            <input type="hidden" name="notes" value="تم تأكيد الطلب">
                            <button type="submit" name="update_status" class="btn btn-success"
                                    onclick="return confirm('هل تريد تأكيد هذا الطلب؟')">
                                <i class="fas fa-check me-1"></i>تأكيد الطلب
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="POST" class="d-grid">
                            <input type="hidden" name="status" value="cancelled">
                            <input type="hidden" name="notes" value="تم إلغاء الطلب">
                            <button type="submit" name="update_status" class="btn btn-danger"
                                    onclick="return confirm('هل تريد إلغاء هذا الطلب؟')">
                                <i class="fas fa-times me-1"></i>إلغاء الطلب
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <?php if ($order['status'] === 'confirmed'): ?>
                    <div class="col-md-3">
                        <form method="POST" class="d-grid">
                            <input type="hidden" name="status" value="processing">
                            <input type="hidden" name="notes" value="بدء تجهيز الطلب">
                            <button type="submit" name="update_status" class="btn btn-info"
                                    onclick="return confirm('هل تريد بدء تجهيز هذا الطلب؟')">
                                <i class="fas fa-cog me-1"></i>بدء التجهيز
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <?php if ($order['status'] === 'processing'): ?>
                    <div class="col-md-3">
                        <form method="POST" class="d-grid">
                            <input type="hidden" name="status" value="shipped">
                            <input type="hidden" name="notes" value="تم شحن الطلب">
                            <button type="submit" name="update_status" class="btn btn-primary"
                                    onclick="return confirm('هل تم شحن هذا الطلب؟')">
                                <i class="fas fa-truck me-1"></i>تم الشحن
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <?php if ($order['status'] === 'shipped'): ?>
                    <div class="col-md-3">
                        <form method="POST" class="d-grid">
                            <input type="hidden" name="status" value="delivered">
                            <input type="hidden" name="notes" value="تم تسليم الطلب للعميل">
                            <button type="submit" name="update_status" class="btn btn-success"
                                    onclick="return confirm('هل تم تسليم هذا الطلب؟')">
                                <i class="fas fa-check-circle me-1"></i>تم التسليم
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <div class="col-md-3">
                    <a href="print-order.php?id=<?php echo $order['id']; ?>" target="_blank" class="btn btn-outline-secondary d-grid">
                        <i class="fas fa-print me-1"></i>طباعة الفاتورة
                    </a>
                </div>

                <div class="col-md-3">
                    <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-primary d-grid">
                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الحقول بناءً على الحالة المختارة
        document.getElementById('status').addEventListener('change', function() {
            const status = this.value;
            const trackingField = document.getElementById('tracking_number');
            const notesField = document.getElementById('notes');

            // إظهار/إخفاء حقل رقم التتبع
            if (status === 'shipped' || status === 'delivered') {
                trackingField.parentElement.style.display = 'block';
                trackingField.required = status === 'shipped';
            } else {
                trackingField.parentElement.style.display = 'none';
                trackingField.required = false;
            }

            // تحديث الملاحظات التلقائية
            const autoNotes = {
                'pending': 'الطلب في انتظار المراجعة',
                'confirmed': 'تم تأكيد الطلب',
                'processing': 'جاري تجهيز الطلب',
                'shipped': 'تم شحن الطلب',
                'delivered': 'تم تسليم الطلب للعميل',
                'cancelled': 'تم إلغاء الطلب'
            };

            if (notesField.value.trim() === '' || Object.values(autoNotes).includes(notesField.value.trim())) {
                notesField.value = autoNotes[status] || '';
            }
        });

        // تشغيل التحديث عند تحميل الصفحة
        document.getElementById('status').dispatchEvent(new Event('change'));
    </script>
</body>
</html>
