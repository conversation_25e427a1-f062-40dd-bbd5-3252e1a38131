<?php
require_once 'auth.php';
require_once '../config/database.php';

header('Content-Type: application/json');

$admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$response = ['success' => false, 'message' => '', 'images' => []];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'طريقة الطلب غير صحيحة';
    echo json_encode($response);
    exit;
}

$product_id = intval($_POST['product_id'] ?? 0);
$image_type = $_POST['image_type'] ?? 'gallery'; // gallery or lifestyle

if ($product_id <= 0) {
    $response['message'] = 'معرف المنتج غير صحيح';
    echo json_encode($response);
    exit;
}

// التحقق من وجود المنتج
try {
    $stmt = $conn->prepare("SELECT id FROM products WHERE id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    if (!$stmt->fetch()) {
        $response['message'] = 'المنتج غير موجود';
        echo json_encode($response);
        exit;
    }
} catch (Exception $e) {
    $response['message'] = 'خطأ في التحقق من المنتج';
    echo json_encode($response);
    exit;
}

// إعدادات رفع الصور
$upload_dir = '../uploads/products/';
$allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$max_file_size = 5 * 1024 * 1024; // 5MB

// إنشاء مجلد الرفع إذا لم يكن موجوداً
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// معالجة الصور المرفوعة
if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
    $uploaded_images = [];
    $files = $_FILES['images'];
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file_name = $files['name'][$i];
            $file_tmp = $files['tmp_name'][$i];
            $file_size = $files['size'][$i];
            
            // التحقق من نوع الملف
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            if (!in_array($file_ext, $allowed_types)) {
                continue; // تخطي الملفات غير المدعومة
            }
            
            // التحقق من حجم الملف
            if ($file_size > $max_file_size) {
                continue; // تخطي الملفات الكبيرة
            }
            
            // إنشاء اسم ملف فريد
            $new_file_name = 'product_' . $product_id . '_' . time() . '_' . $i . '.' . $file_ext;
            $file_path = $upload_dir . $new_file_name;
            
            // رفع الملف
            if (move_uploaded_file($file_tmp, $file_path)) {
                // تحسين الصورة (اختياري)
                optimizeImage($file_path, $file_ext);
                
                // حفظ معلومات الصورة في قاعدة البيانات
                try {
                    // الحصول على أعلى ترتيب
                    $sort_stmt = $conn->prepare("SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM product_images WHERE product_id = ? AND image_type = ?");
                    $sort_stmt->execute([$product_id, $image_type]);
                    $sort_order = $sort_stmt->fetch()['next_order'];
                    
                    $stmt = $conn->prepare("INSERT INTO product_images (product_id, image_path, image_type, sort_order, alt_text) VALUES (?, ?, ?, ?, ?)");
                    $alt_text = 'صورة المنتج ' . ($i + 1);
                    $stmt->execute([$product_id, 'products/' . $new_file_name, $image_type, $sort_order, $alt_text]);
                    
                    $image_id = $conn->lastInsertId();
                    
                    $uploaded_images[] = [
                        'id' => $image_id,
                        'path' => 'products/' . $new_file_name,
                        'url' => '../uploads/products/' . $new_file_name,
                        'type' => $image_type,
                        'sort_order' => $sort_order,
                        'alt_text' => $alt_text
                    ];
                    
                    // تحديث الصورة المميزة إذا كانت هذه أول صورة
                    if ($i === 0 && $image_type === 'gallery') {
                        $update_stmt = $conn->prepare("UPDATE products SET featured_image = ? WHERE id = ? AND (featured_image IS NULL OR featured_image = '')");
                        $update_stmt->execute(['products/' . $new_file_name, $product_id]);
                    }
                    
                } catch (Exception $e) {
                    // حذف الملف إذا فشل حفظه في قاعدة البيانات
                    unlink($file_path);
                }
            }
        }
    }
    
    if (!empty($uploaded_images)) {
        $response['success'] = true;
        $response['message'] = 'تم رفع ' . count($uploaded_images) . ' صورة بنجاح';
        $response['images'] = $uploaded_images;
    } else {
        $response['message'] = 'لم يتم رفع أي صورة. تأكد من نوع وحجم الملفات.';
    }
} else {
    $response['message'] = 'لم يتم اختيار أي صور';
}

echo json_encode($response);

/**
 * تحسين الصورة وتقليل حجمها
 */
function optimizeImage($file_path, $file_ext) {
    $max_width = 1200;
    $max_height = 1200;
    $quality = 85;
    
    switch ($file_ext) {
        case 'jpg':
        case 'jpeg':
            $image = imagecreatefromjpeg($file_path);
            break;
        case 'png':
            $image = imagecreatefrompng($file_path);
            break;
        case 'gif':
            $image = imagecreatefromgif($file_path);
            break;
        case 'webp':
            $image = imagecreatefromwebp($file_path);
            break;
        default:
            return false;
    }
    
    if (!$image) return false;
    
    $width = imagesx($image);
    $height = imagesy($image);
    
    // حساب الأبعاد الجديدة
    if ($width > $max_width || $height > $max_height) {
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);
        
        $new_image = imagecreatetruecolor($new_width, $new_height);
        
        // الحفاظ على الشفافية للـ PNG
        if ($file_ext === 'png') {
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
        }
        
        imagecopyresampled($new_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        
        // حفظ الصورة المحسنة
        switch ($file_ext) {
            case 'jpg':
            case 'jpeg':
                imagejpeg($new_image, $file_path, $quality);
                break;
            case 'png':
                imagepng($new_image, $file_path, 9);
                break;
            case 'gif':
                imagegif($new_image, $file_path);
                break;
            case 'webp':
                imagewebp($new_image, $file_path, $quality);
                break;
        }
        
        imagedestroy($new_image);
    }
    
    imagedestroy($image);
    return true;
}
?>
