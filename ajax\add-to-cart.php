<?php
session_start();
require_once '../config/database.php';
require_once '../includes/Cart.php';
require_once '../includes/Product.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// قراءة البيانات من POST أو JSON
$product_id = 0;
$color_id = null;
$size_id = null;
$quantity = 1;

if (isset($_POST['product_id'])) {
    // البيانات من FormData
    $product_id = (int)$_POST['product_id'];
    $color_id = isset($_POST['color_id']) ? (int)$_POST['color_id'] : null;
    $size_id = isset($_POST['size_id']) ? (int)$_POST['size_id'] : null;
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
} else {
    // البيانات من JSON
    $input = json_decode(file_get_contents('php://input'), true);
    if ($input) {
        $product_id = isset($input['product_id']) ? (int)$input['product_id'] : 0;
        $color_id = isset($input['color_id']) ? (int)$input['color_id'] : null;
        $size_id = isset($input['size_id']) ? (int)$input['size_id'] : null;
        $quantity = isset($input['quantity']) ? (int)$input['quantity'] : 1;
    }
}

// التحقق من البيانات
if ($product_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج غير صحيح']);
    exit();
}

if ($quantity <= 0 || $quantity > 10) {
    echo json_encode(['success' => false, 'message' => 'الكمية غير صحيحة']);
    exit();
}

try {
    // التحقق من وجود جدول cart
    $database = new Database();
    $conn = $database->getConnection();

    $check_cart_table = "SHOW TABLES LIKE 'cart'";
    $table_exists = $conn->query($check_cart_table)->rowCount() > 0;

    if (!$table_exists) {
        // إنشاء جدول cart
        $create_cart = "CREATE TABLE cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(255) NOT NULL,
            product_id INT NOT NULL,
            color_id INT NULL,
            size_id INT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_product_id (product_id)
        )";

        $conn->exec($create_cart);
    }

    // التحقق من وجود المنتج وحالة التوفر
    $product = new Product();
    $product->id = $product_id;

    if (!$product->read_single()) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
        exit();
    }

    if ($product->stock_status !== 'in_stock') {
        echo json_encode(['success' => false, 'message' => 'المنتج غير متوفر حالياً']);
        exit();
    }

    // إضافة المنتج إلى السلة
    $cart = new Cart();
    $cart->product_id = $product_id;

    // التحقق من وجود الألوان والمقاسات قبل إضافتها
    if ($color_id && $color_id > 0) {
        // التحقق من وجود اللون في قاعدة البيانات
        $color_check = "SELECT id FROM colors WHERE id = :color_id AND is_active = 1";
        $color_stmt = $conn->prepare($color_check);
        $color_stmt->bindParam(':color_id', $color_id);
        $color_stmt->execute();

        if ($color_stmt->fetch()) {
            $cart->color_id = $color_id;
        } else {
            $cart->color_id = null; // اللون غير موجود، تجاهله
        }
    } else {
        $cart->color_id = null;
    }

    if ($size_id && $size_id > 0) {
        // التحقق من وجود المقاس في قاعدة البيانات
        $size_check = "SELECT id FROM sizes WHERE id = :size_id AND is_active = 1";
        $size_stmt = $conn->prepare($size_check);
        $size_stmt->bindParam(':size_id', $size_id);
        $size_stmt->execute();

        if ($size_stmt->fetch()) {
            $cart->size_id = $size_id;
        } else {
            $cart->size_id = null; // المقاس غير موجود، تجاهله
        }
    } else {
        $cart->size_id = null;
    }

    $cart->quantity = $quantity;

    if ($cart->add_to_cart()) {
        $cart_count = $cart->get_cart_count();
        $cart_total = $cart->get_cart_total();

        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المنتج إلى السلة بنجاح',
            'cart_count' => $cart_count,
            'cart_total' => $cart_total
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في إضافة المنتج إلى السلة']);
    }
    
} catch (Exception $e) {
    // في بيئة التطوير، اعرض تفاصيل الخطأ
    $error_message = 'حدث خطأ في النظام';
    if (defined('DEBUG') && DEBUG) {
        $error_message .= ': ' . $e->getMessage();
    }

    // تسجيل الخطأ في ملف log
    error_log("Add to cart error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => $error_message,
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
