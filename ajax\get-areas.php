<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$city_id = isset($_POST['city_id']) ? (int)$_POST['city_id'] : 0;

if ($city_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المدينة غير صحيح']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();

    // التحقق من وجود جدول المناطق، وإنشاء بيانات تجريبية إذا لم يكن موجوداً
    $check_table = "SHOW TABLES LIKE 'areas'";
    $table_exists = $conn->query($check_table)->rowCount() > 0;

    if (!$table_exists) {
        // إنشاء جدول المناطق
        $create_areas_table = "CREATE TABLE areas (
            id INT AUTO_INCREMENT PRIMARY KEY,
            city_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_city_id (city_id)
        )";
        $conn->exec($create_areas_table);

        // إدراج بيانات تجريبية للمناطق
        $sample_areas = [
            // مناطق الرياض (city_id = 1)
            [1, 'الملز'],
            [1, 'العليا'],
            [1, 'الورود'],
            [1, 'النرجس'],
            [1, 'الياسمين'],
            [1, 'الربوة'],
            [1, 'المروج'],
            [1, 'الصحافة'],
            [1, 'الملقا'],
            [1, 'الفلاح'],

            // مناطق جدة (city_id = 2)
            [2, 'الروضة'],
            [2, 'الزهراء'],
            [2, 'النزهة'],
            [2, 'الصفا'],
            [2, 'المروة'],
            [2, 'الشاطئ'],
            [2, 'الكورنيش'],
            [2, 'البلد'],
            [2, 'الحمراء'],
            [2, 'السلامة'],

            // مناطق الدمام (city_id = 3)
            [3, 'الفيصلية'],
            [3, 'الشاطئ'],
            [3, 'الجلوية'],
            [3, 'الأندلس'],
            [3, 'النور'],
            [3, 'الضباب'],
            [3, 'الفردوس'],
            [3, 'الواحة'],
            [3, 'المنار'],
            [3, 'الصناعية']
        ];

        $insert_area = "INSERT INTO areas (city_id, name) VALUES (?, ?)";
        $area_stmt = $conn->prepare($insert_area);

        foreach ($sample_areas as $area) {
            $area_stmt->execute($area);
        }
    }

    $query = "SELECT id, name FROM areas WHERE city_id = :city_id AND is_active = 1 ORDER BY name";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':city_id', $city_id);
    $stmt->execute();

    $areas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'areas' => $areas
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في جلب المناطق: ' . $e->getMessage()
    ]);
}
?>
