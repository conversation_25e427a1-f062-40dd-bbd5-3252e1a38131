<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$country_id = isset($_POST['country_id']) ? (int)$_POST['country_id'] : 0;

if ($country_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف البلد غير صحيح']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();

    $query = "SELECT id, name FROM cities WHERE country_id = :country_id AND is_active = 1 ORDER BY name";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':country_id', $country_id);
    $stmt->execute();

    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'cities' => $cities
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب المدن',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
