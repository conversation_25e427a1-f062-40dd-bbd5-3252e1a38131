<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['product_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج مطلوب']);
    exit();
}

$product_id = (int)$input['product_id'];

if ($product_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج غير صحيح']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // جلب المقاسات المتاحة للمنتج
    $query = "SELECT s.id, s.name, s.sort_order
              FROM sizes s
              INNER JOIN product_sizes ps ON s.id = ps.size_id
              WHERE ps.product_id = :product_id
              AND s.is_active = 1
              ORDER BY s.sort_order ASC, s.name ASC";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':product_id', $product_id);
    $stmt->execute();
    
    $sizes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'sizes' => $sizes,
        'count' => count($sizes)
    ]);
    
} catch (Exception $e) {
    error_log("Get product sizes error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب المقاسات',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
