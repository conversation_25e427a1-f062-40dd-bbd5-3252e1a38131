<?php
session_start();
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;

if ($product_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج غير صحيح']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // التحقق من وجود جدول المفضلة وإنشاؤه إذا لم يكن موجوداً
    $check_table = "SHOW TABLES LIKE 'wishlist'";
    $table_exists = $conn->query($check_table)->rowCount() > 0;
    
    if (!$table_exists) {
        $create_table = "CREATE TABLE wishlist (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(255) NOT NULL,
            product_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_wishlist (session_id, product_id),
            INDEX idx_session_id (session_id),
            INDEX idx_product_id (product_id)
        )";
        $conn->exec($create_table);
    }
    
    // الحصول على معرف الجلسة
    $session_id = session_id();
    
    // التحقق من وجود المنتج في المفضلة
    $check_query = "SELECT id FROM wishlist WHERE session_id = :session_id AND product_id = :product_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':session_id', $session_id);
    $check_stmt->bindParam(':product_id', $product_id);
    $check_stmt->execute();
    
    $exists = $check_stmt->fetch();
    
    if ($exists) {
        // إزالة من المفضلة
        $delete_query = "DELETE FROM wishlist WHERE session_id = :session_id AND product_id = :product_id";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bindParam(':session_id', $session_id);
        $delete_stmt->bindParam(':product_id', $product_id);
        
        if ($delete_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'added' => false,
                'message' => 'تم إزالة المنتج من المفضلة'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إزالة المنتج من المفضلة']);
        }
    } else {
        // إضافة للمفضلة
        $insert_query = "INSERT INTO wishlist (session_id, product_id) VALUES (:session_id, :product_id)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bindParam(':session_id', $session_id);
        $insert_stmt->bindParam(':product_id', $product_id);
        
        if ($insert_stmt->execute()) {
            echo json_encode([
                'success' => true,
                'added' => true,
                'message' => 'تم إضافة المنتج للمفضلة'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إضافة المنتج للمفضلة']);
        }
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
