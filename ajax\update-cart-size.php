<?php
session_start();
require_once '../config/database.php';
require_once '../includes/Cart.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['cart_id']) || !isset($input['size_id'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit();
}

$cart_id = (int)$input['cart_id'];
$size_id = (int)$input['size_id'];

if ($cart_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف السلة غير صحيح']);
    exit();
}

if ($size_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف المقاس غير صحيح']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // التحقق من وجود المقاس
    $size_check = "SELECT id, name FROM sizes WHERE id = :size_id AND is_active = 1";
    $stmt = $conn->prepare($size_check);
    $stmt->bindParam(':size_id', $size_id);
    $stmt->execute();
    
    $size = $stmt->fetch();
    if (!$size) {
        echo json_encode(['success' => false, 'message' => 'المقاس غير موجود أو غير متاح']);
        exit();
    }
    
    // تحديث المقاس في السلة
    $cart = new Cart();
    $cart->id = $cart_id;
    $cart->size_id = $size_id;
    
    if ($cart->update_size()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المقاس بنجاح',
            'size_name' => $size['name']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في تحديث المقاس']);
    }
    
} catch (Exception $e) {
    error_log("Update cart size error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في تحديث المقاس',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
