<?php
session_start();
require_once '../config/database.php';
require_once '../includes/Cart.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$action = isset($_POST['action']) ? $_POST['action'] : '';
$cart_id = isset($_POST['cart_id']) ? (int)$_POST['cart_id'] : 0;

if ($cart_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف العنصر غير صحيح']);
    exit();
}

try {
    $cart = new Cart();
    
    switch ($action) {
        case 'update_quantity':
            $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
            
            if ($quantity <= 0 || $quantity > 10) {
                echo json_encode(['success' => false, 'message' => 'الكمية يجب أن تكون بين 1 و 10']);
                exit();
            }
            
            $cart->id = $cart_id;
            $cart->quantity = $quantity;
            
            if ($cart->update_quantity()) {
                $cart_count = $cart->get_cart_count();
                $cart_total = $cart->get_cart_total();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تحديث الكمية بنجاح',
                    'cart_count' => $cart_count,
                    'cart_total' => $cart_total
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في تحديث الكمية']);
            }
            break;
            
        case 'remove_item':
            $cart->id = $cart_id;
            
            if ($cart->remove_from_cart()) {
                $cart_count = $cart->get_cart_count();
                $cart_total = $cart->get_cart_total();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم حذف المنتج من السلة',
                    'cart_count' => $cart_count,
                    'cart_total' => $cart_total
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في حذف المنتج']);
            }
            break;
            
        case 'clear_cart':
            if ($cart->clear_cart()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إفراغ السلة بنجاح',
                    'cart_count' => 0,
                    'cart_total' => 0
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في إفراغ السلة']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'عملية غير مدعومة']);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في النظام',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
