<?php
session_start();
require_once 'config/database.php';
require_once 'includes/Cart.php';

$page_title = 'سلة التسوق';

// إنشاء كائن السلة
$cart = new Cart();

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_quantity':
                $cart_id = sanitize_input($_POST['cart_id']);
                $quantity = (int)sanitize_input($_POST['quantity']);
                
                if ($quantity > 0) {
                    $cart->id = $cart_id;
                    $cart->quantity = $quantity;
                    if ($cart->update_quantity()) {
                        $_SESSION['success_message'] = 'تم تحديث الكمية بنجاح';
                    } else {
                        $_SESSION['error_message'] = 'فشل في تحديث الكمية';
                    }
                }
                break;
                
            case 'remove_item':
                $cart_id = sanitize_input($_POST['cart_id']);
                $cart->id = $cart_id;
                if ($cart->remove_from_cart()) {
                    $_SESSION['success_message'] = 'تم حذف المنتج من السلة';
                } else {
                    $_SESSION['error_message'] = 'فشل في حذف المنتج';
                }
                break;
                
            case 'clear_cart':
                if ($cart->clear_cart()) {
                    $_SESSION['success_message'] = 'تم إفراغ السلة بنجاح';
                } else {
                    $_SESSION['error_message'] = 'فشل في إفراغ السلة';
                }
                break;

            case 'update_size':
                $cart_id = sanitize_input($_POST['cart_id']);
                $size_id = (int)sanitize_input($_POST['size_id']);

                if ($size_id > 0) {
                    $cart->id = $cart_id;
                    $cart->size_id = $size_id;
                    if ($cart->update_size()) {
                        $_SESSION['success_message'] = 'تم تحديث المقاس بنجاح';
                    } else {
                        $_SESSION['error_message'] = 'فشل في تحديث المقاس';
                    }
                }
                break;
        }
        
        header('Location: cart.php');
        exit();
    }
}

// جلب محتويات السلة
$cart_items_result = $cart->get_cart_items();
$cart_items = [];
while ($row = $cart_items_result->fetch(PDO::FETCH_ASSOC)) {
    $cart_items[] = $row;
}

// حساب الإجماليات
$subtotal = 0;
$total_items = 0;

foreach ($cart_items as $item) {
    $item_price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
    $subtotal += $item_price * $item['quantity'];
    $total_items += $item['quantity'];
}

// جلب شركات الشحن من قاعدة البيانات
try {
    $database = new Database();
    $conn = $database->getConnection();

    $shipping_query = "SELECT * FROM shipping_companies WHERE is_active = 1 ORDER BY cost";
    $shipping_stmt = $conn->prepare($shipping_query);
    $shipping_stmt->execute();
    $shipping_companies = $shipping_stmt->fetchAll(PDO::FETCH_ASSOC);

    // استخدام أرخص شركة شحن كافتراضي
    $default_shipping_cost = !empty($shipping_companies) ? $shipping_companies[0]['cost'] : 50;

} catch (Exception $e) {
    $shipping_companies = [];
    $default_shipping_cost = 50;
}

// رسوم الشحن (شحن مجاني للطلبات أكثر من 500 ريال)
$shipping_cost = $subtotal >= 500 ? 0 : $default_shipping_cost;
$total = $subtotal + $shipping_cost;

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

include 'includes/header.php';
?>

<div class="cart-container">
    <div class="container">
        
        <?php if (empty($cart_items)): ?>
        <!-- Empty Cart -->
        <div class="empty-cart">
            <div class="empty-cart-content">
                <div class="empty-cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h2>سلة التسوق فارغة</h2>
                <p>لم تقومي بإضافة أي منتجات إلى سلة التسوق بعد</p>
                <div class="empty-cart-actions">
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-shopping-bag"></i>
                        ابدئي التسوق الآن
                    </a>
                    <a href="index.php" class="btn btn-outline">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
        
        <?php else: ?>
        <!-- Cart Content -->
        <div class="cart-header">
            <h1><i class="fas fa-shopping-cart"></i> سلة التسوق</h1>
            <p>لديك <span class="highlight"><?php echo $total_items; ?></span> منتج في سلة التسوق</p>
        </div>

        <?php
        // التحقق من وجود منتجات بدون مقاسات
        $missing_sizes_items = [];
        foreach ($cart_items as $item) {
            // التحقق من وجود مقاسات للمنتج
            $size_check = "SELECT COUNT(*) as has_sizes FROM product_sizes WHERE product_id = ?";
            $stmt = $conn->prepare($size_check);
            $stmt->execute([$item['product_id']]);
            $has_sizes = $stmt->fetch()['has_sizes'] > 0;

            // إذا كان للمنتج مقاسات ولكن لم يتم اختيار مقاس
            if ($has_sizes && !$item['size_id']) {
                $missing_sizes_items[] = $item['title'];
            }
        }

        if (!empty($missing_sizes_items)): ?>
        <div class="alert alert-warning missing-sizes-alert">
            <div class="alert-content">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="alert-text">
                    <h4>يرجى اختيار المقاسات</h4>
                    <p>بعض المنتجات في سلتك تحتاج إلى اختيار مقاس قبل إتمام الطلب:</p>
                    <ul>
                        <?php foreach ($missing_sizes_items as $item_title): ?>
                        <li><?php echo htmlspecialchars($item_title); ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p><small>استخدمي زر "اختيار المقاس" بجانب كل منتج لتحديد المقاس المطلوب.</small></p>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="cart-layout">
            <!-- Cart Items -->
            <div class="cart-items-section">
                <div class="cart-items-header">
                    <h3>المنتجات المختارة</h3>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_cart">
                        <button type="submit" class="btn btn-danger btn-sm" 
                                onclick="return confirm('هل أنت متأكدة من إفراغ السلة؟')">
                            <i class="fas fa-trash"></i>
                            إفراغ السلة
                        </button>
                    </form>
                </div>
                
                <div class="cart-items-list">
                    <?php foreach ($cart_items as $item): ?>
                    <?php
                    $item_price = $item['sale_price'] && $item['sale_price'] > 0 ? $item['sale_price'] : $item['price'];
                    $item_total = $item_price * $item['quantity'];
                    ?>
                    <div class="cart-item" data-cart-id="<?php echo $item['id']; ?>">
                        <div class="item-image">
                            <?php if ($item['featured_image']): ?>
                                <img src="<?php echo UPLOAD_URL . $item['featured_image']; ?>" alt="<?php echo $item['title']; ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($item['sale_price'] && $item['sale_price'] > 0): ?>
                                <div class="item-badge sale">خصم</div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="item-details">
                            <h4 class="item-title"><?php echo $item['title']; ?></h4>
                            
                            <div class="item-options">
                                <?php if ($item['color_name']): ?>
                                <div class="item-option">
                                    <span class="option-label">اللون:</span>
                                    <span class="option-value">
                                        <?php echo $item['color_name']; ?>
                                        <?php if ($item['hex_code']): ?>
                                            <span class="color-preview" style="background-color: <?php echo $item['hex_code']; ?>"></span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($item['size_name']): ?>
                                <div class="item-option">
                                    <span class="option-label">المقاس:</span>
                                    <span class="option-value"><?php echo $item['size_name']; ?></span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showSizeSelector(<?php echo $item['id']; ?>, <?php echo $item['product_id']; ?>)">
                                        <i class="fas fa-edit"></i> تغيير
                                    </button>
                                </div>
                                <?php else: ?>
                                <!-- إذا لم يكن هناك مقاس محدد -->
                                <div class="item-option missing-size">
                                    <span class="option-label">المقاس:</span>
                                    <span class="option-value missing">غير محدد</span>
                                    <button type="button" class="btn btn-sm btn-warning" onclick="showSizeSelector(<?php echo $item['id']; ?>, <?php echo $item['product_id']; ?>)">
                                        <i class="fas fa-plus"></i> اختيار المقاس
                                    </button>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="item-price">
                                <?php if ($item['sale_price'] && $item['sale_price'] > 0): ?>
                                    <span class="current-price"><?php echo format_price($item['sale_price']); ?></span>
                                    <span class="original-price"><?php echo format_price($item['price']); ?></span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo format_price($item['price']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="item-quantity">
                            <label>الكمية:</label>
                            <div class="quantity-controls">
                                <button type="button" class="quantity-btn minus" onclick="updateQuantity(<?php echo $item['id']; ?>, -1)">-</button>
                                <input type="number" class="quantity-input" value="<?php echo $item['quantity']; ?>" 
                                       min="1" max="10" data-cart-id="<?php echo $item['id']; ?>" 
                                       onchange="updateQuantityDirect(<?php echo $item['id']; ?>, this.value)">
                                <button type="button" class="quantity-btn plus" onclick="updateQuantity(<?php echo $item['id']; ?>, 1)">+</button>
                            </div>
                        </div>
                        
                        <div class="item-total">
                            <span class="total-label">الإجمالي:</span>
                            <span class="total-price"><?php echo format_price($item_total); ?></span>
                        </div>
                        
                        <div class="item-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="remove_item">
                                <input type="hidden" name="cart_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" class="btn btn-danger btn-sm" 
                                        onclick="return confirm('هل تريدين حذف هذا المنتج؟')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="summary-card">
                    <h3><i class="fas fa-calculator"></i> ملخص الطلب</h3>
                    
                    <div class="summary-details">
                        <div class="summary-row">
                            <span>المجموع الفرعي:</span>
                            <span><?php echo format_price($subtotal); ?></span>
                        </div>
                        
                        <div class="summary-row">
                            <span>الشحن:</span>
                            <span class="shipping-pending">لم يحدد بعد</span>
                        </div>
                        

                        
                        <div class="summary-row total">
                            <span>الإجمالي:</span>
                            <span><?php echo format_price($total); ?></span>
                        </div>
                    </div>
                    

                    
                    <div class="summary-actions">
                        <a href="checkout.php" class="btn btn-primary btn-large">
                            <i class="fas fa-credit-card"></i>
                            إتمام الطلب
                        </a>
                        <a href="products.php" class="btn btn-outline">
                            <i class="fas fa-plus"></i>
                            إضافة منتجات أخرى
                        </a>
                    </div>
                </div>
                
                <!-- Promo Code -->
                <div class="promo-card">
                    <h4><i class="fas fa-tag"></i> كود الخصم</h4>
                    <div class="promo-form">
                        <input type="text" placeholder="أدخلي كود الخصم" class="promo-input">
                        <button type="button" class="btn btn-outline btn-sm">تطبيق</button>
                    </div>
                </div>
                
                <!-- Security Features -->
                <div class="security-features">
                    <h4><i class="fas fa-shield-alt"></i> التسوق الآمن</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> دفع آمن ومشفر</li>
                        <li><i class="fas fa-check"></i> إرجاع مجاني خلال 14 يوم</li>
                        <li><i class="fas fa-check"></i> ضمان الجودة</li>
                        <li><i class="fas fa-check"></i> خدمة عملاء 24/7</li>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Size Selection Modal -->
<div class="modal fade" id="sizeModal" tabindex="-1" aria-labelledby="sizeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sizeModalLabel">اختيار المقاس</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="sizeOptions">
                    <!-- سيتم تحميل المقاسات هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSizeBtn" onclick="confirmSizeSelection()">تأكيد</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Cart Page Styles */
.cart-container {
    padding: 40px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    min-height: 80vh;
}

/* Empty Cart Styles */
.empty-cart {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.empty-cart-content {
    text-align: center;
    background: white;
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    max-width: 500px;
}

.empty-cart-icon {
    font-size: 80px;
    color: #e5e7eb;
    margin-bottom: 20px;
}

.empty-cart h2 {
    font-size: 28px;
    color: #1a202c;
    margin-bottom: 12px;
}

.empty-cart p {
    color: #6b7280;
    font-size: 16px;
    margin-bottom: 30px;
}

.empty-cart-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Cart Header */
.cart-header {
    text-align: center;
    margin-bottom: 40px;
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.cart-header h1 {
    font-size: 32px;
    color: #1a202c;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.cart-header p {
    color: #6b7280;
    font-size: 16px;
}

.highlight {
    color: #6366f1;
    font-weight: 700;
}

/* Missing Sizes Alert */
.missing-sizes-alert {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
}

.missing-sizes-alert .alert-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.missing-sizes-alert i {
    color: #f59e0b;
    font-size: 24px;
    margin-top: 2px;
}

.missing-sizes-alert .alert-text h4 {
    color: #92400e;
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.missing-sizes-alert .alert-text p {
    color: #92400e;
    margin: 8px 0;
    line-height: 1.5;
}

.missing-sizes-alert .alert-text ul {
    color: #92400e;
    margin: 10px 0;
    padding-right: 20px;
}

.missing-sizes-alert .alert-text li {
    margin: 5px 0;
    font-weight: 500;
}

.missing-sizes-alert .alert-text small {
    color: #a16207;
    font-style: italic;
}

/* Missing Sizes Alert */
.missing-sizes-alert {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
}

.missing-sizes-alert .alert-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.missing-sizes-alert i {
    color: #f59e0b;
    font-size: 24px;
    margin-top: 2px;
}

.missing-sizes-alert .alert-text h4 {
    color: #92400e;
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.missing-sizes-alert .alert-text p {
    color: #92400e;
    margin: 8px 0;
    line-height: 1.5;
}

.missing-sizes-alert .alert-text ul {
    color: #92400e;
    margin: 10px 0;
    padding-right: 20px;
}

.missing-sizes-alert .alert-text li {
    margin: 5px 0;
    font-weight: 500;
}

.missing-sizes-alert .alert-text small {
    color: #a16207;
    font-style: italic;
}

/* Cart Layout */
.cart-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Cart Items Section */
.cart-items-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.cart-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.cart-items-header h3 {
    font-size: 20px;
    color: #1a202c;
    margin: 0;
}

/* Cart Item */
.cart-item {
    display: grid;
    grid-template-columns: 120px 1fr auto auto auto auto;
    gap: 20px;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.cart-item:hover {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin: 0 -20px;
}

.cart-item:last-child {
    border-bottom: none;
}

/* Item Image */
.item-image {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 12px;
    overflow: hidden;
    background: #f3f4f6;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 24px;
}

.item-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 600;
}

/* Item Details */
.item-details {
    flex: 1;
}

.item-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 8px;
    line-height: 1.4;
}

.item-options {
    margin-bottom: 12px;
}

.item-option {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 14px;
}

.option-label {
    color: #6b7280;
    font-weight: 500;
}

.option-value {
    color: #374151;
    display: flex;
    align-items: center;
    gap: 6px;
}

.option-value.missing {
    color: #dc2626;
    font-style: italic;
}

.item-option.missing-size {
    background: #fef2f2;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

.item-option.missing-size .option-label {
    color: #dc2626;
    font-weight: 600;
}

/* Size Selection Modal */
.size-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin: 20px 0;
}

.size-option {
    position: relative;
}

.size-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.size-label {
    display: block;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.size-label:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.size-option input[type="radio"]:checked + .size-label {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

.size-name {
    display: block;
    font-weight: 600;
    font-size: 16px;
}

.size-desc {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    margin-top: 4px;
}

.color-preview {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    display: inline-block;
}

.item-price {
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-price {
    font-size: 16px;
    font-weight: 700;
    color: #059669;
}

.original-price {
    font-size: 14px;
    color: #9ca3af;
    text-decoration: line-through;
}

/* Quantity Controls */
.item-quantity {
    text-align: center;
}

.item-quantity label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8fafc;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: #6366f1;
    color: white;
}

.quantity-input {
    width: 50px;
    height: 32px;
    border: none;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    background: white;
}

.quantity-input:focus {
    outline: none;
    background: #f8fafc;
}

/* Item Total */
.item-total {
    text-align: center;
    min-width: 100px;
}

.total-label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
    font-weight: 500;
}

.total-price {
    font-size: 16px;
    font-weight: 700;
    color: #1a202c;
}

/* Item Actions */
.item-actions {
    display: flex;
    justify-content: center;
}

/* Cart Summary */
.cart-summary {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
}

.summary-card h3 {
    font-size: 20px;
    color: #1a202c;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-details {
    margin-bottom: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
}

.summary-row.total {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    padding-top: 12px;
    border-top: 2px solid #f1f5f9;
    margin-top: 16px;
}

.free-shipping {
    color: #059669;
    font-weight: 600;
}

.shipping-notice {
    background: #fef3c7;
    color: #92400e;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Promo Card */
.promo-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
}

.promo-card h4 {
    font-size: 16px;
    color: #1a202c;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.promo-form {
    display: flex;
    gap: 8px;
}

.promo-input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
}

.promo-input:focus {
    outline: none;
    border-color: #6366f1;
}

/* Security Features */
.security-features {
    background: #f0f9ff;
    border-radius: 16px;
    padding: 20px;
}

.security-features h4 {
    font-size: 16px;
    color: #1a202c;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.security-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #374151;
}

.security-features li i {
    color: #10b981;
    font-size: 12px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-outline {
    background: white;
    color: #6366f1;
    border: 2px solid #6366f1;
}

.btn-outline:hover {
    background: #6366f1;
    color: white;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .cart-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cart-summary {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .cart-container {
        padding: 20px 0;
    }

    .cart-header,
    .cart-items-section,
    .summary-card {
        padding: 20px;
    }

    .cart-item {
        grid-template-columns: 80px 1fr;
        gap: 12px;
    }

    .item-quantity,
    .item-total,
    .item-actions {
        grid-column: 1 / -1;
        margin-top: 12px;
    }

    .item-quantity {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .item-quantity label {
        margin-bottom: 0;
        white-space: nowrap;
    }

    .empty-cart-content {
        padding: 40px 20px;
    }

    .empty-cart-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .cart-header h1 {
        font-size: 24px;
    }

    .cart-items-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .summary-actions {
        gap: 8px;
    }

    .promo-form {
        flex-direction: column;
    }
}
</style>

<script>
// Cart functionality
document.addEventListener('DOMContentLoaded', function() {
    initCartFunctionality();
});

function initCartFunctionality() {
    // Add event listeners for quantity changes
    const quantityInputs = document.querySelectorAll('.quantity-input');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const cartId = this.dataset.cartId;
            const quantity = parseInt(this.value);
            updateQuantityDirect(cartId, quantity);
        });
    });
}

function updateQuantity(cartId, change) {
    const input = document.querySelector(`input[data-cart-id="${cartId}"]`);
    const currentValue = parseInt(input.value);
    const newValue = Math.max(1, Math.min(10, currentValue + change));

    input.value = newValue;
    updateQuantityDirect(cartId, newValue);
}

function updateQuantityDirect(cartId, quantity) {
    if (quantity < 1 || quantity > 10) {
        showToast('الكمية يجب أن تكون بين 1 و 10', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'update_quantity');
    formData.append('cart_id', cartId);
    formData.append('quantity', quantity);

    fetch('ajax/update-cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم تحديث الكمية', 'success');
            // Reload page to update totals
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast(data.message || 'فشل في تحديث الكمية', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في التحديث', 'error');
    });
}

// Size selection functions
let currentCartId = null;
let currentProductId = null;

function showSizeSelector(cartId, productId) {
    currentCartId = cartId;
    currentProductId = productId;

    // جلب المقاسات المتاحة للمنتج
    fetch('ajax/get-product-sizes.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySizeOptions(data.sizes);
            // فتح modal
            const modal = new bootstrap.Modal(document.getElementById('sizeModal'));
            modal.show();
        } else {
            console.error('Size fetch error:', data);
            showToast(data.message || 'فشل في جلب المقاسات', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في جلب المقاسات', 'error');
    });
}

function displaySizeOptions(sizes) {
    const sizeOptions = document.getElementById('sizeOptions');

    if (sizes.length === 0) {
        sizeOptions.innerHTML = '<p class="text-muted">لا توجد مقاسات متاحة لهذا المنتج</p>';
        return;
    }

    let html = '<div class="size-grid">';
    sizes.forEach(size => {
        html += `
            <div class="size-option" data-size-id="${size.id}">
                <input type="radio" name="selected_size" value="${size.id}" id="size_${size.id}">
                <label for="size_${size.id}" class="size-label">
                    <span class="size-name">${size.name}</span>
                </label>
            </div>
        `;
    });
    html += '</div>';

    sizeOptions.innerHTML = html;
}

function confirmSizeSelection() {
    const selectedSize = document.querySelector('input[name="selected_size"]:checked');

    if (!selectedSize) {
        showToast('يرجى اختيار مقاس', 'error');
        return;
    }

    const sizeId = selectedSize.value;

    // إرسال طلب تحديث المقاس
    fetch('ajax/update-cart-size.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_id: currentCartId,
            size_id: sizeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم تحديث المقاس بنجاح', 'success');
            // إغلاق modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('sizeModal'));
            modal.hide();
            // إعادة تحميل الصفحة لإظهار التحديث
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast(data.message || 'فشل في تحديث المقاس', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في تحديث المقاس', 'error');
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;

    // Add animation styles if not exists
    if (!document.querySelector('#toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
            }
            .toast-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
            }
            .toast-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
            }
        `;
        document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>

<?php include 'includes/footer.php'; ?>
