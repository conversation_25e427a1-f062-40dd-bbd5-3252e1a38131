<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

// التحقق من وجود slug
if (!isset($_GET['slug']) || empty($_GET['slug'])) {
    header('Location: ' . SITE_URL);
    exit();
}

$slug = sanitize_input($_GET['slug']);

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// جلب القسم
if (!$category->read_by_slug($slug)) {
    header('Location: ' . SITE_URL);
    exit();
}

$page_title = $category->name;

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize_input($_GET['sort']) : 'newest';
$min_price = isset($_GET['min_price']) ? (float)$_GET['min_price'] : null;
$max_price = isset($_GET['max_price']) ? (float)$_GET['max_price'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// جلب المنتجات
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بناء الاستعلام
    $where_conditions = ['p.category_id = :category_id', 'p.is_active = 1'];
    $params = [':category_id' => $category->id];
    
    if (!empty($search)) {
        $where_conditions[] = "(p.title LIKE :search OR p.description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if ($min_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) >= :min_price";
        $params[':min_price'] = $min_price;
    }
    
    if ($max_price !== null) {
        $where_conditions[] = "COALESCE(p.sale_price, p.price) <= :max_price";
        $params[':max_price'] = $max_price;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // ترتيب النتائج
    $order_clause = '';
    switch ($sort) {
        case 'price_low':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) ASC';
            break;
        case 'price_high':
            $order_clause = 'ORDER BY COALESCE(p.sale_price, p.price) DESC';
            break;
        case 'name':
            $order_clause = 'ORDER BY p.title ASC';
            break;
        case 'featured':
            $order_clause = 'ORDER BY p.is_featured DESC, p.created_at DESC';
            break;
        default:
            $order_clause = 'ORDER BY p.created_at DESC';
    }
    
    // استعلام العد
    $count_query = "SELECT COUNT(*) as total FROM products p {$where_clause}";
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_products = $count_stmt->fetch()['total'];
    
    // استعلام البيانات
    $products_query = "SELECT p.* FROM products p {$where_clause} {$order_clause} LIMIT :limit OFFSET :offset";
    $products_stmt = $conn->prepare($products_query);
    foreach ($params as $key => $value) {
        $products_stmt->bindValue($key, $value);
    }
    $products_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $products_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $products_stmt->execute();
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب عدد الصفحات
    $total_pages = ceil($total_products / $limit);
    
    // جلب الأقسام الفرعية
    $subcategories_stmt = $category->get_subcategories($category->id);
    $subcategories = $subcategories_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب المنتجات";
    $products = [];
    $total_products = 0;
    $total_pages = 0;
    $subcategories = [];
}

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

// إعداد عنوان الصفحة ووصف SEO
$page_title = !empty($category->seo_title) ? $category->seo_title : $category->name;
$page_description = !empty($category->description) ? $category->description : '';
$page_keywords = $category->name . ', فساتين, أزياء, موضة';

$breadcrumb = [
    ['name' => 'الرئيسية', 'url' => 'index.php'],
    ['name' => $category->name]
];

include 'includes/header.php';
?>

<!-- Filters and Products -->
<section class="products-section">
    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3">
                <!-- Mobile Filter Toggle -->
                <div class="mobile-filter-toggle" onclick="toggleFilters()">
                    <div class="filter-icon">
                        <i class="fas fa-filter"></i>
                        <span>فلتر</span>
                    </div>
                    <div class="filter-count">3</div>
                </div>
                
                <!-- Filter Overlay -->
                <div class="filter-overlay" onclick="closeFilters()"></div>
                
                <div class="filters-sidebar">
                    <div class="filter-header">
                        <h3>تصفية النتائج</h3>
                        <div class="filter-actions">
                            <button class="clear-filters">مسح الكل</button>
                            <button class="filter-close" onclick="closeFilters()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Price Filter -->
                    <div class="filter-group">
                        <h4>السعر</h4>
                        <div class="price-filter">
                            <div class="price-range">
                                <input type="range" class="range-slider" min="0" max="1000" value="<?php echo $min_price ?? 0; ?>">
                                <input type="range" class="range-slider" min="0" max="1000" value="<?php echo $max_price ?? 1000; ?>">
                            </div>
                            <div class="price-inputs">
                                <input type="number" placeholder="من" min="0" value="<?php echo $min_price; ?>">
                                <span>-</span>
                                <input type="number" placeholder="إلى" min="0" value="<?php echo $max_price; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Size Filter -->
                    <div class="filter-group">
                        <h4>المقاس</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="size" value="XS">
                                <span class="checkmark"></span>
                                <span>XS</span>
                                <span class="count">(24)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="size" value="S">
                                <span class="checkmark"></span>
                                <span>S</span>
                                <span class="count">(156)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="size" value="M">
                                <span class="checkmark"></span>
                                <span>M</span>
                                <span class="count">(234)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="size" value="L">
                                <span class="checkmark"></span>
                                <span>L</span>
                                <span class="count">(189)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="size" value="XL">
                                <span class="checkmark"></span>
                                <span>XL</span>
                                <span class="count">(98)</span>
                            </label>
                        </div>
                    </div>

                    <!-- Color Filter -->
                    <div class="filter-group">
                        <h4>اللون</h4>
                        <div class="color-filter">
                            <div class="color-option" data-color="أسود" style="background: #000000"></div>
                            <div class="color-option" data-color="أبيض" style="background: #ffffff; border: 1px solid #ddd"></div>
                            <div class="color-option" data-color="أحمر" style="background: #dc2626"></div>
                            <div class="color-option" data-color="أزرق" style="background: #1e40af"></div>
                            <div class="color-option" data-color="أخضر" style="background: #059669"></div>
                            <div class="color-option" data-color="وردي" style="background: #ec4899"></div>
                            <div class="color-option" data-color="بنفسجي" style="background: #7c3aed"></div>
                            <div class="color-option" data-color="ذهبي" style="background: #f59e0b"></div>
                            <div class="color-option" data-color="فضي" style="background: #6b7280"></div>
                            <div class="color-option" data-color="بني" style="background: #92400e"></div>
                        </div>
                    </div>

                    <!-- Rating Filter -->
                    <div class="filter-group">
                        <h4>التقييم</h4>
                        <div class="rating-filter">
                            <label class="rating-option">
                                <input type="checkbox" name="rating" value="5">
                                <div class="stars">★★★★★</div>
                                <span>فأكثر</span>
                            </label>
                            <label class="rating-option">
                                <input type="checkbox" name="rating" value="4">
                                <div class="stars">★★★★☆</div>
                                <span>فأكثر</span>
                            </label>
                            <label class="rating-option">
                                <input type="checkbox" name="rating" value="3">
                                <div class="stars">★★★☆☆</div>
                                <span>فأكثر</span>
                            </label>
                        </div>
                    </div>

                    <!-- Discount Filter -->
                    <div class="filter-group">
                        <h4>الخصم</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" name="discount" value="10">
                                <span class="checkmark"></span>
                                <span>10% فأكثر</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="discount" value="25">
                                <span class="checkmark"></span>
                                <span>25% فأكثر</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" name="discount" value="50">
                                <span class="checkmark"></span>
                                <span>50% فأكثر</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <div class="products-header">
                    <div class="results-info">
                        <span class="results-count">عرض <?php echo $offset + 1; ?>-<?php echo min($offset + $limit, $total_products); ?> من أصل <?php echo $total_products; ?> منتج</span>
                    </div>
                    <div class="sort-options">
                        <select class="sort-select" onchange="updateSort(this.value)">
                            <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                            <option value="featured" <?php echo $sort === 'featured' ? 'selected' : ''; ?>>الموصى به</option>
                            <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>السعر: من الأقل للأعلى</option>
                            <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>السعر: من الأعلى للأقل</option>
                            <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الأعلى تقييماً</option>
                        </select>
                    </div>
                </div>

                <?php if (!empty($products)): ?>
                <div class="products-grid">
                    <?php foreach ($products as $prod): ?>
                    <div class="product-card">
                        <div class="product-image">
                            <?php if ($prod['featured_image']): ?>
                                <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo $prod['title']; ?>">
                            <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                            <div class="product-badges">
                                <?php if ($prod['is_featured']): ?>
                                    <span class="badge trending">ترند</span>
                                <?php endif; ?>
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="badge new">جديد</span>
                                <?php endif; ?>
                            </div>
                            <div class="quick-actions">
                                <button class="quick-btn wishlist"><i class="far fa-heart"></i></button>
                                <button class="quick-btn quick-view"><i class="fas fa-eye"></i></button>
                                <button class="quick-btn compare"><i class="fas fa-random"></i></button>
                            </div>
                            <div class="size-guide">
                                <span>دليل المقاسات</span>
                            </div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">فساتين سهرة</div>
                            <h3><a href="<?php echo getProductUrl($prod['slug']); ?>"><?php echo htmlspecialchars($prod['title']); ?></a></h3>
                            <div class="rating">
                                <div class="stars">★★★★★</div>
                                <span class="rating-count">(<?php echo rand(50, 300); ?>)</span>
                            </div>
                            <div class="price">
                                <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                                    <span class="current"><?php echo format_price($prod['sale_price']); ?></span>
                                    <span class="original"><?php echo format_price($prod['price']); ?></span>
                                    <span class="discount">-<?php echo round((($prod['price'] - $prod['sale_price']) / $prod['price']) * 100); ?>%</span>
                                <?php else: ?>
                                    <span class="current"><?php echo format_price($prod['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="shipping-info">
                                <i class="fas fa-shipping-fast"></i>
                                <span>شحن مجاني</span>
                            </div>
                            <div class="color-options">
                                <span class="color-dot" style="background: #1e40af"></span>
                                <span class="color-dot" style="background: #dc2626"></span>
                                <span class="color-dot" style="background: #059669"></span>
                                <span class="more-colors">+3</span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Load More -->
                <div class="load-more-section">
                    <?php if ($page < $total_pages): ?>
                        <button class="load-more-btn" onclick="loadMore()">
                            <span>عرض المزيد</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    <?php endif; ?>
                    <div class="pagination-info">
                        <span>عرض <?php echo min($offset + $limit, $total_products); ?> من أصل <?php echo $total_products; ?> منتج</span>
                    </div>
                </div>
                
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="category.php?slug=<?php echo $slug; ?>" class="btn btn-primary">
                        <i class="fas fa-refresh"></i>
                        عرض جميع المنتجات
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
function updateSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

function loadMore() {
    const url = new URL(window.location);
    const currentPage = parseInt(url.searchParams.get('page') || '1');
    url.searchParams.set('page', currentPage + 1);
    window.location.href = url.toString();
}

// Mobile Filter Functions
function toggleFilters() {
    const sidebar = document.querySelector('.filters-sidebar');
    const overlay = document.querySelector('.filter-overlay');
    
    sidebar.classList.add('show');
    overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeFilters() {
    const sidebar = document.querySelector('.filters-sidebar');
    const overlay = document.querySelector('.filter-overlay');
    
    sidebar.classList.remove('show');
    overlay.classList.remove('show');
    document.body.style.overflow = '';
}

// Close filters on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeFilters();
    }
});

// Update filter count
function updateFilterCount() {
    const checkedFilters = document.querySelectorAll('.filters-sidebar input[type="checkbox"]:checked');
    const filterCount = document.querySelector('.filter-count');
    const count = checkedFilters.length;
    
    if (filterCount) {
        filterCount.textContent = count;
        filterCount.style.display = count > 0 ? 'block' : 'none';
    }
}

// Add event listeners to filter checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.filters-sidebar input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateFilterCount);
    });
    
    // Initial count update
    updateFilterCount();
});
</script>

<?php include 'includes/footer.php'; ?>
