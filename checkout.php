<?php
session_start();
require_once 'config/database.php';
require_once 'includes/Cart.php';

$page_title = 'إتمام الطلب';

// إنشاء كائن السلة
$cart = new Cart();

// التحقق من وجود عناصر في السلة
$cart_items_result = $cart->get_cart_items();
$cart_items = [];
while ($row = $cart_items_result->fetch(PDO::FETCH_ASSOC)) {
    $cart_items[] = $row;
}

if (empty($cart_items)) {
    $_SESSION['error_message'] = 'السلة فارغة. يرجى إضافة منتجات قبل المتابعة.';
    header('Location: cart.php');
    exit();
}

// التحقق من أن جميع المنتجات التي تحتاج مقاسات لها مقاسات محددة
$missing_sizes = [];
foreach ($cart_items as $item) {
    // التحقق من وجود مقاسات للمنتج
    $database = new Database();
    $conn = $database->getConnection();

    $size_check = "SELECT COUNT(*) as has_sizes FROM product_sizes WHERE product_id = ?";
    $stmt = $conn->prepare($size_check);
    $stmt->execute([$item['product_id']]);
    $has_sizes = $stmt->fetch()['has_sizes'] > 0;

    // إذا كان للمنتج مقاسات ولكن لم يتم اختيار مقاس
    if ($has_sizes && !$item['size_id']) {
        $missing_sizes[] = $item['title'];
    }
}

if (!empty($missing_sizes)) {
    $_SESSION['error_message'] = 'يرجى اختيار المقاس للمنتجات التالية: ' . implode(', ', $missing_sizes);
    header('Location: cart.php');
    exit();
}

// حساب الإجماليات
$subtotal = 0;
$total_items = 0;

foreach ($cart_items as $item) {
    $item_price = $item['sale_price'] && $item['sale_price'] > 0 ? $item['sale_price'] : $item['price'];
    $subtotal += $item_price * $item['quantity'];
    $total_items += $item['quantity'];
}

// رسوم الشحن - سيتم تحديدها بواسطة JavaScript
$shipping_cost = 0; // قيمة افتراضية، سيتم تحديثها بواسطة JavaScript
$total = $subtotal; // سيتم تحديث الإجمالي بواسطة JavaScript

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

// جلب البيانات من الجلسة إذا كانت موجودة (في حالة وجود أخطاء)
$form_data = $_SESSION['form_data'] ?? [];
unset($_SESSION['form_data']);

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // جلب البلدان
    $countries_query = "SELECT * FROM countries WHERE is_active = 1 ORDER BY name";
    $countries_stmt = $conn->prepare($countries_query);
    $countries_stmt->execute();
    $countries = $countries_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب شركات الشحن
    $shipping_query = "SELECT * FROM shipping_companies WHERE is_active = 1 ORDER BY cost";
    $shipping_stmt = $conn->prepare($shipping_query);
    $shipping_stmt->execute();
    $shipping_companies = $shipping_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $countries = [];
    $shipping_companies = [];
}

include 'includes/header.php';
?>

<div class="checkout-container">
    <div class="container">
        
        <!-- Checkout Header -->
        <div class="checkout-header">
            <h1><i class="fas fa-credit-card"></i> إتمام الطلب</h1>
            <div class="checkout-steps">
                <div class="step active">
                    <span class="step-number">1</span>
                    <span class="step-text">بيانات الطلب</span>
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    <span class="step-text">الدفع</span>
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    <span class="step-text">التأكيد</span>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        </div>
        <?php endif; ?>

        <form action="process_order.php" method="POST" class="checkout-form" id="checkoutForm" enctype="multipart/form-data">
            <div class="checkout-layout">
                
                <!-- Order Form -->
                <div class="checkout-form-section">
                    
                    <!-- Customer Information -->
                    <div class="form-section">
                        <h2><i class="fas fa-user"></i> بيانات العميل</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="customer_name">الاسم الكامل *</label>
                                <input type="text" id="customer_name" name="customer_name" 
                                       value="<?php echo $form_data['customer_name'] ?? ''; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="customer_email">البريد الإلكتروني (اختياري)</label>
                                <input type="email" id="customer_email" name="customer_email"
                                       value="<?php echo $form_data['customer_email'] ?? ''; ?>"
                                       placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label for="customer_phone">رقم الهاتف *</label>
                                <input type="tel" id="customer_phone" name="customer_phone" 
                                       value="<?php echo $form_data['customer_phone'] ?? ''; ?>" 
                                       placeholder="05xxxxxxxx" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="customer_phone_alt">رقم هاتف بديل</label>
                                <input type="tel" id="customer_phone_alt" name="customer_phone_alt" 
                                       value="<?php echo $form_data['customer_phone_alt'] ?? ''; ?>" 
                                       placeholder="05xxxxxxxx">
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="form-section">
                        <h2><i class="fas fa-map-marker-alt"></i> عنوان التوصيل</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="country_display">البلد</label>
                                <input type="text" id="country_display" value="السعودية" readonly
                                       style="background: #f8f9fa; cursor: not-allowed;">
                                <input type="hidden" id="country_id" name="country_id" value="1">
                            </div>
                            
                            <div class="form-group">
                                <label for="city_id">المدينة *</label>
                                <select id="city_id" name="city_id" required onchange="loadAreas()">
                                    <option value="">اختر المدينة</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="area_id">المنطقة *</label>
                                <select id="area_id" name="area_id" required>
                                    <option value="">اختر المنطقة</option>
                                </select>
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="address">العنوان التفصيلي *</label>
                                <textarea id="address" name="address" rows="3" required 
                                          placeholder="اكتب العنوان التفصيلي..."><?php echo $form_data['address'] ?? ''; ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="landmark">علامة مميزة</label>
                                <input type="text" id="landmark" name="landmark" 
                                       value="<?php echo $form_data['landmark'] ?? ''; ?>" 
                                       placeholder="مثل: بجانب مسجد النور">
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Options -->
                    <div class="form-section">
                        <h2><i class="fas fa-truck"></i> شركة الشحن</h2>
                        <div class="shipping-companies">
                            <?php if (!empty($shipping_companies)): ?>
                                <?php foreach ($shipping_companies as $index => $company): ?>
                                <div class="shipping-option">
                                    <input type="radio" id="shipping_<?php echo $company['id']; ?>"
                                           name="shipping_company_id" value="<?php echo $company['id']; ?>"
                                           data-cost="<?php echo $company['cost']; ?>"
                                           <?php echo $index === 0 ? 'checked' : ''; ?>>
                                    <label for="shipping_<?php echo $company['id']; ?>" class="shipping-label">
                                        <div class="shipping-info">
                                            <div class="shipping-name"><?php echo $company['name']; ?></div>
                                            <div class="shipping-cost"><?php echo format_price($company['cost']); ?></div>
                                        </div>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="shipping-option">
                                    <input type="radio" id="shipping_default" name="shipping_company_id" value=""
                                           data-cost="50" checked>
                                    <label for="shipping_default" class="shipping-label">
                                        <div class="shipping-info">
                                            <div class="shipping-name">الشحن العادي</div>
                                            <div class="shipping-cost"><?php echo format_price(50); ?></div>
                                        </div>
                                    </label>
                                </div>
                            <?php endif; ?>
                        </div>


                    </div>

                    <!-- Payment Method -->
                    <div class="form-section">
                        <h2><i class="fas fa-university"></i> طريقة الدفع</h2>
                        <div class="payment-methods">
                            <div class="payment-method">
                                <input type="radio" id="bank_transfer" name="payment_method" value="bank_transfer" checked>
                                <label for="bank_transfer" class="payment-label">
                                    <i class="fas fa-university"></i>
                                    <div class="payment-info">
                                        <strong>تحويل بنكي</strong>
                                        <span>تحويل مباشر للحساب البنكي</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Bank Transfer Details -->
                        <div id="bank_transfer_details" class="bank-details">
                            <?php
                            // جلب بيانات البنك
                            try {
                                $bank_sql = "SELECT setting_key, setting_value FROM bank_settings";
                                $bank_stmt = $conn->prepare($bank_sql);
                                $bank_stmt->execute();
                                $bank_settings = $bank_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                            } catch (Exception $e) {
                                $bank_settings = [
                                    'bank_name' => 'الراجحي',
                                    'account_name' => 'مؤسسة اكنان',
                                    'account_number' => '***************',
                                    'iban' => '************************',
                                    'transfer_instructions' => 'يرجى إرسال إيصال التحويل بعد إتمام العملية'
                                ];
                            }
                            ?>
                            <div class="bank-info">
                                <h4><i class="fas fa-info-circle"></i> بيانات التحويل البنكي</h4>
                                <div class="bank-details-grid">
                                    <div class="bank-detail">
                                        <label>اسم البنك:</label>
                                        <span><?php echo htmlspecialchars($bank_settings['bank_name'] ?? 'الراجحي'); ?></span>
                                    </div>
                                    <div class="bank-detail">
                                        <label>اسم الحساب:</label>
                                        <span><?php echo htmlspecialchars($bank_settings['account_name'] ?? 'مؤسسة اكنان'); ?></span>
                                    </div>
                                    <div class="bank-detail">
                                        <label>رقم الحساب:</label>
                                        <span class="account-number"><?php echo htmlspecialchars($bank_settings['account_number'] ?? '***************'); ?></span>
                                        <button type="button" class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($bank_settings['account_number'] ?? '***************'); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="bank-detail">
                                        <label>الآيبان:</label>
                                        <span class="iban"><?php echo htmlspecialchars($bank_settings['iban'] ?? '************************'); ?></span>
                                        <button type="button" class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($bank_settings['iban'] ?? '************************'); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="transfer-instructions">
                                    <p><i class="fas fa-exclamation-triangle"></i> <?php echo nl2br(htmlspecialchars($bank_settings['transfer_instructions'] ?? 'يرجى إرسال إيصال التحويل بعد إتمام العملية')); ?></p>
                                </div>
                            </div>

                            <!-- Receipt Upload -->
                            <div class="receipt-upload">
                                <h4><i class="fas fa-upload"></i> رفع إيصال التحويل <span class="required">*</span></h4>
                                <div class="upload-area" id="uploadArea">
                                    <input type="file" id="transfer_receipt" name="transfer_receipt" accept=".jpg,.jpeg,.png,.pdf" required>
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>اضغط هنا لرفع إيصال التحويل</p>
                                        <small>يُقبل ملفات JPG, PNG, PDF (حد أقصى 5 ميجا)</small>
                                    </div>
                                </div>
                                <div id="uploadPreview" class="upload-preview"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Notes -->
                    <div class="form-section">
                        <h2><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h2>
                        <div class="form-group">
                            <textarea id="notes" name="notes" rows="4" 
                                      placeholder="أي ملاحظات خاصة بالطلب..."><?php echo $form_data['notes'] ?? ''; ?></textarea>
                        </div>
                    </div>


                </div>

                <!-- Order Summary -->
                <div class="order-summary-section">
                    <div class="summary-card sticky">
                        <h2><i class="fas fa-shopping-bag"></i> ملخص الطلب</h2>
                        
                        <!-- Cart Items -->
                        <div class="summary-items">
                            <?php foreach ($cart_items as $item): ?>
                            <?php $item_price = $item['sale_price'] && $item['sale_price'] > 0 ? $item['sale_price'] : $item['price']; ?>
                            <div class="summary-item">
                                <div class="item-image">
                                    <?php if ($item['featured_image']): ?>
                                        <img src="<?php echo UPLOAD_URL . $item['featured_image']; ?>" alt="<?php echo $item['title']; ?>">
                                    <?php else: ?>
                                        <div class="no-image"><i class="fas fa-image"></i></div>
                                    <?php endif; ?>
                                </div>
                                <div class="item-details">
                                    <h4><?php echo $item['title']; ?></h4>
                                    <div class="item-options">
                                        <?php if ($item['color_name']): ?>
                                            <span>اللون: <?php echo $item['color_name']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($item['size_name']): ?>
                                            <span>المقاس: <?php echo $item['size_name']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="item-quantity">الكمية: <?php echo $item['quantity']; ?></div>
                                </div>
                                <div class="item-price">
                                    <?php echo format_price($item_price * $item['quantity']); ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Totals -->
                        <div class="summary-totals">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span class="subtotal-amount" data-amount="<?php echo $subtotal; ?>"><?php echo format_price($subtotal); ?></span>
                            </div>

                            <div class="total-row">
                                <span>الشحن:</span>
                                <span class="shipping-cost-display">لم يحدد بعد</span>
                            </div>

                            <div class="total-row total">
                                <span>الإجمالي:</span>
                                <span class="total-amount"><?php echo format_price($total); ?></span>
                            </div>
                        </div>
                        

                        
                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary btn-large submit-order">
                            <i class="fas fa-check"></i>
                            تأكيد الطلب
                        </button>
                        
                        <a href="cart.php" class="btn btn-outline">
                            <i class="fas fa-arrow-right"></i>
                            العودة للسلة
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Checkout Page Styles */
.checkout-container {
    padding: 40px 0;
    background: #f8fafc;
    min-height: 80vh;
    overflow-x: hidden; /* منع التمرير الأفقي */
}

/* تأكد من عدم تجاوز العناصر للشاشة */
* {
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* منع تجاوز العناصر للشاشة */
.checkout-container,
.checkout-layout,
.checkout-form-section,
.summary-card,
.form-section {
    max-width: 100%;
    overflow-x: hidden;
}

/* تحسين العرض للنصوص الطويلة */
.bank-detail span,
.item-details h4,
.shipping-name,
.payment-info strong {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.checkout-header {
    text-align: center;
    margin-bottom: 40px;
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.checkout-header h1 {
    font-size: 32px;
    color: #1a202c;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.checkout-steps {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 20px;
}

.step {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #9ca3af;
}

.step.active {
    color: #6366f1;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.step.active .step-number {
    background: #6366f1;
    color: white;
}

.step-text {
    font-weight: 500;
}

.alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.checkout-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 15px;
}

.checkout-form-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.form-section {
    margin-bottom: 40px;
    padding: 30px;
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section h2 {
    font-size: 22px;
    color: #1a202c;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 2px solid #f3f4f6;
    font-weight: 700;
    gap: 12px;
}

.form-section h2 i {
    color: #6366f1;
    font-size: 24px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 8px;
    color: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 10px;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-group label::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 2px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 14px 18px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fafbfc;
    font-family: inherit;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #d1d5db;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    background: white;
    transform: translateY(-1px);
}

/* Shipping Companies */
.shipping-companies {
    display: grid;
    gap: 16px;
    margin-top: 10px;
}

.shipping-option {
    position: relative;
}

.shipping-option input[type="radio"] {
    display: none;
}

.shipping-label {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafbfc;
    position: relative;
    overflow: hidden;
}

.shipping-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.shipping-option input[type="radio"]:checked + .shipping-label::before {
    transform: scaleY(1);
}

.shipping-label:hover {
    border-color: #d1d5db;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.shipping-option input[type="radio"]:checked + .shipping-label {
    border-color: #6366f1;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
}

.shipping-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.shipping-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 17px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.shipping-name::before {
    content: '🚚';
    font-size: 20px;
}

.shipping-cost {
    font-weight: 700;
    color: #059669;
    font-size: 18px;
    background: #ecfdf5;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #d1fae5;
}

.payment-methods {
    display: grid;
    gap: 16px;
    margin-top: 10px;
}

.payment-method {
    position: relative;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-label {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafbfc;
    position: relative;
    overflow: hidden;
}

.payment-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #10b981, #059669);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.payment-method input[type="radio"]:checked + .payment-label::before {
    transform: scaleY(1);
}

.payment-label:hover {
    border-color: #d1d5db;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.payment-method input[type="radio"]:checked + .payment-label {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
}

.payment-label i {
    font-size: 28px;
    color: white;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.payment-info {
    flex: 1;
}

.payment-info strong {
    display: block;
    color: #1a202c;
    margin-bottom: 6px;
    font-size: 17px;
    font-weight: 600;
}

.payment-info span {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.4;
}

/* Bank Transfer Details */
.bank-details {
    margin-top: 20px;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
}

.bank-info h4 {
    color: #1e293b;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bank-details-grid {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.bank-detail {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bank-detail label {
    font-weight: 600;
    color: #374151;
    min-width: 100px;
    margin: 0;
}

.bank-detail span {
    flex: 1;
    font-family: 'Courier New', monospace;
    background: #f8fafc;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-size: 14px;
    color: #1e293b;
    word-break: break-all;
    overflow-wrap: break-word;
}

.copy-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.copy-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.transfer-instructions {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.transfer-instructions p {
    margin: 0;
    color: #92400e;
    font-size: 14px;
    line-height: 1.5;
}

/* Receipt Upload */
.receipt-upload {
    margin-top: 24px;
    padding: 20px;
    background: white;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
}

.receipt-upload h4 {
    color: #1e293b;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.upload-area {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.upload-area input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-content {
    text-align: center;
    padding: 32px 16px;
}

.upload-content i {
    font-size: 48px;
    color: #6b7280;
    margin-bottom: 16px;
}

.upload-content p {
    font-size: 16px;
    color: #374151;
    margin-bottom: 8px;
}

.upload-content small {
    color: #6b7280;
    font-size: 14px;
}

.upload-preview {
    margin-top: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    display: none;
}

.upload-preview.show {
    display: block;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.file-icon {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.file-details h5 {
    margin: 0;
    font-size: 14px;
    color: #1e293b;
}

.file-details small {
    color: #6b7280;
}

.terms-section {
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #6366f1;
    border-color: #6366f1;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.checkbox-label a {
    color: #6366f1;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.order-summary-section {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-card h2 {
    font-size: 20px;
    color: #1a202c;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-items {
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #e5e7eb;
}

.summary-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f3f4f6;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 20px;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    font-size: 14px;
    color: #1a202c;
    margin-bottom: 4px;
    line-height: 1.4;
}

.item-options {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 4px;
}

.item-options span {
    font-size: 12px;
    color: #6b7280;
}

.item-quantity {
    font-size: 12px;
    color: #6b7280;
}

.item-price {
    font-weight: 600;
    color: #059669;
    font-size: 14px;
}

.summary-totals {
    padding-top: 20px;
    border-top: 2px solid #e5e7eb;
    margin-bottom: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.total-row.total {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
}

.free-shipping {
    color: #059669;
    font-weight: 600;
}

.shipping-notice {
    background: #fef3c7;
    color: #92400e;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    width: 100%;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-outline {
    background: white;
    color: #6366f1;
    border: 2px solid #6366f1;
}

.btn-outline:hover {
    background: #6366f1;
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .order-summary-section {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .checkout-container {
        padding: 15px 0;
    }

    .container {
        padding: 0 10px;
        max-width: 100%;
        width: 100%;
        margin: 0 auto;
    }

    .checkout-layout {
        padding: 0;
        gap: 15px;
        width: 100%;
        max-width: 100%;
    }

    .checkout-header {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .checkout-header h1 {
        font-size: 22px;
        flex-direction: column;
        gap: 8px;
    }

    .checkout-form-section,
    .summary-card {
        padding: 15px;
        margin: 0;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        width: 100%;
        max-width: 100%;
        overflow: hidden;
    }

    .form-section {
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
    }

    .form-section h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .form-section h2 i {
        width: 24px;
        height: 24px;
        font-size: 16px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px 14px;
        font-size: 16px; /* منع التكبير في iOS */
    }

    .shipping-companies,
    .payment-methods {
        gap: 10px;
    }

    .shipping-label,
    .payment-label {
        padding: 15px;
        gap: 10px;
        flex-direction: column;
        text-align: center;
    }

    .shipping-info,
    .payment-info {
        width: 100%;
        text-align: center;
    }

    .shipping-name,
    .payment-info strong {
        font-size: 16px;
        margin-bottom: 5px;
    }

    .shipping-cost {
        font-size: 16px;
        padding: 6px 12px;
    }

    .bank-details {
        padding: 15px;
        margin-top: 15px;
    }

    .bank-details-grid {
        gap: 10px;
    }

    .bank-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
    }

    .bank-detail label {
        min-width: auto;
        font-size: 14px;
    }

    .bank-detail span {
        width: 100%;
        font-size: 13px;
        padding: 6px 10px;
        word-break: break-all;
        overflow-wrap: break-word;
        white-space: normal;
    }

    .copy-btn {
        align-self: flex-end;
        padding: 6px 10px;
        font-size: 11px;
    }

    .upload-content {
        padding: 20px 12px;
    }

    .upload-content i {
        font-size: 36px;
    }

    .upload-content p {
        font-size: 14px;
    }

    .upload-content small {
        font-size: 12px;
    }

    .summary-item {
        flex-direction: column;
        gap: 8px;
        padding: 12px 0;
    }

    .item-image {
        width: 80px;
        height: 80px;
        align-self: center;
    }

    .item-details {
        text-align: center;
    }

    .item-details h4 {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .item-options {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
        margin-bottom: 8px;
    }

    .item-options span {
        font-size: 13px;
        background: #f3f4f6;
        padding: 2px 8px;
        border-radius: 4px;
    }

    .item-quantity {
        font-size: 13px;
        margin-bottom: 8px;
    }

    .item-price {
        font-size: 16px;
        font-weight: 700;
        text-align: center;
    }

    .total-row {
        font-size: 15px;
        margin-bottom: 10px;
    }

    .total-row.total {
        font-size: 18px;
        padding-top: 10px;
    }

    .btn {
        padding: 14px 20px;
        font-size: 16px;
        margin-bottom: 10px;
    }

    .btn-large {
        padding: 16px 24px;
        font-size: 18px;
    }

    .checkout-steps {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: 4px;
        min-width: 80px;
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .step-text {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .checkout-container {
        padding: 10px 0;
    }

    .container {
        padding: 0 10px;
    }

    .checkout-header {
        padding: 15px 10px;
        margin-bottom: 15px;
    }

    .checkout-header h1 {
        font-size: 20px;
    }

    .checkout-form-section,
    .summary-card {
        padding: 12px;
        margin: 0;
        width: 100%;
        max-width: 100%;
        overflow: hidden;
    }

    .form-section {
        padding: 12px;
        margin-bottom: 12px;
    }

    .form-section h2 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
    }

    .shipping-label,
    .payment-label {
        padding: 12px;
    }

    .bank-details {
        padding: 12px;
    }

    .bank-detail {
        padding: 10px;
    }

    .checkout-steps {
        gap: 10px;
    }

    .step {
        min-width: 70px;
    }

    .step-number {
        width: 24px;
        height: 24px;
        font-size: 11px;
    }

    .step-text {
        font-size: 11px;
    }
}
</style>

<script>
// Checkout page functionality
document.addEventListener('DOMContentLoaded', function() {
    initCheckoutForm();
});

function initCheckoutForm() {
    // تحميل المدن للسعودية تلقائياً
    loadCities();

    // Load cities when country changes
    const countrySelect = document.getElementById('country_id');
    if (countrySelect) {
        countrySelect.addEventListener('change', loadCities);

        // Load cities if country is already selected
        if (countrySelect.value) {
            loadCities();
        }
    }

    // Load areas when city changes
    const citySelect = document.getElementById('city_id');
    if (citySelect) {
        citySelect.addEventListener('change', loadAreas);
    }

    // استعادة القيم المحفوظة
    <?php if (isset($form_data['city_id']) && !empty($form_data['city_id'])): ?>
    setTimeout(() => {
        const citySelect = document.getElementById('city_id');
        if (citySelect) {
            citySelect.value = '<?php echo $form_data['city_id']; ?>';
            loadAreas();

            <?php if (isset($form_data['area_id']) && !empty($form_data['area_id'])): ?>
            setTimeout(() => {
                const areaSelect = document.getElementById('area_id');
                if (areaSelect) {
                    areaSelect.value = '<?php echo $form_data['area_id']; ?>';
                }
            }, 500);
            <?php endif; ?>
        }
    }, 500);
    <?php endif; ?>

    // Form validation
    const form = document.getElementById('checkoutForm');
    if (form) {
        form.addEventListener('submit', validateForm);
    }

    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', formatPhoneNumber);
    });

    // ربط أحداث شركات الشحن
    const shippingInputs = document.querySelectorAll('input[name="shipping_company_id"]');
    console.log('عدد خيارات الشحن:', shippingInputs.length);

    shippingInputs.forEach((input, index) => {
        console.log('ربط الحدث للخيار', index, ':', input.value, 'تكلفة:', input.dataset.cost);

        input.addEventListener('change', function() {
            console.log('تم تغيير الشحن إلى:', this.value, 'تكلفة:', this.dataset.cost);
            updateShippingCost();
        });

        input.addEventListener('click', function() {
            console.log('تم النقر على الشحن:', this.value);
            setTimeout(updateShippingCost, 50);
        });
    });

    // تحديث التكلفة عند تحميل الصفحة
    setTimeout(() => {
        console.log('تحديث أولي عند تحميل الصفحة');
        updateShippingCost();
    }, 1000);
}

function updateShippingCost() {
    // البحث عن شركة الشحن المختارة
    const selectedShipping = document.querySelector('input[name="shipping_company_id"]:checked');
    if (!selectedShipping) return;

    // الحصول على تكلفة الشحن
    const shippingCost = parseFloat(selectedShipping.dataset.cost) || 0;

    // الحصول على المجموع الفرعي
    const subtotalElement = document.querySelector('.subtotal-amount');
    if (!subtotalElement) return;
    const subtotal = parseFloat(subtotalElement.dataset.amount) || 0;

    // تحديث عرض تكلفة الشحن (دائماً تكلفة الشركة المختارة)
    const shippingCostElement = document.querySelector('.shipping-cost-display');
    if (shippingCostElement) {
        shippingCostElement.innerHTML = shippingCost.toFixed(2) + ' ريال';
    }

    // تحديث الإجمالي (المجموع الفرعي + تكلفة الشحن)
    const total = subtotal + shippingCost;
    const totalElement = document.querySelector('.total-amount');
    if (totalElement) {
        totalElement.innerHTML = total.toFixed(2) + ' ريال';
    }

    // إخفاء رسالة الشحن المجاني (لأنه لا يوجد شحن مجاني)
    const freeShippingNotice = document.querySelector('.shipping-notice');
    if (freeShippingNotice) {
        freeShippingNotice.style.display = 'none';
    }

    // إضافة رسالة تتبع للتطوير
    console.log('تحديث الشحن:', {
        subtotal: subtotal,
        shippingCost: shippingCost,
        total: total
    });
}

function loadCities() {
    const countryId = 1; // السعودية محددة مسبقاً
    const citySelect = document.getElementById('city_id');
    const areaSelect = document.getElementById('area_id');

    // Clear existing options
    citySelect.innerHTML = '<option value="">اختر المدينة</option>';
    areaSelect.innerHTML = '<option value="">اختر المنطقة</option>';

    if (!countryId) return;

    // Show loading
    citySelect.innerHTML = '<option value="">جاري التحميل...</option>';

    fetch('ajax/get-cities.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `country_id=${countryId}`
    })
    .then(response => response.json())
    .then(data => {
        citySelect.innerHTML = '<option value="">اختر المدينة</option>';

        if (data.success && data.cities) {
            data.cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city.id;
                option.textContent = city.name;
                citySelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading cities:', error);
        citySelect.innerHTML = '<option value="">خطأ في التحميل</option>';
    });
}

function loadAreas() {
    const cityId = document.getElementById('city_id').value;
    const areaSelect = document.getElementById('area_id');

    // Clear existing options
    areaSelect.innerHTML = '<option value="">اختر المنطقة</option>';

    if (!cityId) return;

    // Show loading
    areaSelect.innerHTML = '<option value="">جاري التحميل...</option>';

    fetch('ajax/get-areas.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `city_id=${cityId}`
    })
    .then(response => response.json())
    .then(data => {
        areaSelect.innerHTML = '<option value="">اختر المنطقة</option>';

        if (data.success && data.areas) {
            data.areas.forEach(area => {
                const option = document.createElement('option');
                option.value = area.id;
                option.textContent = area.name;
                areaSelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading areas:', error);
        areaSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
    });
}

function formatPhoneNumber(event) {
    let value = event.target.value.replace(/\D/g, '');

    // Saudi phone number format
    if (value.startsWith('966')) {
        value = value.substring(3);
    }

    if (value.startsWith('5')) {
        value = '0' + value;
    }

    if (value.length > 10) {
        value = value.substring(0, 10);
    }

    event.target.value = value;
}

function validateForm(event) {
    const form = event.target;
    const submitBtn = form.querySelector('.submit-order');

    // Basic validation
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            field.style.borderColor = '#ef4444';
        } else {
            field.style.borderColor = '#e5e7eb';
        }
    });

    // Phone validation - تم إلغاء التحقق الصارم من تنسيق رقم الهاتف
    const phone = document.getElementById('customer_phone').value;
    if (phone && phone.length < 10) {
        isValid = false;
        document.getElementById('customer_phone').style.borderColor = '#ef4444';
        showToast('رقم الهاتف قصير جداً', 'error');
    }

    // Email validation
    const email = document.getElementById('customer_email').value;
    if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        isValid = false;
        document.getElementById('customer_email').style.borderColor = '#ef4444';
        showToast('البريد الإلكتروني غير صحيح', 'error');
    }

    if (!isValid) {
        event.preventDefault();
        showToast('يرجى تصحيح الأخطاء في النموذج', 'error');
        return false;
    }

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
    submitBtn.disabled = true;

    return true;
}

// Toast notification function
function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 12px;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;

    // Add animation styles if not exists
    if (!document.querySelector('#toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'toast-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
            }
            .toast-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.8;
            }
            .toast-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
            }
        `;
        document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// كود إضافي لضمان عمل تحديث الشحن
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة - بدء ربط أحداث الشحن');

    // ربط أحداث مباشرة
    const shippingRadios = document.querySelectorAll('input[name="shipping_company_id"]');
    console.log('تم العثور على', shippingRadios.length, 'خيار شحن');

    shippingRadios.forEach((radio, index) => {
        console.log(`خيار ${index + 1}: ID=${radio.id}, Value=${radio.value}, Cost=${radio.dataset.cost}`);

        // ربط حدث التغيير
        radio.addEventListener('change', function() {
            console.log('🔄 تم تغيير الشحن إلى:', this.value, 'بتكلفة:', this.dataset.cost);
            setTimeout(updateShippingCostNow, 100);
        });

        // ربط حدث النقر
        radio.addEventListener('click', function() {
            console.log('👆 تم النقر على الشحن:', this.value, 'بتكلفة:', this.dataset.cost);
            setTimeout(updateShippingCostNow, 100);
        });
    });

    // تحديث فوري عند التحميل
    setTimeout(updateShippingCostNow, 2000);

    // إعداد رفع الملفات
    setupFileUpload();
});

// دالة نسخ النص
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('تم نسخ النص بنجاح', 'success');
    }, function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('تم نسخ النص بنجاح', 'success');
    });
}

// إعداد رفع الملفات
function setupFileUpload() {
    const fileInput = document.getElementById('transfer_receipt');
    const uploadArea = document.getElementById('uploadArea');
    const uploadPreview = document.getElementById('uploadPreview');

    if (!fileInput || !uploadArea || !uploadPreview) return;

    // Handle file selection
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            validateAndPreviewFile(file);
        }
    });

    // Handle drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#3b82f6';
        uploadArea.style.background = '#f8fafc';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#d1d5db';
        uploadArea.style.background = 'white';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#d1d5db';
        uploadArea.style.background = 'white';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            validateAndPreviewFile(files[0]);
        }
    });
}

// التحقق من الملف وعرض المعاينة
function validateAndPreviewFile(file) {
    const uploadPreview = document.getElementById('uploadPreview');
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];

    // التحقق من نوع الملف
    if (!allowedTypes.includes(file.type)) {
        showToast('نوع الملف غير مدعوم. يُقبل فقط JPG, PNG, PDF', 'error');
        return false;
    }

    // التحقق من حجم الملف
    if (file.size > maxSize) {
        showToast('حجم الملف كبير جداً. الحد الأقصى 5 ميجا', 'error');
        return false;
    }

    // عرض معاينة الملف
    const fileIcon = file.type.includes('pdf') ? 'fa-file-pdf' : 'fa-file-image';
    const fileSize = (file.size / 1024 / 1024).toFixed(2);

    uploadPreview.innerHTML = `
        <div class="file-info">
            <div class="file-icon">
                <i class="fas ${fileIcon}"></i>
            </div>
            <div class="file-details">
                <h5>${file.name}</h5>
                <small>${fileSize} ميجا</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    uploadPreview.classList.add('show');
    showToast('تم اختيار الملف بنجاح', 'success');
    return true;
}

// إزالة الملف
function removeFile() {
    const fileInput = document.getElementById('transfer_receipt');
    const uploadPreview = document.getElementById('uploadPreview');

    fileInput.value = '';
    uploadPreview.classList.remove('show');
    uploadPreview.innerHTML = '';
}

// دالة تحديث مبسطة
function updateShippingCostNow() {
    console.log('🚀 بدء updateShippingCostNow');

    const selected = document.querySelector('input[name="shipping_company_id"]:checked');
    if (!selected) {
        console.log('❌ لا يوجد شحن مختار');
        return;
    }

    const cost = parseFloat(selected.dataset.cost) || 0;
    const subtotalEl = document.querySelector('.subtotal-amount');
    const subtotal = subtotalEl ? parseFloat(subtotalEl.dataset.amount) || 0 : 0;

    console.log('📊 البيانات:', {
        selectedValue: selected.value,
        cost: cost,
        subtotal: subtotal
    });

    // حساب الإجمالي (دائماً المجموع الفرعي + تكلفة الشحن)
    const total = subtotal + cost;

    console.log('💰 الحسابات:', {
        cost: cost,
        total: total
    });

    // تحديث عرض الشحن (دائماً تكلفة الشركة)
    const shippingDisplay = document.querySelector('.shipping-cost-display');
    if (shippingDisplay) {
        shippingDisplay.innerHTML = cost.toFixed(2) + ' ريال';
        console.log('✅ تم تحديث عرض الشحن');
    }

    // تحديث الإجمالي
    const totalDisplay = document.querySelector('.total-amount');
    if (totalDisplay) {
        totalDisplay.innerHTML = total.toFixed(2) + ' ريال';
        console.log('✅ تم تحديث الإجمالي إلى:', total.toFixed(2));
    }

    console.log('🎉 تم إكمال التحديث');
}
</script>

<?php include 'includes/footer.php'; ?>
