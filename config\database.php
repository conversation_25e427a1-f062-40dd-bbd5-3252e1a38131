<?php
/**
 * إعدادات قاعدة البيانات
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'evening_dresses_store';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * إعدادات عامة للموقع
 */
if (!defined('SITE_NAME')) {
    define('SITE_NAME', 'متجر فساتين السهرة');
}
if (!defined('SITE_URL')) {
    define('SITE_URL', 'http://localhost/sss');
}
if (!defined('ADMIN_URL')) {
    define('ADMIN_URL', SITE_URL . '/admin');
}
if (!defined('UPLOAD_PATH')) {
    define('UPLOAD_PATH', __DIR__ . '/../uploads/');
}
if (!defined('UPLOAD_URL')) {
    define('UPLOAD_URL', SITE_URL . '/uploads/');
}

// إعدادات إضافية للرفع والموقع
if (!defined('MAX_FILE_SIZE')) {
    define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
}
if (!defined('ALLOWED_IMAGE_TYPES')) {
    define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
}
if (!defined('ADMIN_EMAIL')) {
    define('ADMIN_EMAIL', '<EMAIL>');
}

// إعدادات الشحن
if (!defined('FREE_SHIPPING_THRESHOLD')) {
    define('FREE_SHIPPING_THRESHOLD', 500); // شحن مجاني للطلبات أكثر من 500 ريال
}
if (!defined('DEFAULT_SHIPPING_COST')) {
    define('DEFAULT_SHIPPING_COST', 50);
}
if (!defined('TAX_RATE')) {
    define('TAX_RATE', 0.0); // لا توجد ضريبة
}

// إعدادات الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات التوقيت
date_default_timezone_set('Asia/Riyadh');

// إعدادات الأمان
if (!defined('ADMIN_SESSION_NAME')) {
    define('ADMIN_SESSION_NAME', 'admin_logged_in');
}
if (!defined('CART_SESSION_NAME')) {
    define('CART_SESSION_NAME', 'shopping_cart');
}

/**
 * دالة لتنظيف البيانات
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// تم نقل دالة create_slug إلى includes/SlugHelper.php لتجنب التضارب

/**
 * دالة لتحديد الصفحة النشطة
 */
function is_active_page($page) {
    $current_page = basename($_SERVER['PHP_SELF'], '.php');
    return $current_page === $page ? 'active' : '';
}

/**
 * دالة لتنسيق السعر
 */
function format_price($price) {
    return number_format($price, 2) . ' ريال';
}

// تم نقل دالة get_stock_status_text إلى includes/functions.php

// تم نقل دالة get_order_status_text إلى includes/functions.php

/**
 * دالة للحصول على إعدادات الموقع
 */
function get_site_setting($key, $default = '') {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $stmt = $conn->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// تم نقل جميع الدوال إلى includes/functions.php لتجنب التضارب
?>
