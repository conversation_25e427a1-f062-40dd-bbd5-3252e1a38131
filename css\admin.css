/* Admin Panel Styles - Salla-inspired Design */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.admin-body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8fafc;
    color: #2d3748;
    line-height: 1.6;
    direction: rtl;
}

/* Header Styles */
.admin-header {
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-logo a {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #2d3748;
}

.logo-img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
    color: #6366f1;
}

/* Search Bar */
.header-search {
    flex: 1;
    max-width: 400px;
    margin: 0 24px;
}

.search-form {
    position: relative;
    display: flex;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: #f7fafc;
    transition: all 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: #6366f1;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-action {
    position: relative;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: #64748b;
}

.action-btn:hover {
    background: #f1f5f9;
    color: #2d3748;
}

.badge {
    position: absolute;
    top: -4px;
    left: -4px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* User Button */
.user-btn {
    padding: 8px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #6366f1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.user-name {
    font-weight: 500;
    color: #2d3748;
}

/* Dropdown Menus */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s;
    z-index: 1001;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.dropdown-header h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: #2d3748;
    transition: all 0.2s;
}

.dropdown-item:hover {
    background: #f8fafc;
}

.dropdown-item.text-danger {
    color: #ef4444;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 8px 0;
}

/* Mobile Header */
.mobile-header {
    display: none;
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    align-items: center;
    justify-content: space-between;
}

.mobile-menu-toggle {
    background: none;
    border: none;
    font-size: 20px;
    color: #2d3748;
    cursor: pointer;
}

.mobile-logo {
    font-size: 18px;
    font-weight: 700;
    color: #6366f1;
}

.mobile-actions {
    display: flex;
    gap: 8px;
}

.mobile-action-btn {
    position: relative;
    background: none;
    border: none;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    color: #64748b;
}

/* Admin Container */
.admin-container {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    display: flex;
    position: relative; /* إضافة موضع نسبي */
}

/* Sidebar */
.admin-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e2e8f0;
    height: calc(100vh - 70px);
    overflow-y: auto;
    position: fixed;
    right: 0;
    top: 70px;
    z-index: 999;
    transition: transform 0.3s ease;
}

.sidebar-content {
    padding: 24px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.sidebar-nav {
    flex: 1;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    text-decoration: none;
    color: #64748b;
    transition: all 0.2s;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: #f8fafc;
    color: #6366f1;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #6366f1;
}

.nav-icon {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.nav-text {
    flex: 1;
    font-weight: 500;
}

.nav-arrow {
    font-size: 12px;
    transition: transform 0.2s;
}

/* Submenu */
.nav-submenu {
    list-style: none;
    background: #f8fafc;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-submenu li {
    margin: 0;
}

.nav-submenu a {
    display: block;
    padding: 8px 24px 8px 56px;
    text-decoration: none;
    color: #64748b;
    font-size: 14px;
    transition: all 0.2s;
}

.nav-submenu a:hover,
.nav-submenu a.active {
    background: #e2e8f0;
    color: #6366f1;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 300px; /* زيادة المساحة لتجنب تداخل السايت بار */
    padding: 24px;
    min-height: calc(100vh - 70px);
    width: calc(100% - 300px); /* تحديد العرض بوضوح */
    box-sizing: border-box; /* تضمين الحشو في العرض */
    overflow-x: auto; /* تمرير أفقي عند الحاجة */
}

/* Page Header */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.page-title h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 4px;
}

.page-title p {
    color: #64748b;
    font-size: 14px;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.current-time {
    color: #64748b;
    font-size: 14px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s;
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 4px;
}

.stat-content p {
    color: #64748b;
    font-size: 14px;
}

.stat-trend {
    color: #10b981;
    font-size: 12px;
}

.stat-trend.positive {
    color: #10b981;
}

.stat-trend.negative {
    color: #ef4444;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1a202c;
}

.card-content {
    padding: 24px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.btn-primary {
    background: #6366f1;
    color: white;
}

.btn-primary:hover {
    background: #5b5bd6;
}

.btn-outline {
    background: transparent;
    color: #6366f1;
    border: 1px solid #6366f1;
}

.btn-outline:hover {
    background: #6366f1;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.table td {
    color: #64748b;
    font-size: 14px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fef3c7;
    color: #d97706;
}

.status-confirmed {
    background: #dbeafe;
    color: #2563eb;
}

.status-shipped {
    background: #e0e7ff;
    color: #7c3aed;
}

.status-delivered {
    background: #d1fae5;
    color: #059669;
}

.status-cancelled {
    background: #fee2e2;
    color: #dc2626;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #64748b;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 16px;
}

/* Alerts */
.alert {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    position: relative;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-close {
    position: absolute;
    left: 12px;
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header {
        display: flex; /* إظهار الهيدر في الموبايل */
    }

    /* إظهار زر toggle في الموبايل */
    .sidebar-toggle {
        display: block !important;
    }

    .admin-container {
        margin-top: 70px;
    }

    .admin-sidebar {
        transform: translateX(100%);
        width: 100%;
        max-width: 320px;
        z-index: 1001; /* أعلى من overlay */
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 16px;
        width: 100%; /* عرض كامل في الشاشات الصغيرة */
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    display: none; /* مخفي افتراضياً */
}

/* إظهار overlay في الموبايل فقط */
@media (max-width: 768px) {
    .sidebar-overlay {
        display: block;
    }
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 24px;
    border-top: 1px solid #e2e8f0;
    margin-top: auto;
}

.storage-info {
    margin-bottom: 16px;
}

.storage-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #64748b;
}

.storage-bar {
    height: 6px;
    background: #f1f5f9;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 3px;
    transition: width 0.3s;
}

.storage-text {
    font-size: 12px;
    color: #64748b;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

.sidebar-action {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    text-decoration: none;
    color: #64748b;
    font-size: 12px;
    transition: all 0.2s;
    flex: 1;
    justify-content: center;
}

.sidebar-action:hover {
    background: #e2e8f0;
    color: #374151;
}
