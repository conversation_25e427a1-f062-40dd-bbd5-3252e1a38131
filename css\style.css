/* التصميم الرابع - Shein Style */

:root {
    --primary-pink: #855df5;
    --secondary-pink: #6c47d9;
    --light-pink: #b19bff;
    --dark-pink: #5a3bb8;
    --black: #000000;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--white);
    color: var(--gray-800);
    line-height: 1.5;
    font-size: 14px;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Banner */
.top-banner {
    background: linear-gradient(45deg, var(--primary-pink), var(--secondary-pink));
    color: var(--white);
    padding: 8px 0;
    font-size: 13px;
    position: relative;
}

.banner-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.banner-text {
    text-align: center;
    font-weight: 500;
}

.banner-close {
    position: absolute;
    left: 20px;
    background: none;
    border: none;
    color: var(--white);
    font-size: 18px;
    cursor: pointer;
    opacity: 0.8;
}

.banner-close:hover {
    opacity: 1;
}

/* Header */
.header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Mobile Header */
.mobile-header {
    display: block;
    transition: all 0.3s ease;
}

.mobile-header.compact {
    padding: 5px 0;
}

.mobile-header.compact .mobile-header-top {
    margin-bottom: 0;
    padding: 5px 0;
}

.mobile-header.compact .mobile-actions .action-item {
    padding: 4px 2px;
    min-width: 40px;
}

.mobile-header.compact .mobile-actions .action-item i {
    font-size: 16px;
    margin-bottom: 1px;
}

.mobile-header.compact .mobile-actions .action-item .action-text {
    font-size: 9px;
}

.mobile-header.compact .logo .logo-text {
    font-size: 20px;
}

.mobile-header.compact .menu-toggle-btn {
    padding: 4px;
    font-size: 18px;
}

.mobile-header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    transition: margin-bottom 0.3s ease;
}

.menu-toggle-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--gray-700);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s;
}

.menu-toggle-btn:hover {
    background: rgba(255, 105, 180, 0.1);
    color: var(--primary-pink);
}

.mobile-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mobile-actions .action-item {
    position: relative;
    background: none;
    border: none;
    font-size: 16px;
    color: var(--gray-700);
    padding: 8px 4px;
    border-radius: 8px;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    min-width: 50px;
}

.mobile-actions .action-item:hover {
    background: rgba(255, 105, 180, 0.1);
    color: var(--primary-pink);
}

.mobile-actions .action-item i {
    font-size: 18px;
    margin-bottom: 2px;
}

.mobile-actions .action-item .action-text {
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    line-height: 1;
    margin-top: 2px;
}

.mobile-actions .action-item .badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--primary-pink);
    color: var(--white);
    font-size: 9px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 8px;
    min-width: 14px;
    text-align: center;
    line-height: 1;
}

.mobile-search {
    position: relative;
    transition: all 0.3s ease;
    transform: translateY(0);
    opacity: 1;
    max-height: 60px;
    overflow: hidden;
}

.mobile-search.hidden {
    max-height: 0;
    opacity: 0;
    transform: translateY(-100%);
    margin: 0;
    padding: 0;
}

/* Desktop Header */
.desktop-header .header-content {
    display: grid;
    grid-template-columns: 200px 1fr 300px;
    gap: 20px;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: var(--black);
}

.logo img {
    width: 90px;
    height: 40px;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-pink);
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    background: var(--gray-100);
    border-radius: 25px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: border-color 0.2s ease;
}

.search-box:focus-within {
    border-color: var(--primary-pink);
}

.search-box input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: none;
    background: transparent;
    outline: none;
    font-size: 14px;
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-pink);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: var(--white);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: var(--secondary-pink);
}

.trending-searches {
    margin-top: 8px;
    font-size: 12px;
    color: var(--gray-600);
}

.trending-searches span {
    margin-left: 10px;
}

.trending-searches a {
    color: var(--primary-pink);
    text-decoration: none;
    margin: 0 8px;
}

.trending-searches a:hover {
    text-decoration: underline;
}

.header-actions {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--gray-700);
    position: relative;
    transition: color 0.2s ease;
}

.action-item:hover {
    color: var(--primary-pink);
}

.action-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.action-text {
    font-size: 12px;
    font-weight: 500;
}

.badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background: var(--primary-pink);
    color: var(--white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

/* Navigation */
.main-nav {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 12px 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-item {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary-pink);
    background: var(--gray-100);
}

.nav-item.sale {
    color: var(--danger);
    font-weight: 600;
}

.nav-item.sale::after {
    content: 'HOT';
    position: absolute;
    top: -5px;
    right: -10px;
    background: var(--danger);
    color: var(--white);
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 8px;
}

.nav-extras {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: var(--gray-600);
}

.currency,
.language {
    padding: 4px 8px;
    border: 1px solid var(--gray-300);
    border-radius: 12px;
    cursor: pointer;
}

/* Hero Slider */
.hero-slider {
    background: linear-gradient(135deg, var(--light-pink), var(--primary-pink));
    padding: 60px 0;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    color: var(--white);
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-price {
    margin-bottom: 30px;
}

.price-from {
    color: var(--white);
    font-size: 1rem;
    opacity: 0.8;
}

.price {
    color: var(--white);
    font-size: 2.5rem;
    font-weight: 700;
    margin-right: 10px;
}

.btn-hero {
    background: var(--white);
    color: var(--primary-pink);
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.2s ease;
    display: inline-block;
}

.btn-hero:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Flash Sale */
.flash-sale {
    background: var(--gray-100);
    padding: 40px 0;
}

.sale-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.sale-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sale-title i {
    color: var(--warning);
    font-size: 24px;
}

.sale-title h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.sale-subtitle {
    color: var(--gray-600);
    font-size: 14px;
    margin-right: 10px;
}

.countdown {
    display: flex;
    gap: 10px;
}

.time-unit {
    background: var(--primary-pink);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 8px;
    text-align: center;
    min-width: 50px;
}

.time-unit .number {
    display: block;
    font-size: 18px;
    font-weight: 700;
}

.time-unit .label {
    font-size: 10px;
    opacity: 0.8;
}

.view-all {
    color: var(--primary-pink);
    text-decoration: none;
    font-weight: 600;
    padding: 8px 16px;
    border: 2px solid var(--primary-pink);
    border-radius: 20px;
    transition: all 0.2s ease;
}

.view-all:hover {
    background: var(--primary-pink);
    color: var(--white);
}

.sale-products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* Product Cards */
.product-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--danger);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.product-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge.trending {
    background: var(--primary-pink);
}

.badge.new {
    background: var(--success);
}

.badge.hot {
    background: var(--danger);
}

.badge.limited {
    background: var(--warning);
    color: var(--gray-800);
}

.badge.premium {
    background: var(--info);
}

.badge.featured {
    background: var(--warning);
    color: var(--gray-800);
}

.badge.sale {
    background: var(--danger);
}

.badge.out-of-stock {
    background: var(--gray-500);
}

.quick-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.product-card:hover .quick-actions {
    opacity: 1;
}

.quick-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-btn:hover {
    background: var(--primary-pink);
    color: var(--white);
    transform: scale(1.1);
}

.quick-btn.active {
    background: var(--primary-pink);
    color: var(--white);
}

.size-guide {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.product-card:hover .size-guide {
    opacity: 1;
}

.product-info {
    padding: 15px;
}

.product-category {
    color: var(--gray-500);
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 5px;
}

.product-info h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.product-title a {
    text-decoration: none;
    color: var(--gray-800);
    transition: color 0.2s;
}

.product-title a:hover {
    color: var(--primary-pink);
}

.rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 8px;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 8px;
}

.stars {
    color: var(--warning);
    font-size: 12px;
}

.rating-count {
    color: var(--gray-500);
    font-size: 11px;
}

.price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.price .current,
.current-price,
.sale-price {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-pink);
}

.price .original,
.original-price {
    font-size: 12px;
    color: var(--gray-500);
    text-decoration: line-through;
}

.price .discount {
    background: var(--danger);
    color: var(--white);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
}

.sold-count {
    color: var(--gray-500);
    font-size: 11px;
    margin-bottom: 8px;
}

.shipping-info {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--success);
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 8px;
}

.color-options {
    display: flex;
    align-items: center;
    gap: 5px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--white);
    box-shadow: 0 0 0 1px var(--gray-300);
    cursor: pointer;
}

.more-colors {
    color: var(--gray-500);
    font-size: 11px;
    margin-right: 5px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 105, 180, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--primary-pink);
    border: 2px solid var(--primary-pink);
}

.btn-outline:hover {
    background: var(--primary-pink);
    color: white;
}

.btn-disabled {
    background: var(--gray-200);
    color: var(--gray-500);
    cursor: not-allowed;
}

.add-to-cart {
    width: 100%;
    justify-content: center;
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 10px;
}

.section-header p {
    color: var(--gray-600);
    font-size: 16px;
}

.categories-section,
.trending-section,
.featured-products-section,
.latest-products-section {
    padding: 20px 0;
}

.latest-products-section {
    background: var(--gray-100);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.category-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
    text-decoration: none;
    color: inherit;
    display: block;
}

.category-card:hover {
    transform: translateY(-5px);
    text-decoration: none;
    color: inherit;
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: var(--white);
    padding: 20px 15px 15px;
}

.category-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.item-count {
    font-size: 12px;
    opacity: 0.8;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.section-footer {
    text-align: center;
    margin-top: 40px;
}

.btn-view-all {
    background: var(--primary-pink);
    color: var(--white);
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-view-all:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(45deg, var(--primary-pink), var(--secondary-pink));
    padding: 50px 0;
    color: var(--white);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.newsletter-text h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.newsletter-text p {
    opacity: 0.9;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    gap: 10px;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.newsletter-form button {
    background: var(--white);
    color: var(--primary-pink);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.newsletter-form button:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary-pink);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-pink);
}

.social-links {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.social-links a {
    width: 35px;
    height: 35px;
    background: var(--primary-pink);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.2s ease;
}

.social-links a:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

.app-download p {
    margin-bottom: 10px;
    font-size: 14px;
}

.app-buttons {
    display: flex;
    gap: 10px;
}

.app-buttons img {
    height: 35px;
    border-radius: 5px;
}

.payment-methods {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.payment-methods img {
    height: 25px;
    border-radius: 3px;
}

.security-badges {
    display: flex;
    gap: 8px;
}

.security-badges img {
    height: 30px;
    border-radius: 3px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-bottom p {
    margin: 0;
    color: var(--gray-400);
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary-pink);
}

/* Breadcrumb */
.breadcrumb-section {
    background: var(--gray-100);
    padding: 15px 0;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 13px;
}

.breadcrumb-item a {
    color: var(--gray-600);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--primary-pink);
    font-weight: 500;
}

/* Category Header */
.category-header {
    background: linear-gradient(135deg, var(--light-pink), var(--primary-pink));
    padding: 40px 0;
    color: var(--white);
}

.category-banner {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: center;
}

.banner-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-content p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    opacity: 0.9;
}

.category-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-item strong {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-item span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.banner-image img {
    width: 100%;
    max-width: 300px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Products Section */
.products-section {
    padding: 40px 0;
}

/* Filters Sidebar */
.filters-sidebar {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 120px;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.filter-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.clear-filters {
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-filters:hover {
    background: var(--secondary-pink);
}

.filter-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-group h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 15px;
}

/* Price Filter */
.price-filter {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.price-range {
    position: relative;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
}

.range-slider {
    position: absolute;
    width: 100%;
    height: 6px;
    background: none;
    pointer-events: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-slider::-webkit-slider-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-pink);
    cursor: pointer;
    pointer-events: all;
    -webkit-appearance: none;
    appearance: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-inputs input {
    flex: 1;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    padding: 8px 10px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s ease;
}

.price-inputs input:focus {
    border-color: var(--primary-pink);
}

.price-inputs span {
    color: var(--gray-500);
    font-weight: 600;
}

/* Filter Options */
.filter-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
    transition: color 0.2s ease;
}

.filter-option:hover {
    color: var(--primary-pink);
}

.filter-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.filter-option input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-pink);
    border-color: var(--primary-pink);
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-weight: bold;
    font-size: 12px;
}

.filter-option span:not(.checkmark):not(.count) {
    flex: 1;
    font-weight: 500;
}

.count {
    color: var(--gray-500);
    font-size: 12px;
}

/* Color Filter */
.color-filter {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

.color-filter .color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-filter .color-option:hover,
.color-filter .color-option.active {
    border-color: var(--gray-800);
    transform: scale(1.1);
}

/* Rating Filter */
.rating-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
}

.rating-option input[type="checkbox"] {
    display: none;
}

.rating-option .stars {
    color: var(--warning);
    font-size: 14px;
}

.rating-option span {
    color: var(--gray-600);
    font-size: 13px;
}

/* Products Header */
.products-header {
    background: var(--white);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.results-count {
    font-weight: 600;
    color: var(--gray-700);
}

.sort-select {
    border: 2px solid var(--gray-300);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    background: var(--white);
    color: var(--gray-700);
    outline: none;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.sort-select:focus {
    border-color: var(--primary-pink);
}

/* Load More */
.load-more-section {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.load-more-btn:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

.pagination-info {
    color: var(--gray-600);
    font-size: 14px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-600);
}

.empty-state i {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--gray-700);
}

.empty-state p {
    margin-bottom: 20px;
}

/* Mobile Navigation */
.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.mobile-nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-navigation {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: var(--white);
    z-index: 1999;
    transition: right 0.3s;
    overflow-y: auto;
}

.mobile-navigation.active {
    right: 0;
}

.mobile-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-pink));
    color: var(--white);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.user-info h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.user-info p {
    margin: 0;
    font-size: 12px;
    opacity: 0.8;
}

.nav-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s;
}

.nav-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-nav-content {
    padding: 20px;
}

.nav-section {
    margin-bottom: 25px;
}

.nav-section h5 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-200);
}

.nav-list {
    list-style: none;
}

.nav-list li {
    margin-bottom: 8px;
}

.nav-list a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    transition: all 0.3s;
    border-radius: 8px;
}

.nav-list a:hover {
    color: var(--primary-pink);
    background: rgba(255, 105, 180, 0.05);
    padding-left: 8px;
}

.nav-list a i {
    width: 20px;
    text-align: center;
    color: var(--gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .desktop-header {
        display: none !important;
    }

    .mobile-header {
        display: block !important;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .sale-header {
        flex-direction: column;
        text-align: center;
    }
    
    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .category-banner {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .banner-content h1 {
        font-size: 2rem;
    }

    .nav-menu {
        display: none;
    }

    .nav-extras {
        display: none;
    }
}

@media (min-width: 769px) {
    .mobile-header {
        display: none !important;
    }

    .desktop-header {
        display: block !important;
    }
}

/* Utility Functions */
.format_price {
    color: var(--primary-pink);
    font-weight: 600;
}

/* Performance */
* {
    animation: none !important;
    transition-duration: 0.2s !important;
}

.product-image img,
.category-card img,
.hero-image img {
    will-change: transform;
}

/* Mobile Filter Toggle */
.mobile-filter-toggle {
    display: none;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 12px 20px;
    margin-bottom: 20px;
    cursor: pointer;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.mobile-filter-toggle:hover {
    border-color: var(--primary-pink);
    box-shadow: 0 4px 12px rgba(133, 93, 245, 0.15);
}

.mobile-filter-toggle .filter-icon {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray-800);
}

.mobile-filter-toggle .filter-count {
    background: var(--primary-pink);
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 20px;
    min-width: 20px;
    text-align: center;
}

/* Enhanced Filters Sidebar */
.filters-sidebar {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: sticky;
    top: 100px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    transition: all 0.3s ease;
}

.filters-sidebar::-webkit-scrollbar {
    width: 6px;
}

.filters-sidebar::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

.filters-sidebar::-webkit-scrollbar-thumb {
    background: var(--primary-pink);
    border-radius: 3px;
}

.filter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.filter-overlay.show {
    opacity: 1;
    visibility: visible;
}

.filter-close {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: var(--gray-800);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.filter-close:hover {
    background: var(--gray-100);
}

/* Mobile Responsive Filters */
@media (max-width: 991px) {
    .mobile-filter-toggle {
        display: flex;
    }
    
    .filters-sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 320px;
        height: 100vh;
        z-index: 1050;
        border-radius: 0;
        max-height: none;
        overflow-y: auto;
        transition: right 0.3s ease;
    }
    
    .filters-sidebar.show {
        right: 0;
    }
    
    .filter-header {
        position: sticky;
        top: 0;
        background: var(--white);
        z-index: 10;
        margin: -24px -24px 24px -24px;
        padding: 20px 24px 16px 24px;
        border-bottom: 2px solid var(--gray-100);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .filter-close {
        display: flex;
    }
}

@media (max-width: 576px) {
    .filters-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .color-filter {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .price-inputs {
        flex-direction: column;
        gap: 8px;
    }
    
    .price-inputs span {
        display: none;
    }
}

/* Filter Actions */
.filter-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}
