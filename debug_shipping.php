<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع مشكلة تحديث الشحن</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .shipping-option { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .shipping-label { display: flex; justify-content: space-between; cursor: pointer; }
        .totals { background: #f9f9f9; padding: 15px; border-radius: 5px; }
        .debug-log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 200px; overflow-y: auto; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>

<h1>🔍 تتبع مشكلة تحديث الشحن</h1>

<?php
session_start();
require_once 'config/database.php';

// محاكاة بيانات السلة
$subtotal = 300; // مجموع فرعي أقل من 500 لاختبار رسوم الشحن

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // جلب شركات الشحن
    $shipping_query = "SELECT * FROM shipping_companies WHERE is_active = 1 ORDER BY cost";
    $shipping_stmt = $conn->prepare($shipping_query);
    $shipping_stmt->execute();
    $shipping_companies = $shipping_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    $shipping_companies = [];
}
?>

<div class="section">
    <h2>بيانات الاختبار:</h2>
    <p><strong>المجموع الفرعي:</strong> <?php echo $subtotal; ?> ريال</p>
    <p><strong>عدد شركات الشحن:</strong> <?php echo count($shipping_companies); ?></p>
</div>

<div class="section">
    <h2>اختبار شركات الشحن:</h2>
    
    <div class="totals">
        <div>
            <span>المجموع الفرعي:</span>
            <span class="subtotal-amount" data-amount="<?php echo $subtotal; ?>"><?php echo $subtotal; ?>.00 ريال</span>
        </div>
        <div>
            <span>الشحن:</span>
            <span class="shipping-cost-display">لم يحدد بعد</span>
        </div>
        <div style="font-weight: bold; margin-top: 10px;">
            <span>الإجمالي:</span>
            <span class="total-amount"><?php echo $subtotal; ?>.00 ريال</span>
        </div>
    </div>
    
    <h3>اختر شركة الشحن:</h3>
    
    <?php if (!empty($shipping_companies)): ?>
        <?php foreach ($shipping_companies as $index => $company): ?>
        <div class="shipping-option">
            <input type="radio" id="shipping_<?php echo $company['id']; ?>" 
                   name="shipping_company_id" value="<?php echo $company['id']; ?>" 
                   data-cost="<?php echo $company['cost']; ?>"
                   <?php echo $index === 0 ? 'checked' : ''; ?>>
            <label for="shipping_<?php echo $company['id']; ?>" class="shipping-label">
                <span><?php echo $company['name']; ?></span>
                <span><?php echo $company['cost']; ?> ريال</span>
            </label>
        </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="shipping-option">
            <input type="radio" id="shipping_default" name="shipping_company_id" value="" 
                   data-cost="50" checked>
            <label for="shipping_default" class="shipping-label">
                <span>الشحن العادي</span>
                <span>50.00 ريال</span>
            </label>
        </div>
    <?php endif; ?>
    
    <button class="test-button" onclick="testUpdateShipping()">اختبار التحديث يدوياً</button>
    <button class="test-button" onclick="clearLog()">مسح السجل</button>
</div>

<div class="section">
    <h2>سجل التتبع:</h2>
    <div class="debug-log" id="debug-log">
        سيتم عرض سجل التتبع هنا...
    </div>
</div>

<script>
let logDiv = document.getElementById('debug-log');

function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
    logDiv.scrollTop = logDiv.scrollHeight;
    console.log(message);
}

function clearLog() {
    logDiv.innerHTML = 'تم مسح السجل...<br>';
}

function updateShippingCost() {
    log('🔄 بدء updateShippingCost()');
    
    try {
        const selectedShipping = document.querySelector('input[name="shipping_company_id"]:checked');
        
        if (!selectedShipping) {
            log('❌ لم يتم العثور على شركة شحن مختارة');
            return;
        }
        
        log(`✅ تم العثور على الشحن المختار: ${selectedShipping.value}`);
        
        const shippingCost = parseFloat(selectedShipping.dataset.cost) || 0;
        log(`💰 تكلفة الشحن: ${shippingCost}`);
        
        // الحصول على المجموع الفرعي
        const subtotalElement = document.querySelector('.subtotal-amount');
        if (!subtotalElement) {
            log('❌ لم يتم العثور على عنصر المجموع الفرعي');
            return;
        }
        
        const subtotal = parseFloat(subtotalElement.dataset.amount) || 0;
        log(`📊 المجموع الفرعي: ${subtotal}`);
        
        // حساب تكلفة الشحن الفعلية
        const actualShippingCost = subtotal >= 500 ? 0 : shippingCost;
        log(`🚚 تكلفة الشحن الفعلية: ${actualShippingCost} (${subtotal >= 500 ? 'مجاني' : 'مدفوع'})`);
        
        // تحديث عرض تكلفة الشحن
        const shippingCostElement = document.querySelector('.shipping-cost-display');
        if (!shippingCostElement) {
            log('❌ لم يتم العثور على عنصر عرض تكلفة الشحن');
            return;
        }
        
        if (actualShippingCost === 0) {
            shippingCostElement.innerHTML = '<span style="color: #10b981; font-weight: 600;">مجاني</span>';
            log('✅ تم تحديث عرض الشحن إلى: مجاني');
        } else {
            shippingCostElement.innerHTML = actualShippingCost.toFixed(2) + ' ريال';
            log(`✅ تم تحديث عرض الشحن إلى: ${actualShippingCost.toFixed(2)} ريال`);
        }
        
        // حساب وتحديث الإجمالي
        const total = subtotal + actualShippingCost;
        const totalElement = document.querySelector('.total-amount');
        
        if (!totalElement) {
            log('❌ لم يتم العثور على عنصر الإجمالي');
            return;
        }
        
        totalElement.innerHTML = total.toFixed(2) + ' ريال';
        log(`✅ تم تحديث الإجمالي إلى: ${total.toFixed(2)} ريال`);
        
        log('🎉 تم إكمال التحديث بنجاح!');
        
    } catch (error) {
        log(`❌ خطأ في التحديث: ${error.message}`);
    }
}

function testUpdateShipping() {
    log('🧪 اختبار التحديث اليدوي...');
    updateShippingCost();
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    log('📄 تم تحميل الصفحة');
    
    const shippingInputs = document.querySelectorAll('input[name="shipping_company_id"]');
    log(`🔗 تم العثور على ${shippingInputs.length} خيار شحن`);
    
    shippingInputs.forEach((input, index) => {
        log(`🔗 ربط الحدث للخيار ${index + 1}: ${input.value} (${input.dataset.cost} ريال)`);
        
        input.addEventListener('change', function() {
            log(`🔄 تم تغيير الشحن إلى: ${this.value} (${this.dataset.cost} ريال)`);
            updateShippingCost();
        });
    });
    
    // تحديث أولي
    setTimeout(() => {
        log('🔄 تحديث أولي عند تحميل الصفحة');
        updateShippingCost();
    }, 500);
});
</script>

</body>
</html>
