// التصميم الرابع - Shein Style - JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize Shein-style interactions
    initBannerClose();
    initProductGallery();
    initFilters();
    initWishlist();
    initQuickView();
    initColorOptions();
    initSizeOptions();
    initQuantitySelector();
    initTabs();
    initCountdown();
    initLoadMore();
    
    // Banner close functionality
    function initBannerClose() {
        const bannerClose = document.querySelector('.banner-close');
        const topBanner = document.querySelector('.top-banner');
        
        if (bannerClose && topBanner) {
            bannerClose.addEventListener('click', function() {
                topBanner.style.transform = 'translateY(-100%)';
                setTimeout(() => {
                    topBanner.style.display = 'none';
                }, 300);
            });
        }
    }

    // Product gallery functionality
    function initProductGallery() {
        const thumbnails = document.querySelectorAll('.thumbnail-item');
        const mainImage = document.querySelector('.main-image');
        
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Remove active class from all thumbnails
                thumbnails.forEach(t => t.classList.remove('active'));
                // Add active class to clicked thumbnail
                this.classList.add('active');
                
                // Update main image with smooth transition
                if (mainImage) {
                    const newSrc = this.querySelector('img').src;
                    mainImage.style.opacity = '0';
                    
                    setTimeout(() => {
                        mainImage.src = newSrc;
                        mainImage.style.opacity = '1';
                    }, 200);
                }
            });
        });

        // Zoom functionality
        const zoomBtn = document.querySelector('.zoom-btn');
        if (zoomBtn && mainImage) {
            zoomBtn.addEventListener('click', function() {
                // Create zoom modal
                const modal = document.createElement('div');
                modal.className = 'zoom-modal';
                modal.innerHTML = `
                    <div class="zoom-overlay">
                        <div class="zoom-container">
                            <img src="${mainImage.src}" alt="صورة مكبرة">
                            <button class="close-zoom">×</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                
                // Show modal
                setTimeout(() => {
                    modal.classList.add('show');
                }, 100);
                
                // Close modal
                const closeBtn = modal.querySelector('.close-zoom');
                const overlay = modal.querySelector('.zoom-overlay');
                
                function closeModal() {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        document.body.removeChild(modal);
                    }, 300);
                }
                
                closeBtn.addEventListener('click', closeModal);
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        closeModal();
                    }
                });
            });
        }
    }

    // Filters functionality
    function initFilters() {
        const filterInputs = document.querySelectorAll('.filter-option input, .rating-option input');
        const clearFiltersBtn = document.querySelector('.clear-filters');
        const colorOptions = document.querySelectorAll('.color-filter .color-option');
        
        // Filter change handlers
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                applyFilters();
                updateFilterCount();
            });
        });

        // Color filter handlers
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                this.classList.toggle('active');
                applyFilters();
                updateFilterCount();
            });
        });

        // Clear filters
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function() {
                // Clear checkboxes
                filterInputs.forEach(input => {
                    input.checked = false;
                });
                
                // Clear color selections
                colorOptions.forEach(option => {
                    option.classList.remove('active');
                });
                
                // Reset price inputs
                const priceInputs = document.querySelectorAll('.price-inputs input');
                priceInputs.forEach(input => {
                    input.value = '';
                });
                
                applyFilters();
                updateFilterCount();
            });
        }

        function applyFilters() {
            // Get selected filters
            const selectedSizes = Array.from(document.querySelectorAll('input[name="size"]:checked')).map(input => input.value);
            const selectedRatings = Array.from(document.querySelectorAll('input[name="rating"]:checked')).map(input => input.value);
            const selectedDiscounts = Array.from(document.querySelectorAll('input[name="discount"]:checked')).map(input => input.value);
            const selectedColors = Array.from(document.querySelectorAll('.color-filter .color-option.active')).map(option => option.dataset.color);
            
            console.log('Applying filters:', {
                sizes: selectedSizes,
                ratings: selectedRatings,
                discounts: selectedDiscounts,
                colors: selectedColors
            });
            
            // Add visual feedback
            showFilteringLoader();
        }

        function updateFilterCount() {
            const activeFilters = document.querySelectorAll('.filter-option input:checked, .rating-option input:checked, .color-filter .color-option.active').length;
            
            if (clearFiltersBtn) {
                clearFiltersBtn.textContent = activeFilters > 0 ? `مسح الكل (${activeFilters})` : 'مسح الكل';
                clearFiltersBtn.style.display = activeFilters > 0 ? 'block' : 'inline-block';
            }
        }

        function showFilteringLoader() {
            const productsGrid = document.querySelector('.products-grid');
            if (productsGrid) {
                productsGrid.style.opacity = '0.6';
                setTimeout(() => {
                    productsGrid.style.opacity = '1';
                }, 800);
            }
        }
    }

    // Wishlist functionality
    function initWishlist() {
        const wishlistBtns = document.querySelectorAll('.wishlist, .quick-btn.wishlist');
        
        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const icon = this.querySelector('i');
                const isActive = this.classList.contains('active');
                
                if (isActive) {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    this.classList.remove('active');
                } else {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    this.classList.add('active');
                }
                
                // Add animation
                this.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
                
                // Show notification
                showNotification(isActive ? 'تم إزالة المنتج من المفضلة' : 'تم إضافة المنتج للمفضلة');
            });
        });
    }

    // Quick view functionality
    function initQuickView() {
        const quickViewBtns = document.querySelectorAll('.quick-view, .quick-btn.quick-view');
        
        quickViewBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Create quick view modal
                const modal = document.createElement('div');
                modal.className = 'quick-view-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <button class="close-modal">×</button>
                            <div class="quick-view-content">
                                <div class="quick-view-image">
                                    <img src="../files/1721421479.jpg" alt="عرض سريع">
                                </div>
                                <div class="quick-view-info">
                                    <h3>فستان سهرة أنيق</h3>
                                    <div class="rating">
                                        <div class="stars">★★★★★</div>
                                        <span>(234)</span>
                                    </div>
                                    <div class="price">
                                        <span class="current">299 ر.س</span>
                                        <span class="original">399 ر.س</span>
                                    </div>
                                    <div class="quick-actions">
                                        <button class="btn-add-to-cart">إضافة للسلة</button>
                                        <a href="product.html" class="btn-view-details">عرض التفاصيل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                
                // Show modal
                setTimeout(() => {
                    modal.classList.add('show');
                }, 100);
                
                // Close modal functionality
                const closeBtn = modal.querySelector('.close-modal');
                const overlay = modal.querySelector('.modal-overlay');
                
                function closeModal() {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        document.body.removeChild(modal);
                    }, 300);
                }
                
                closeBtn.addEventListener('click', closeModal);
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        closeModal();
                    }
                });
            });
        });
    }

    // Color options functionality
    function initColorOptions() {
        const colorOptions = document.querySelectorAll('.color-options .color-dot, .color-option');
        
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from siblings
                const siblings = this.parentNode.querySelectorAll('.color-dot, .color-option');
                siblings.forEach(sibling => sibling.classList.remove('active'));
                
                // Add active class to clicked option
                this.classList.add('active');
                
                // Update selected option text
                const selectedOption = this.closest('.option-group')?.querySelector('.selected-option');
                if (selectedOption) {
                    const colorName = this.dataset.color || this.querySelector('.color-name')?.textContent || 'اللون المحدد';
                    selectedOption.textContent = colorName;
                }
            });
        });
    }

    // Size options functionality
    function initSizeOptions() {
        const sizeOptions = document.querySelectorAll('.size-option');
        
        sizeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from siblings
                const siblings = this.parentNode.querySelectorAll('.size-option');
                siblings.forEach(sibling => sibling.classList.remove('active'));
                
                // Add active class to clicked option
                this.classList.add('active');
                
                // Update selected option text
                const selectedOption = this.closest('.option-group')?.querySelector('.selected-option');
                if (selectedOption) {
                    const sizeName = this.dataset.size || this.textContent;
                    selectedOption.textContent = sizeName;
                }
            });
        });
    }

    // Quantity selector functionality
    function initQuantitySelector() {
        const qtyBtns = document.querySelectorAll('.qty-btn');
        
        qtyBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentNode.querySelector('.qty-input');
                const isPlus = this.classList.contains('plus');
                let currentValue = parseInt(input.value) || 1;
                
                if (isPlus) {
                    currentValue = Math.min(currentValue + 1, 10);
                } else {
                    currentValue = Math.max(currentValue - 1, 1);
                }
                
                input.value = currentValue;
                
                // Add animation
                input.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    input.style.transform = 'scale(1)';
                }, 150);
            });
        });
    }

    // Tabs functionality
    function initTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // Remove active class from all buttons and panes
                tabBtns.forEach(b => b.classList.remove('active'));
                tabPanes.forEach(p => p.classList.remove('active'));
                
                // Add active class to clicked button and corresponding pane
                this.classList.add('active');
                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
            });
        });
    }

    // Countdown functionality
    function initCountdown() {
        const timeUnits = document.querySelectorAll('.time-unit');
        
        if (timeUnits.length > 0) {
            function updateCountdown() {
                timeUnits.forEach((unit, index) => {
                    const numberEl = unit.querySelector('.number');
                    let currentValue = parseInt(numberEl.textContent);
                    
                    if (index === 2) { // Seconds
                        currentValue = currentValue > 0 ? currentValue - 1 : 59;
                        if (currentValue === 59) {
                            // Update minutes
                            const minutesEl = timeUnits[1]?.querySelector('.number');
                            if (minutesEl) {
                                let minutes = parseInt(minutesEl.textContent);
                                minutes = minutes > 0 ? minutes - 1 : 59;
                                minutesEl.textContent = minutes.toString().padStart(2, '0');
                                
                                if (minutes === 59) {
                                    // Update hours
                                    const hoursEl = timeUnits[0]?.querySelector('.number');
                                    if (hoursEl) {
                                        let hours = parseInt(hoursEl.textContent);
                                        hours = hours > 0 ? hours - 1 : 23;
                                        hoursEl.textContent = hours.toString().padStart(2, '0');
                                    }
                                }
                            }
                        }
                    }
                    
                    numberEl.textContent = currentValue.toString().padStart(2, '0');
                });
            }
            
            // Update countdown every second
            setInterval(updateCountdown, 1000);
        }
    }

    // Load more functionality
    function initLoadMore() {
        const loadMoreBtn = document.querySelector('.load-more-btn');
        
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                this.disabled = true;
                
                // Simulate loading
                setTimeout(() => {
                    // Reset button
                    this.innerHTML = '<span>عرض المزيد</span><i class="fas fa-chevron-down"></i>';
                    this.disabled = false;
                    
                    // Update pagination info
                    const paginationInfo = document.querySelector('.pagination-info span');
                    if (paginationInfo) {
                        paginationInfo.textContent = 'عرض 48 من أصل 487 منتج';
                    }
                    
                    showNotification('تم تحميل المزيد من المنتجات');
                }, 1500);
            });
        }
    }

    // Add to cart functionality
    const addToCartBtns = document.querySelectorAll('.btn-add-to-cart, .add-to-cart');
    const cartBadge = document.querySelector('.badge');
    
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update cart count
            let currentCount = parseInt(cartBadge.textContent) || 0;
            cartBadge.textContent = currentCount + 1;
            
            // Animate badge
            cartBadge.style.transform = 'scale(1.5)';
            setTimeout(() => {
                cartBadge.style.transform = 'scale(1)';
            }, 200);
            
            // Button feedback
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
            this.style.background = '#28a745';
            
            setTimeout(() => {
                this.innerHTML = originalText;
                this.style.background = '';
            }, 2000);
            
            showNotification('تم إضافة المنتج للسلة بنجاح!');
        });
    });

    // Search functionality
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            console.log('Searching for:', query);
            showNotification(`البحث عن: ${query}`);
        }
    }

    // Sort functionality
    const sortSelect = document.querySelector('.sort-select');
    
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            console.log('Sorting by:', sortValue);
            
            // Show loading
            const productsGrid = document.querySelector('.products-grid');
            if (productsGrid) {
                productsGrid.style.opacity = '0.6';
                setTimeout(() => {
                    productsGrid.style.opacity = '1';
                    showNotification('تم ترتيب المنتجات');
                }, 800);
            }
        });
    }

    // Utility function to show notifications
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'shein-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-check-circle"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

});

// CSS for modals and notifications
const style = document.createElement('style');
style.textContent = `
    .zoom-modal, .quick-view-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .zoom-modal.show, .quick-view-modal.show {
        opacity: 1;
        visibility: visible;
    }
    
    .zoom-overlay, .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }
    
    .zoom-container {
        position: relative;
        max-width: 90vw;
        max-height: 90vh;
    }
    
    .zoom-container img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 10px;
    }
    
    .close-zoom, .close-modal {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 20px;
        cursor: pointer;
        z-index: 1;
    }
    
    .modal-content {
        background: white;
        border-radius: 15px;
        max-width: 800px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
        transform: scale(0.8);
        transition: transform 0.3s ease;
    }
    
    .quick-view-modal.show .modal-content {
        transform: scale(1);
    }
    
    .quick-view-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        padding: 30px;
    }
    
    .quick-view-image img {
        width: 100%;
        border-radius: 10px;
    }
    
    .quick-view-info h3 {
        margin-bottom: 15px;
        color: #333;
    }
    
    .quick-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    
    .btn-view-details {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 600;
    }
    
    .shein-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff69b4;
        color: white;
        padding: 15px 20px;
        border-radius: 25px;
        box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .shein-notification.show {
        transform: translateX(0);
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 600;
    }
    
    @media (max-width: 768px) {
        .quick-view-content {
            grid-template-columns: 1fr;
        }
        
        .zoom-container {
            max-width: 95vw;
            max-height: 95vh;
        }
    }
`;

document.head.appendChild(style);

// Export for use in other scripts
window.SheinDesign = {
    // Add any public methods here
};
