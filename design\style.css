/* التصميم الرابع - Shein Style */

:root {
    --primary-pink: #ff69b4;
    --secondary-pink: #ff1493;
    --light-pink: #ffb6c1;
    --dark-pink: #c71585;
    --black: #000000;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--white);
    color: var(--gray-800);
    line-height: 1.5;
    font-size: 14px;
}

/* Top Banner */
.top-banner {
    background: linear-gradient(45deg, var(--primary-pink), var(--secondary-pink));
    color: var(--white);
    padding: 8px 0;
    font-size: 13px;
    position: relative;
}

.banner-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.banner-text {
    text-align: center;
    font-weight: 500;
}

.banner-close {
    position: absolute;
    left: 20px;
    background: none;
    border: none;
    color: var(--white);
    font-size: 18px;
    cursor: pointer;
    opacity: 0.8;
}

.banner-close:hover {
    opacity: 1;
}

/* Header */
.header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
    display: grid;
    grid-template-columns: 200px 1fr 300px;
    gap: 20px;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: var(--black);
}

.logo img {
    width: 40px;
    height: 40px;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-pink);
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    background: var(--gray-100);
    border-radius: 25px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: border-color 0.2s ease;
}

.search-box:focus-within {
    border-color: var(--primary-pink);
}

.search-box input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: none;
    background: transparent;
    outline: none;
    font-size: 14px;
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-pink);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: var(--white);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: var(--secondary-pink);
}

.trending-searches {
    margin-top: 8px;
    font-size: 12px;
    color: var(--gray-600);
}

.trending-searches span {
    margin-left: 10px;
}

.trending-searches a {
    color: var(--primary-pink);
    text-decoration: none;
    margin: 0 8px;
}

.trending-searches a:hover {
    text-decoration: underline;
}

.header-actions {
    display: flex;
    gap: 20px;
    justify-content: flex-end;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--gray-700);
    position: relative;
    transition: color 0.2s ease;
}

.action-item:hover {
    color: var(--primary-pink);
}

.action-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.action-text {
    font-size: 12px;
    font-weight: 500;
}

.badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background: var(--primary-pink);
    color: var(--white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

/* Navigation */
.main-nav {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 12px 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-item {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary-pink);
    background: var(--gray-100);
}

.nav-item.sale {
    color: var(--danger);
    font-weight: 600;
}

.nav-item.sale::after {
    content: 'HOT';
    position: absolute;
    top: -5px;
    right: -10px;
    background: var(--danger);
    color: var(--white);
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 8px;
}

.nav-extras {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: var(--gray-600);
}

.currency,
.language {
    padding: 4px 8px;
    border: 1px solid var(--gray-300);
    border-radius: 12px;
    cursor: pointer;
}

/* Hero Slider */
.hero-slider {
    background: linear-gradient(135deg, var(--light-pink), var(--primary-pink));
    padding: 60px 0;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 15px;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    color: var(--white);
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-price {
    margin-bottom: 30px;
}

.price-from {
    color: var(--white);
    font-size: 1rem;
    opacity: 0.8;
}

.price {
    color: var(--white);
    font-size: 2.5rem;
    font-weight: 700;
    margin-right: 10px;
}

.btn-hero {
    background: var(--white);
    color: var(--primary-pink);
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.2s ease;
    display: inline-block;
}

.btn-hero:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Flash Sale */
.flash-sale {
    background: var(--gray-100);
    padding: 40px 0;
}

.sale-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.sale-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sale-title i {
    color: var(--warning);
    font-size: 24px;
}

.sale-title h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.sale-subtitle {
    color: var(--gray-600);
    font-size: 14px;
    margin-right: 10px;
}

.countdown {
    display: flex;
    gap: 10px;
}

.time-unit {
    background: var(--primary-pink);
    color: var(--white);
    padding: 8px 12px;
    border-radius: 8px;
    text-align: center;
    min-width: 50px;
}

.time-unit .number {
    display: block;
    font-size: 18px;
    font-weight: 700;
}

.time-unit .label {
    font-size: 10px;
    opacity: 0.8;
}

.view-all {
    color: var(--primary-pink);
    text-decoration: none;
    font-weight: 600;
    padding: 8px 16px;
    border: 2px solid var(--primary-pink);
    border-radius: 20px;
    transition: all 0.2s ease;
}

.view-all:hover {
    background: var(--primary-pink);
    color: var(--white);
}

.sale-products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* Product Cards */
.product-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--danger);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.product-badges {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    color: var(--white);
}

.badge.trending {
    background: var(--primary-pink);
}

.badge.new {
    background: var(--success);
}

.badge.hot {
    background: var(--danger);
}

.badge.limited {
    background: var(--warning);
    color: var(--gray-800);
}

.badge.premium {
    background: var(--info);
}

.quick-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.product-card:hover .quick-actions {
    opacity: 1;
}

.quick-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-btn:hover {
    background: var(--primary-pink);
    color: var(--white);
    transform: scale(1.1);
}

.quick-btn.active {
    background: var(--primary-pink);
    color: var(--white);
}

.size-guide {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.product-card:hover .size-guide {
    opacity: 1;
}

.product-info {
    padding: 15px;
}

.product-category {
    color: var(--gray-500);
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 5px;
}

.product-info h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 8px;
}

.stars {
    color: var(--warning);
    font-size: 12px;
}

.rating-count {
    color: var(--gray-500);
    font-size: 11px;
}

.price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.price .current {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-pink);
}

.price .original {
    font-size: 12px;
    color: var(--gray-500);
    text-decoration: line-through;
}

.price .discount {
    background: var(--danger);
    color: var(--white);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
}

.sold-count {
    color: var(--gray-500);
    font-size: 11px;
    margin-bottom: 8px;
}

.shipping-info {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--success);
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 8px;
}

.color-options {
    display: flex;
    align-items: center;
    gap: 5px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--white);
    box-shadow: 0 0 0 1px var(--gray-300);
    cursor: pointer;
}

.more-colors {
    color: var(--gray-500);
    font-size: 11px;
    margin-right: 5px;
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 10px;
}

.section-header p {
    color: var(--gray-600);
    font-size: 16px;
}

.categories-section,
.trending-section {
    padding: 60px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.category-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: var(--white);
    padding: 20px 15px 15px;
}

.category-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.item-count {
    font-size: 12px;
    opacity: 0.8;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.section-footer {
    text-align: center;
    margin-top: 40px;
}

.btn-view-all {
    background: var(--primary-pink);
    color: var(--white);
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-view-all:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(45deg, var(--primary-pink), var(--secondary-pink));
    padding: 50px 0;
    color: var(--white);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.newsletter-text h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.newsletter-text p {
    opacity: 0.9;
    line-height: 1.6;
}

.newsletter-form {
    display: flex;
    gap: 10px;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.newsletter-form button {
    background: var(--white);
    color: var(--primary-pink);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.newsletter-form button:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary-pink);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-pink);
}

.social-links {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.social-links a {
    width: 35px;
    height: 35px;
    background: var(--primary-pink);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.2s ease;
}

.social-links a:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

.app-download p {
    margin-bottom: 10px;
    font-size: 14px;
}

.app-buttons {
    display: flex;
    gap: 10px;
}

.app-buttons img {
    height: 35px;
    border-radius: 5px;
}

.payment-methods {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.payment-methods img {
    height: 25px;
    border-radius: 3px;
}

.security-badges {
    display: flex;
    gap: 8px;
}

.security-badges img {
    height: 30px;
    border-radius: 3px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-bottom p {
    margin: 0;
    color: var(--gray-400);
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary-pink);
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: center;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .sale-header {
        flex-direction: column;
        text-align: center;
    }
    
    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

/* Breadcrumb */
.breadcrumb-section {
    background: var(--gray-100);
    padding: 15px 0;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 13px;
}

.breadcrumb-item a {
    color: var(--gray-600);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--primary-pink);
    font-weight: 500;
}

/* Product Details */
.product-details {
    padding: 40px 0;
}

.product-gallery {
    position: sticky;
    top: 120px;
}

.main-image-container {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 15px;
    background: var(--gray-100);
}

.main-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

.gallery-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.gallery-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.gallery-btn:hover {
    background: var(--primary-pink);
    color: var(--white);
}

.thumbnail-gallery {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 5px 0;
}

.thumbnail-item {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.2s ease;
}

.thumbnail-item.active {
    border-color: var(--primary-pink);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    padding-left: 30px;
}

.product-header {
    margin-bottom: 25px;
}

.product-category {
    color: var(--gray-500);
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 8px;
}

.product-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 15px;
    line-height: 1.3;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.stars {
    color: var(--warning);
    font-size: 16px;
}

.rating-text {
    font-weight: 600;
    color: var(--gray-700);
}

.rating-count {
    color: var(--gray-500);
    font-size: 14px;
}

.view-reviews {
    color: var(--primary-pink);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.view-reviews:hover {
    text-decoration: underline;
}

.price-section {
    background: var(--gray-100);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
}

.price-main {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.current-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-pink);
}

.original-price {
    font-size: 1.2rem;
    color: var(--gray-500);
    text-decoration: line-through;
}

.discount-percent {
    background: var(--danger);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.price-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.save-amount {
    color: var(--success);
    font-weight: 600;
    font-size: 14px;
}

.price-comparison {
    color: var(--gray-600);
    font-size: 12px;
}

.product-options {
    margin-bottom: 25px;
}

.option-group {
    margin-bottom: 20px;
}

.option-label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--gray-800);
}

.selected-option {
    color: var(--primary-pink);
    font-weight: 500;
}

.color-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-option.active {
    border-color: var(--gray-800);
    transform: scale(1.1);
}

.color-option .color-name {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: var(--gray-600);
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.color-option:hover .color-name {
    opacity: 1;
}

.size-options {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.size-option {
    width: 45px;
    height: 45px;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.size-option:hover,
.size-option.active {
    border-color: var(--primary-pink);
    background: var(--primary-pink);
    color: var(--white);
}

.size-guide-link {
    color: var(--primary-pink);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.size-guide-link:hover {
    text-decoration: underline;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0;
    width: fit-content;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;
}

.qty-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    color: var(--gray-700);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: var(--primary-pink);
    color: var(--white);
}

.qty-input {
    width: 60px;
    height: 40px;
    border: none;
    text-align: center;
    font-weight: 600;
    outline: none;
    background: var(--white);
}

.stock-info {
    margin-top: 5px;
}

.stock-count {
    color: var(--danger);
    font-size: 12px;
    font-weight: 600;
}

.product-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 25px;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-add-to-cart:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

.btn-wishlist,
.btn-compare {
    width: 50px;
    height: 50px;
    border: 2px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-wishlist:hover,
.btn-compare:hover {
    border-color: var(--primary-pink);
    background: var(--primary-pink);
    color: var(--white);
}

.shipping-info {
    background: var(--gray-100);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 25px;
}

.shipping-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.shipping-item:last-child {
    margin-bottom: 0;
}

.shipping-item i {
    color: var(--primary-pink);
    font-size: 18px;
    width: 20px;
}

.shipping-item strong {
    display: block;
    margin-bottom: 2px;
    color: var(--gray-800);
}

.shipping-item p {
    margin: 0;
    color: var(--gray-600);
    font-size: 13px;
}

.product-features {
    margin-bottom: 25px;
}

.product-features h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--gray-800);
}

.product-features ul {
    list-style: none;
    padding: 0;
}

.product-features li {
    padding: 5px 0;
    color: var(--gray-700);
    font-size: 14px;
    position: relative;
    padding-right: 20px;
}

.product-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success);
    font-weight: 600;
}

.social-share {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-top: 1px solid var(--gray-200);
}

.social-share span {
    font-weight: 600;
    color: var(--gray-700);
}

.share-buttons {
    display: flex;
    gap: 8px;
}

.share-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--white);
    transition: all 0.2s ease;
}

.share-btn.whatsapp {
    background: #25d366;
}

.share-btn.instagram {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.share-btn.snapchat {
    background: #fffc00;
    color: var(--black);
}

.share-btn.copy-link {
    background: var(--gray-600);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Product Tabs */
.product-tabs {
    padding: 40px 0;
    background: var(--gray-100);
}

.tabs-nav {
    display: flex;
    gap: 0;
    margin-bottom: 30px;
    background: var(--white);
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-weight: 500;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: var(--primary-pink);
    color: var(--white);
}

.tabs-content {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.description-content h3,
.size-guide-content h3,
.reviews-content h3,
.shipping-content h3 {
    color: var(--gray-800);
    margin-bottom: 20px;
    font-weight: 600;
}

.product-specs h4 {
    margin: 20px 0 10px;
    color: var(--gray-800);
}

.product-specs ul {
    list-style: none;
    padding: 0;
}

.product-specs li {
    padding: 8px 0;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.size-chart {
    overflow-x: auto;
    margin-bottom: 20px;
}

.size-chart table {
    width: 100%;
    border-collapse: collapse;
}

.size-chart th,
.size-chart td {
    padding: 12px;
    text-align: center;
    border: 1px solid var(--gray-200);
}

.size-chart th {
    background: var(--primary-pink);
    color: var(--white);
    font-weight: 600;
}

.size-tips h4 {
    margin-bottom: 15px;
    color: var(--gray-800);
}

.size-tips ul {
    list-style: none;
    padding: 0;
}

.size-tips li {
    padding: 5px 0;
    color: var(--gray-700);
    position: relative;
    padding-right: 20px;
}

.size-tips li::before {
    content: '•';
    position: absolute;
    right: 0;
    color: var(--primary-pink);
    font-weight: 600;
}

/* Reviews */
.reviews-summary {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--gray-100);
    border-radius: 12px;
}

.rating-overview {
    text-align: center;
}

.rating-score {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-pink);
    margin-bottom: 10px;
}

.rating-stars {
    color: var(--warning);
    font-size: 20px;
    margin-bottom: 5px;
}

.rating-count {
    color: var(--gray-600);
    font-size: 14px;
}

.rating-breakdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.rating-bar span:first-child {
    width: 60px;
    color: var(--gray-600);
}

.bar {
    flex: 1;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: var(--warning);
    border-radius: 4px;
}

.rating-bar span:last-child {
    width: 40px;
    text-align: left;
    color: var(--gray-600);
}

.reviews-list {
    margin-bottom: 20px;
}

.review-item {
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-pink);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.reviewer-name {
    font-weight: 600;
    color: var(--gray-800);
}

.review-date {
    color: var(--gray-500);
    font-size: 12px;
}

.review-rating {
    color: var(--warning);
}

.review-content p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: var(--gray-700);
}

.review-images {
    margin-bottom: 15px;
}

.review-images img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    margin-left: 10px;
}

.helpful-btn {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.helpful-btn:hover {
    background: var(--primary-pink);
    color: var(--white);
    border-color: var(--primary-pink);
}

.load-more-reviews {
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.load-more-reviews:hover {
    background: var(--secondary-pink);
}

.shipping-policies {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.policy-section h4 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: var(--gray-800);
}

.policy-section i {
    color: var(--primary-pink);
}

.policy-section ul {
    list-style: none;
    padding: 0;
}

.policy-section li {
    padding: 8px 0;
    color: var(--gray-700);
    position: relative;
    padding-right: 20px;
}

.policy-section li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success);
    font-weight: 600;
}

/* Related Products */
.related-products {
    padding: 60px 0;
}

/* Category Header */
.category-header {
    background: linear-gradient(135deg, var(--light-pink), var(--primary-pink));
    padding: 40px 0;
    color: var(--white);
}

.category-banner {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: center;
}

.banner-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-content p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    opacity: 0.9;
}

.category-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-item strong {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-item span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.banner-image img {
    width: 100%;
    max-width: 300px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Products Section */
.products-section {
    padding: 40px 0;
}

/* Filters Sidebar */
.filters-sidebar {
    background: var(--white);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 120px;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.filter-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.clear-filters {
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-filters:hover {
    background: var(--secondary-pink);
}

.filter-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-group h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 15px;
}

/* Price Filter */
.price-filter {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.price-range {
    position: relative;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
}

.range-slider {
    position: absolute;
    width: 100%;
    height: 6px;
    background: none;
    pointer-events: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-slider::-webkit-slider-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-pink);
    cursor: pointer;
    pointer-events: all;
    -webkit-appearance: none;
    appearance: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-inputs input {
    flex: 1;
    border: 2px solid var(--gray-300);
    border-radius: 8px;
    padding: 8px 10px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s ease;
}

.price-inputs input:focus {
    border-color: var(--primary-pink);
}

.price-inputs span {
    color: var(--gray-500);
    font-weight: 600;
}

/* Filter Options */
.filter-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
    transition: color 0.2s ease;
}

.filter-option:hover {
    color: var(--primary-pink);
}

.filter-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.filter-option input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-pink);
    border-color: var(--primary-pink);
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-weight: bold;
    font-size: 12px;
}

.filter-option span:not(.checkmark):not(.count) {
    flex: 1;
    font-weight: 500;
}

.count {
    color: var(--gray-500);
    font-size: 12px;
}

/* Color Filter */
.color-filter {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

.color-filter .color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-filter .color-option:hover,
.color-filter .color-option.active {
    border-color: var(--gray-800);
    transform: scale(1.1);
}

/* Rating Filter */
.rating-filter {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.rating-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
}

.rating-option input[type="checkbox"] {
    display: none;
}

.rating-option .stars {
    color: var(--warning);
    font-size: 14px;
}

.rating-option span {
    color: var(--gray-600);
    font-size: 13px;
}

/* Products Header */
.products-header {
    background: var(--white);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.results-count {
    font-weight: 600;
    color: var(--gray-700);
}

.sort-select {
    border: 2px solid var(--gray-300);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    background: var(--white);
    color: var(--gray-700);
    outline: none;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.sort-select:focus {
    border-color: var(--primary-pink);
}

/* Load More */
.load-more-section {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    background: var(--primary-pink);
    color: var(--white);
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.load-more-btn:hover {
    background: var(--secondary-pink);
    transform: translateY(-2px);
}

.pagination-info {
    color: var(--gray-600);
    font-size: 14px;
}

/* Performance */
* {
    animation: none !important;
    transition-duration: 0.2s !important;
}

.product-image img,
.category-card img,
.hero-image img {
    will-change: transform;
}
