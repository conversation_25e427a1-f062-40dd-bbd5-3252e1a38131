-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 26, 2025 at 08:21 AM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `evening_dresses_store`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_activity_log`
--

CREATE TABLE `admin_activity_log` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_activity_log`
--

INSERT INTO `admin_activity_log` (`id`, `admin_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(44, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:35:36'),
(45, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:35:41'),
(46, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:22'),
(47, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:37'),
(48, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:38'),
(49, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:38'),
(50, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:39'),
(51, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:49'),
(52, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:49'),
(53, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:50'),
(54, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:36:51'),
(78, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:01'),
(90, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:45'),
(91, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:45'),
(92, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:50'),
(93, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(94, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(95, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(96, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:38:51'),
(117, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0', '2025-07-24 05:43:13'),
(123, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:18:54'),
(124, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:19:01'),
(125, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:19:12'),
(126, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:19'),
(127, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:31'),
(128, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:36'),
(129, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:37'),
(130, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:46'),
(131, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:25:56'),
(132, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:27:22'),
(133, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:28:43'),
(134, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:29:40'),
(135, 1, 'dashboard_view', 'عرض لوحة التحكم الرئيسية', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', '2025-07-24 20:29:47');

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','manager') DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expires` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `created_at`, `updated_at`, `last_login`, `last_activity`, `is_active`, `reset_token`, `reset_token_expires`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', '2025-07-24 02:51:39', '2025-07-24 20:29:47', '2025-07-24 20:18:54', '2025-07-24 20:29:47', 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `areas`
--

CREATE TABLE `areas` (
  `id` int(11) NOT NULL,
  `city_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `areas`
--

INSERT INTO `areas` (`id`, `city_id`, `name`, `is_active`) VALUES
(1, 1, 'العليا', 1),
(2, 1, 'الملز', 1),
(3, 1, 'النخيل', 1),
(4, 2, 'البلد', 1),
(5, 2, 'الروضة', 1),
(6, 4, 'دبي مارينا', 1),
(7, 4, 'وسط البلد', 1),
(8, 1, 'العليا', 1),
(9, 1, 'الملز', 1),
(10, 1, 'النخيل', 1),
(11, 2, 'البلد', 1),
(12, 2, 'الروضة', 1),
(13, 4, 'دبي مارينا', 1),
(14, 4, 'وسط البلد', 1),
(15, 1, 'العليا', 1),
(16, 1, 'الملز', 1),
(17, 1, 'النخيل', 1),
(18, 2, 'البلد', 1),
(19, 2, 'الروضة', 1),
(20, 4, 'دبي مارينا', 1),
(21, 4, 'وسط البلد', 1);

-- --------------------------------------------------------

--
-- Table structure for table `bank_settings`
--

CREATE TABLE `bank_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bank_settings`
--

INSERT INTO `bank_settings` (`id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'bank_name', 'الراجحي', 'اسم البنك', '2025-07-25 14:12:55', '2025-07-25 14:12:55'),
(2, 'account_name', 'مؤسسة اكنان', 'اسم الحساب', '2025-07-25 14:12:55', '2025-07-25 14:12:55'),
(3, 'account_number', '***************', 'رقم الحساب', '2025-07-25 14:12:55', '2025-07-25 14:12:55'),
(4, 'iban', '************************', 'رقم الآيبان', '2025-07-25 14:12:55', '2025-07-25 14:12:55'),
(5, 'transfer_instructions', 'يرجى إرسال إيصال التحويل بعد إتمام العملية', 'تعليمات التحويل', '2025-07-25 14:12:55', '2025-07-25 14:12:55');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `id` int(11) NOT NULL,
  `session_id` varchar(100) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cart`
--

INSERT INTO `cart` (`id`, `session_id`, `product_id`, `color_id`, `size_id`, `quantity`, `created_at`, `updated_at`) VALUES
(83, 'size_test_68838a3e90f23', 24, NULL, 52, 1, '2025-07-25 14:32:42', '2025-07-25 14:32:46');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `seo_content` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `seo_title`, `description`, `seo_content`, `image`, `parent_id`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'فساتين سهرة', 'evening-dresses', 'فساتين سهرة أنيقة وراقية', 'مجموعة متنوعة من فساتين السهرة الأنيقة', '', 'category_1753455298_3573.jpg', NULL, 1, 1, '2025-07-24 02:51:39', '2025-07-25 14:54:58'),
(2, 'فساتين طويلة', 'long-dresses', 'فساتين سهرة طويلة', 'فساتين سهرة طويلة وأنيقة لجميع المناسبات', NULL, NULL, NULL, 2, 1, '2025-07-24 02:51:39', '2025-07-24 02:51:39'),
(3, 'فساتين قصيرة', 'short-dresses', 'فساتين سهرة قصيرة', 'فساتين سهرة قصيرة عصرية وجذابة', NULL, NULL, NULL, 3, 1, '2025-07-24 02:51:39', '2025-07-24 02:51:39'),
(9, 'فساتين مناسبات', 'occasion-dresses', 'احداث فساتين مناسبات فى السعودية', 'تسوقي احداث موديلات فساتين مناسبات', 'تسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسباتتسوقي احداث موديلات فساتين مناسبات', 'category_1753397329_9446.jpg', NULL, 0, 1, '2025-07-24 22:31:09', '2025-07-25 15:09:27'),
(10, 'قسم اختبار', 'test-category', NULL, 'هذا قسم للاختبار', NULL, NULL, NULL, 0, 1, '2025-07-25 15:59:02', '2025-07-25 15:59:02');

-- --------------------------------------------------------

--
-- Table structure for table `cities`
--

CREATE TABLE `cities` (
  `id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cities`
--

INSERT INTO `cities` (`id`, `country_id`, `name`, `is_active`) VALUES
(1, 1, 'الرياض', 1),
(2, 1, 'جدة', 1),
(3, 1, 'الدمام', 1),
(4, 2, 'دبي', 1),
(5, 2, 'أبوظبي', 1),
(6, 3, 'الكويت', 1),
(7, 1, 'الرياض', 1),
(8, 1, 'جدة', 1),
(9, 1, 'الدمام', 1),
(10, 2, 'دبي', 1),
(11, 2, 'أبوظبي', 1),
(12, 3, 'الكويت', 1),
(13, 1, 'الرياض', 1),
(14, 1, 'جدة', 1),
(15, 1, 'الدمام', 1),
(16, 2, 'دبي', 1),
(17, 2, 'أبوظبي', 1),
(18, 3, 'الكويت', 1);

-- --------------------------------------------------------

--
-- Table structure for table `colors`
--

CREATE TABLE `colors` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `hex_code` varchar(7) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(3) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `name`, `code`, `is_active`) VALUES
(1, 'السعودية', 'SA', 1),
(2, 'الإمارات', 'AE', 1),
(3, 'الكويت', 'KW', 1),
(4, 'السعودية', 'SA', 1),
(5, 'الإمارات', 'AE', 1),
(6, 'الكويت', 'KW', 1),
(7, 'السعودية', 'SA', 1),
(8, 'الإمارات', 'AE', 1),
(9, 'الكويت', 'KW', 1);

-- --------------------------------------------------------

--
-- Table structure for table `coupons`
--

CREATE TABLE `coupons` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `type` enum('fixed','percentage') NOT NULL DEFAULT 'fixed',
  `value` decimal(10,2) NOT NULL,
  `minimum_amount` decimal(10,2) DEFAULT NULL,
  `maximum_discount` decimal(10,2) DEFAULT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `used_count` int(11) DEFAULT 0,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `coupon_usage`
--

CREATE TABLE `coupon_usage` (
  `id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `discount_amount` decimal(10,2) NOT NULL,
  `used_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_addresses`
--

CREATE TABLE `customer_addresses` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `area_id` int(11) NOT NULL,
  `address_line` varchar(255) NOT NULL,
  `is_default` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` enum('order','product','system','customer') NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `customer_phone_alt` varchar(20) DEFAULT NULL,
  `city_id` int(11) NOT NULL,
  `area_id` int(11) NOT NULL,
  `shipping_address` text NOT NULL,
  `landmark` varchar(255) DEFAULT NULL,
  `delivery_time` varchar(50) DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `shipping_company_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL,
  `coupon_code` varchar(50) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `subtotal` decimal(10,2) NOT NULL,
  `shipping_cost` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cod','bank_transfer','online') NOT NULL DEFAULT 'cod',
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `order_number`, `customer_id`, `customer_name`, `customer_email`, `customer_phone`, `customer_phone_alt`, `city_id`, `area_id`, `shipping_address`, `landmark`, `delivery_time`, `delivery_date`, `shipping_company_id`, `coupon_id`, `coupon_code`, `discount_amount`, `subtotal`, `shipping_cost`, `tax_amount`, `total_amount`, `payment_method`, `status`, `notes`, `created_at`, `updated_at`) VALUES
(16, 'ORD-********-3120', NULL, '‪ahmed sobhy‬‏', '<EMAIL>', '**********', '*********', 1, 1, 'فيصل', '', '', '0000-00-00', NULL, NULL, NULL, '0.00', '300.00', '50.00', '45.00', '395.00', 'online', 'confirmed', '', '2025-07-25 13:58:47', '2025-07-25 13:59:37'),
(17, 'ORD-********-9553', NULL, 'صثضصثضص', '<EMAIL>', '**********', '**********', 1, 15, 'شسيسشي', '', '', '0000-00-00', NULL, NULL, NULL, '0.00', '300.00', '50.00', '0.00', '350.00', 'cod', 'pending', '', '2025-07-25 14:08:05', '2025-07-25 14:08:05'),
(18, 'ORD-********-2715', NULL, '‪ahmed sobhy‬‏', '<EMAIL>', '**********', '**********', 1, 1, 'فيصل', '', '', '0000-00-00', NULL, NULL, NULL, '0.00', '300.00', '50.00', '0.00', '350.00', 'bank_transfer', 'pending', '', '2025-07-25 14:16:30', '2025-07-25 14:16:30'),
(19, 'ORD-********-4469', NULL, 'سشيسشيشس', '<EMAIL>', '**********', '**********', 1, 15, 'سشيسشي', 'سشيشس', '', '0000-00-00', NULL, NULL, NULL, '0.00', '300.00', '50.00', '0.00', '350.00', 'bank_transfer', 'pending', '', '2025-07-25 14:30:46', '2025-07-25 14:30:46'),
(20, 'ORD-********-0917', NULL, 'ففففففففففففف', '<EMAIL>', '**********', '**********', 1, 8, 'شسيسشيشسي', '', '', '0000-00-00', 2, NULL, NULL, '0.00', '300.00', '100.00', '0.00', '400.00', 'bank_transfer', 'pending', '', '2025-07-25 14:52:06', '2025-07-25 14:52:06');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `total` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `title`, `color_id`, `size_id`, `quantity`, `price`, `total`) VALUES
(14, 16, 24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', NULL, 55, 1, '300.00', '300.00'),
(15, 17, 24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', NULL, 55, 1, '300.00', '300.00'),
(16, 18, 24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', NULL, 56, 1, '300.00', '300.00'),
(17, 19, 24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', NULL, 56, 1, '300.00', '300.00'),
(18, 20, 24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', NULL, 56, 1, '300.00', '300.00');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `seo_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `category_id` int(11) NOT NULL,
  `stock_status` enum('in_stock','out_of_stock') DEFAULT 'in_stock',
  `featured_image` varchar(255) DEFAULT NULL,
  `video_url` varchar(500) DEFAULT NULL,
  `video_file` varchar(255) DEFAULT NULL,
  `is_featured` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `views_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(20) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `title`, `slug`, `seo_title`, `seo_description`, `meta_keywords`, `description`, `price`, `sale_price`, `category_id`, `stock_status`, `featured_image`, `video_url`, `video_file`, `is_featured`, `is_active`, `views_count`, `created_at`, `updated_at`, `status`) VALUES
(24, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', 'sasds', NULL, NULL, NULL, 'فستان سهرة بسيطة باللون الأسود أفخم فساتين الرياض', '300.00', NULL, 1, 'in_stock', 'products/product_24_1753460059_0.jpg', '', NULL, 0, 1, 36, '2025-07-25 13:57:48', '2025-07-26 06:01:44', 'active'),
(25, 'منتج اختبار', 'test-product', NULL, NULL, NULL, 'هذا منتج للاختبار', '100.00', NULL, 10, 'in_stock', NULL, NULL, NULL, 0, 0, 1, '2025-07-25 15:59:02', '2025-07-26 06:00:49', 'active'),
(26, 'فستان طويل منفوش بالون الفوشي', 'fstan-twyl-mnfwsh-balwn-alfwshy', NULL, NULL, NULL, 'فستان طويل منفوش بالون الفوشي', '370.00', NULL, 1, 'in_stock', 'products/product_26_1753509968_0.webp', '', NULL, 0, 1, 2, '2025-07-26 06:06:08', '2025-07-26 06:13:48', 'active'),
(27, 'فستان بدون تطريز أكمام طويله', 'fstan-bdwn-ttryz-akmam-twylh', NULL, NULL, NULL, 'فستان بدون تطريز أكمام طويله', '340.00', NULL, 1, 'in_stock', 'products/product_27_1753510333_0.webp', '', NULL, 0, 1, 2, '2025-07-26 06:11:54', '2025-07-26 06:14:54', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `product_colors`
--

CREATE TABLE `product_colors` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_images`
--

CREATE TABLE `product_images` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `image_type` enum('gallery','lifestyle','main') DEFAULT 'gallery',
  `sort_order` int(11) DEFAULT 0,
  `alt_text` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_images`
--

INSERT INTO `product_images` (`id`, `product_id`, `image_path`, `image_type`, `sort_order`, `alt_text`, `created_at`) VALUES
(25, 24, 'products/product_24_1753460059_0.jpg', 'gallery', 1, 'صورة المنتج 1', '2025-07-25 16:14:19'),
(26, 24, 'products/product_24_1753509661_0.jpg', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-26 06:01:01'),
(29, 26, 'products/product_26_1753509968_0.webp', 'gallery', 1, 'صورة المنتج 1', '2025-07-26 06:06:08'),
(30, 26, 'products/product_26_1753509989_0.webp', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-26 06:06:29'),
(35, 27, 'products/product_27_1753510333_0.webp', 'gallery', 1, 'صورة المنتج 1', '2025-07-26 06:12:13'),
(37, 27, 'products/product_27_1753510355_0.webp', 'lifestyle', 1, 'صورة المنتج 1', '2025-07-26 06:12:35');

-- --------------------------------------------------------

--
-- Table structure for table `product_reviews`
--

CREATE TABLE `product_reviews` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_email` varchar(100) NOT NULL,
  `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `review_text` text DEFAULT NULL,
  `is_approved` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_sizes`
--

CREATE TABLE `product_sizes` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `size_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_sizes`
--

INSERT INTO `product_sizes` (`id`, `product_id`, `size_id`) VALUES
(621, 24, 51),
(620, 24, 52),
(619, 24, 53),
(622, 24, 54),
(623, 24, 55),
(624, 24, 56),
(657, 26, 51),
(656, 26, 52),
(655, 26, 53),
(658, 26, 54),
(659, 26, 55),
(689, 27, 52),
(688, 27, 53),
(690, 27, 54),
(691, 27, 55);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'site_logo', 'logo.png', '2025-07-24 23:14:18', '2025-07-25 14:57:00'),
(2, 'site_name', 'فساتين شوب', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(3, 'site_description', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(4, 'site_keywords', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(5, 'contact_email', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(6, 'contact_phone', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(7, 'facebook_url', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(8, 'instagram_url', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(9, 'twitter_url', '', '2025-07-24 23:14:36', '2025-07-25 14:58:32'),
(37, 'site_url', 'http://localhost/sss/', '2025-07-25 15:21:34', '2025-07-25 15:22:21');

-- --------------------------------------------------------

--
-- Table structure for table `shipping_companies`
--

CREATE TABLE `shipping_companies` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `cost` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shipping_companies`
--

INSERT INTO `shipping_companies` (`id`, `name`, `logo`, `cost`, `is_active`, `created_at`) VALUES
(1, 'سمسا', 'fast_shipping.png', '50.00', 1, '2025-07-24 02:51:39'),
(2, 'البريد السعودي', 'guaranteed_delivery.png', '100.00', 1, '2025-07-24 02:51:39');

-- --------------------------------------------------------

--
-- Table structure for table `site_settings`
--

CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','textarea','number','boolean','json') DEFAULT 'text',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'متجر فساتين السهرة', 'text', 'general', 'اسم الموقع', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(2, 'site_description', 'متجر متخصص في فساتين السهرة الأنيقة والراقية', 'textarea', 'general', 'وصف الموقع', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(3, 'site_keywords', 'فساتين سهرة, فساتين أنيقة, فساتين راقية', 'textarea', 'seo', 'الكلمات المفتاحية', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(4, 'contact_email', '<EMAIL>', 'text', 'contact', 'البريد الإلكتروني للتواصل', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(5, 'contact_phone', '+966500000000', 'text', 'contact', 'رقم الهاتف', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(6, 'free_shipping_threshold', '500', 'number', 'shipping', 'الحد الأدنى للشحن المجاني', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(7, 'tax_rate', '0.15', 'number', 'general', 'معدل الضريبة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(8, 'currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(9, 'items_per_page', '12', 'number', 'general', 'عدد العناصر في الصفحة', '2025-07-24 04:25:02', '2025-07-24 04:25:02'),
(10, 'maintenance_mode', '0', 'boolean', 'general', 'وضع الصيانة', '2025-07-24 04:25:02', '2025-07-24 04:25:02');

-- --------------------------------------------------------

--
-- Table structure for table `sizes`
--

CREATE TABLE `sizes` (
  `id` int(11) NOT NULL,
  `name` varchar(20) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sizes`
--

INSERT INTO `sizes` (`id`, `name`, `sort_order`, `is_active`, `created_at`) VALUES
(51, 'S - 1', 0, 1, '2025-07-25 13:55:50'),
(52, 'M -2', 0, 1, '2025-07-25 13:55:54'),
(53, 'L -3', 0, 1, '2025-07-25 13:55:59'),
(54, 'XL -4', 0, 1, '2025-07-25 13:56:06'),
(55, 'XXL - 5', 0, 1, '2025-07-25 13:56:14'),
(56, 'XXXL -6', 0, 1, '2025-07-25 13:56:23');

-- --------------------------------------------------------

--
-- Table structure for table `transfer_receipts`
--

CREATE TABLE `transfer_receipts` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `receipt_filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `transfer_receipts`
--

INSERT INTO `transfer_receipts` (`id`, `order_id`, `receipt_filename`, `original_filename`, `file_size`, `file_type`, `upload_date`) VALUES
(1, 20, 'receipt_2025-07-25_17-52-06_68839a1624b5a.jpg', '520853460_122227911806165996_5476072894810431362_n.jpg', 156404, 'image/jpeg', '2025-07-25 14:52:06');

-- --------------------------------------------------------

--
-- Table structure for table `wishlist`
--

CREATE TABLE `wishlist` (
  `id` int(11) NOT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `action` (`action`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `areas`
--
ALTER TABLE `areas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `city_id` (`city_id`);

--
-- Indexes for table `bank_settings`
--
ALTER TABLE `bank_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD KEY `color_id` (`color_id`),
  ADD KEY `size_id` (`size_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `cities`
--
ALTER TABLE `cities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `country_id` (`country_id`);

--
-- Indexes for table `colors`
--
ALTER TABLE `colors`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `coupons`
--
ALTER TABLE `coupons`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coupon_id` (`coupon_id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `area_id` (`area_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `city_id` (`city_id`),
  ADD KEY `area_id` (`area_id`),
  ADD KEY `shipping_company_id` (`shipping_company_id`),
  ADD KEY `coupon_id` (`coupon_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `color_id` (`color_id`),
  ADD KEY `size_id` (`size_id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `product_colors`
--
ALTER TABLE `product_colors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_color` (`product_id`,`color_id`),
  ADD KEY `color_id` (`color_id`);

--
-- Indexes for table `product_images`
--
ALTER TABLE `product_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_reviews`
--
ALTER TABLE `product_reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `is_approved` (`is_approved`);

--
-- Indexes for table `product_sizes`
--
ALTER TABLE `product_sizes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_size` (`product_id`,`size_id`),
  ADD KEY `size_id` (`size_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `shipping_companies`
--
ALTER TABLE `shipping_companies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `sizes`
--
ALTER TABLE `sizes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transfer_receipts`
--
ALTER TABLE `transfer_receipts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `product_id` (`product_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=136;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `areas`
--
ALTER TABLE `areas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `bank_settings`
--
ALTER TABLE `bank_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=94;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `cities`
--
ALTER TABLE `cities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `colors`
--
ALTER TABLE `colors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `coupons`
--
ALTER TABLE `coupons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `product_colors`
--
ALTER TABLE `product_colors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=491;

--
-- AUTO_INCREMENT for table `product_images`
--
ALTER TABLE `product_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `product_reviews`
--
ALTER TABLE `product_reviews`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_sizes`
--
ALTER TABLE `product_sizes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=692;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `shipping_companies`
--
ALTER TABLE `shipping_companies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `sizes`
--
ALTER TABLE `sizes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `transfer_receipts`
--
ALTER TABLE `transfer_receipts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `wishlist`
--
ALTER TABLE `wishlist`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `areas`
--
ALTER TABLE `areas`
  ADD CONSTRAINT `areas_ibfk_1` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `cart`
--
ALTER TABLE `cart`
  ADD CONSTRAINT `cart_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cart_ibfk_2` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `cart_ibfk_3` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `cities`
--
ALTER TABLE `cities`
  ADD CONSTRAINT `cities_ibfk_1` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD CONSTRAINT `coupon_usage_ibfk_1` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `coupon_usage_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD CONSTRAINT `customer_addresses_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_addresses_ibfk_2` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`area_id`) REFERENCES `areas` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `orders_ibfk_4` FOREIGN KEY (`shipping_company_id`) REFERENCES `shipping_companies` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_5` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_3` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `order_items_ibfk_4` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_colors`
--
ALTER TABLE `product_colors`
  ADD CONSTRAINT `product_colors_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_colors_ibfk_2` FOREIGN KEY (`color_id`) REFERENCES `colors` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_images`
--
ALTER TABLE `product_images`
  ADD CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_reviews`
--
ALTER TABLE `product_reviews`
  ADD CONSTRAINT `product_reviews_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_sizes`
--
ALTER TABLE `product_sizes`
  ADD CONSTRAINT `product_sizes_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_sizes_ibfk_2` FOREIGN KEY (`size_id`) REFERENCES `sizes` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD CONSTRAINT `wishlist_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wishlist_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
