<?php
session_start();
require_once 'config/database.php';
require_once 'includes/Cart.php';

echo "<h1>🎯 الاختبار النهائي - العملية الكاملة</h1>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // إنشاء session جديد للاختبار
    $test_session = 'final_test_' . uniqid();
    $_SESSION['cart_session_id'] = $test_session;
    
    echo "<h2>1. إضافة منتج للسلة مع لون ومقاس</h2>";
    
    // جلب منتج مع ألوان ومقاسات
    $product_query = "SELECT p.id, p.title, p.price 
                      FROM products p 
                      WHERE EXISTS (SELECT 1 FROM product_colors pc WHERE pc.product_id = p.id)
                      AND EXISTS (SELECT 1 FROM product_sizes ps WHERE ps.product_id = p.id)
                      LIMIT 1";
    
    $stmt = $conn->prepare($product_query);
    $stmt->execute();
    $product = $stmt->fetch();
    
    if (!$product) {
        echo "<p style='color: red;'>❌ لا توجد منتجات مع ألوان ومقاسات</p>";
        exit;
    }
    
    // جلب لون ومقاس
    $color_query = "SELECT c.id, c.name FROM colors c 
                    INNER JOIN product_colors pc ON c.id = pc.color_id 
                    WHERE pc.product_id = ? LIMIT 1";
    $stmt = $conn->prepare($color_query);
    $stmt->execute([$product['id']]);
    $color = $stmt->fetch();
    
    $size_query = "SELECT s.id, s.name FROM sizes s 
                   INNER JOIN product_sizes ps ON s.id = ps.size_id 
                   WHERE ps.product_id = ? LIMIT 1";
    $stmt = $conn->prepare($size_query);
    $stmt->execute([$product['id']]);
    $size = $stmt->fetch();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<p><strong>المنتج:</strong> " . htmlspecialchars($product['title']) . "</p>";
    echo "<p><strong>اللون:</strong> " . ($color ? $color['name'] : 'غير متاح') . "</p>";
    echo "<p><strong>المقاس:</strong> " . ($size ? $size['name'] : 'غير متاح') . "</p>";
    echo "</div>";
    
    // إضافة للسلة
    $cart = new Cart();
    $cart->product_id = $product['id'];
    $cart->color_id = $color ? $color['id'] : null;
    $cart->size_id = $size ? $size['id'] : null;
    $cart->quantity = 2;
    
    if ($cart->add_to_cart()) {
        echo "✅ تم إضافة المنتج للسلة<br>";
    } else {
        echo "❌ فشل في إضافة المنتج للسلة<br>";
        exit;
    }
    
    echo "<h2>2. محاكاة عملية الطلب الكاملة</h2>";
    
    // بيانات العميل
    $customer_data = [
        'customer_name' => 'اختبار نهائي',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '0501234567',
        'city_id' => 1,
        'area_id' => 1,
        'shipping_address' => 'عنوان الاختبار النهائي',
        'payment_method' => 'cash_on_delivery',
        'notes' => 'طلب اختبار نهائي للتأكد من عمل النظام'
    ];
    
    // محاكاة process_order.php
    echo "<h3>أ. التحقق من السلة</h3>";
    
    $cart_data = $cart->get_cart_for_order();
    $cart_items = $cart_data['items'];
    
    if (empty($cart_items)) {
        echo "<p style='color: red;'>❌ السلة فارغة</p>";
        exit;
    }
    
    echo "<p>✅ السلة تحتوي على " . count($cart_items) . " عنصر</p>";
    
    // التحقق من صحة العناصر
    foreach ($cart_items as $item) {
        if (!isset($item['product_id']) || !isset($item['title']) || !isset($item['price']) || !isset($item['quantity'])) {
            echo "<p style='color: red;'>❌ عنصر غير صحيح في السلة</p>";
            exit;
        }
    }
    
    echo "<p>✅ جميع عناصر السلة صحيحة</p>";
    
    echo "<h3>ب. حساب المجاميع</h3>";
    
    $subtotal = 0;
    foreach ($cart_items as $item) {
        $subtotal += $item['total'];
    }
    
    $shipping_cost = $subtotal >= 500 ? 0 : 50;
    $tax_rate = 0.15;
    $tax_amount = $subtotal * $tax_rate;
    $total_amount = $subtotal + $shipping_cost + $tax_amount;
    
    echo "<p><strong>المجموع الفرعي:</strong> " . number_format($subtotal, 2) . " ر.س</p>";
    echo "<p><strong>الشحن:</strong> " . number_format($shipping_cost, 2) . " ر.س</p>";
    echo "<p><strong>الضريبة:</strong> " . number_format($tax_amount, 2) . " ر.س</p>";
    echo "<p><strong>المجموع الكلي:</strong> " . number_format($total_amount, 2) . " ر.س</p>";
    
    echo "<h3>ج. إنشاء الطلب</h3>";
    
    $order_number = 'FINAL-' . date('Ymd') . '-' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
    
    $conn->beginTransaction();
    
    try {
        // إدراج الطلب
        $order_query = "INSERT INTO orders (
            order_number, customer_name, customer_email, customer_phone,
            city_id, area_id, shipping_address,
            subtotal, shipping_cost, tax_amount, total_amount, payment_method,
            status, notes, created_at
        ) VALUES (
            :order_number, :customer_name, :customer_email, :customer_phone,
            :city_id, :area_id, :shipping_address,
            :subtotal, :shipping_cost, :tax_amount, :total_amount, :payment_method,
            'pending', :notes, NOW()
        )";
        
        $order_stmt = $conn->prepare($order_query);
        $order_stmt->bindParam(':order_number', $order_number);
        $order_stmt->bindParam(':customer_name', $customer_data['customer_name']);
        $order_stmt->bindParam(':customer_email', $customer_data['customer_email']);
        $order_stmt->bindParam(':customer_phone', $customer_data['customer_phone']);
        $order_stmt->bindParam(':city_id', $customer_data['city_id']);
        $order_stmt->bindParam(':area_id', $customer_data['area_id']);
        $order_stmt->bindParam(':shipping_address', $customer_data['shipping_address']);
        $order_stmt->bindParam(':subtotal', $subtotal);
        $order_stmt->bindParam(':shipping_cost', $shipping_cost);
        $order_stmt->bindParam(':tax_amount', $tax_amount);
        $order_stmt->bindParam(':total_amount', $total_amount);
        $order_stmt->bindParam(':payment_method', $customer_data['payment_method']);
        $order_stmt->bindParam(':notes', $customer_data['notes']);
        
        if (!$order_stmt->execute()) {
            throw new Exception('فشل في إنشاء الطلب');
        }
        
        $order_id = $conn->lastInsertId();
        echo "<p>✅ تم إنشاء الطلب: $order_number (ID: $order_id)</p>";
        
        // إدراج عناصر الطلب
        $order_item_query = "INSERT INTO order_items (
            order_id, product_id, title, price, quantity, color_id, size_id, total
        ) VALUES (
            :order_id, :product_id, :title, :price, :quantity, :color_id, :size_id, :total
        )";
        
        $order_item_stmt = $conn->prepare($order_item_query);
        
        foreach ($cart_items as $item) {
            $item_price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
            $item_total = $item_price * $item['quantity'];

            $order_item_stmt->bindParam(':order_id', $order_id);
            $order_item_stmt->bindParam(':product_id', $item['product_id']);
            $order_item_stmt->bindParam(':title', $item['title']);
            $order_item_stmt->bindParam(':price', $item_price);
            $order_item_stmt->bindParam(':quantity', $item['quantity']);
            $order_item_stmt->bindParam(':color_id', $item['color_id']);
            $order_item_stmt->bindParam(':size_id', $item['size_id']);
            $order_item_stmt->bindParam(':total', $item_total);

            if (!$order_item_stmt->execute()) {
                $error_info = $order_item_stmt->errorInfo();
                throw new Exception('فشل في إضافة عناصر الطلب: ' . $error_info[2]);
            }
            
            echo "<p>✅ تم إضافة عنصر: " . $item['title'];
            if ($item['color_name']) echo " - " . $item['color_name'];
            if ($item['size_name']) echo " - " . $item['size_name'];
            echo " (الكمية: " . $item['quantity'] . ")</p>";
        }
        
        $conn->commit();
        
        echo "<h2>3. التحقق من النتيجة النهائية</h2>";
        
        // فحص الطلب في لوحة التحكم
        $admin_check = "SELECT o.order_number, o.customer_name, o.total_amount,
                              COUNT(oi.id) as items_count,
                              GROUP_CONCAT(
                                  CONCAT(oi.title, 
                                         CASE WHEN col.name IS NOT NULL THEN CONCAT(' - ', col.name) ELSE '' END,
                                         CASE WHEN s.name IS NOT NULL THEN CONCAT(' - ', s.name) ELSE '' END,
                                         ' (الكمية: ', oi.quantity, ')')
                                  SEPARATOR ' | '
                              ) as items_details
                       FROM orders o
                       LEFT JOIN order_items oi ON o.id = oi.order_id
                       LEFT JOIN colors col ON oi.color_id = col.id
                       LEFT JOIN sizes s ON oi.size_id = s.id
                       WHERE o.id = ?
                       GROUP BY o.id";
        
        $stmt = $conn->prepare($admin_check);
        $stmt->execute([$order_id]);
        $result = $stmt->fetch();
        
        if ($result && $result['items_count'] > 0) {
            echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 3px solid #28a745;'>";
            echo "<h3>🎉 نجح الاختبار النهائي بالكامل!</h3>";
            echo "<p><strong>رقم الطلب:</strong> " . $result['order_number'] . "</p>";
            echo "<p><strong>العميل:</strong> " . $result['customer_name'] . "</p>";
            echo "<p><strong>المبلغ:</strong> " . number_format($result['total_amount'], 2) . " ر.س</p>";
            echo "<p><strong>عدد العناصر:</strong> " . $result['items_count'] . "</p>";
            echo "<p><strong>تفاصيل المنتجات:</strong> " . htmlspecialchars($result['items_details']) . "</p>";
            echo "<p style='color: #28a745; font-weight: bold; font-size: 20px; text-align: center;'>✅ المشكلة تم حلها نهائياً!</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
            echo "<h3>❌ لا تزال هناك مشكلة</h3>";
            echo "</div>";
        }
        
        // مسح السلة
        $cart->clear_cart();
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h2>🔗 الروابط</h2>";
echo "<p><a href='admin/orders.php' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin: 10px;'>📋 لوحة التحكم</a></p>";
echo "<p><a href='products.php' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin: 10px;'>🛍️ تصفح المنتجات</a></p>";
echo "<p><a href='cart.php' target='_blank' style='background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin: 10px;'>🛒 السلة</a></p>";
?>
