<?php
/**
 * إنشاء ملف sitemap.xml للموقع
 */

require_once 'config/database.php';
require_once 'includes/settings.php';

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$conn = $database->getConnection();

// بداية ملف XML
$sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
$sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// الصفحة الرئيسية
$sitemap .= '  <url>' . "\n";
$sitemap .= '    <loc>' . SITE_URL . '</loc>' . "\n";
$sitemap .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
$sitemap .= '    <changefreq>daily</changefreq>' . "\n";
$sitemap .= '    <priority>1.0</priority>' . "\n";
$sitemap .= '  </url>' . "\n";

// صفحة المنتجات
$sitemap .= '  <url>' . "\n";
$sitemap .= '    <loc>' . SITE_URL . '/products.php</loc>' . "\n";
$sitemap .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
$sitemap .= '    <changefreq>daily</changefreq>' . "\n";
$sitemap .= '    <priority>0.9</priority>' . "\n";
$sitemap .= '  </url>' . "\n";

// صفحة السلة
$sitemap .= '  <url>' . "\n";
$sitemap .= '    <loc>' . SITE_URL . '/cart.php</loc>' . "\n";
$sitemap .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
$sitemap .= '    <changefreq>weekly</changefreq>' . "\n";
$sitemap .= '    <priority>0.7</priority>' . "\n";
$sitemap .= '  </url>' . "\n";

// صفحة الدفع
$sitemap .= '  <url>' . "\n";
$sitemap .= '    <loc>' . SITE_URL . '/checkout.php</loc>' . "\n";
$sitemap .= '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
$sitemap .= '    <changefreq>weekly</changefreq>' . "\n";
$sitemap .= '    <priority>0.6</priority>' . "\n";
$sitemap .= '  </url>' . "\n";

try {
    // إضافة الأقسام
    $categories_query = "SELECT slug, updated_at FROM categories WHERE is_active = 1 ORDER BY name";
    $categories_stmt = $conn->prepare($categories_query);
    $categories_stmt->execute();
    $categories = $categories_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($categories as $category) {
        $sitemap .= '  <url>' . "\n";
        $sitemap .= '    <loc>' . getCategoryUrl($category['slug']) . '</loc>' . "\n";
        $lastmod = $category['updated_at'] ? date('Y-m-d', strtotime($category['updated_at'])) : date('Y-m-d');
        $sitemap .= '    <lastmod>' . $lastmod . '</lastmod>' . "\n";
        $sitemap .= '    <changefreq>weekly</changefreq>' . "\n";
        $sitemap .= '    <priority>0.8</priority>' . "\n";
        $sitemap .= '  </url>' . "\n";
    }
    
    // إضافة المنتجات
    $products_query = "SELECT slug, updated_at FROM products WHERE is_active = 1 ORDER BY created_at DESC";
    $products_stmt = $conn->prepare($products_query);
    $products_stmt->execute();
    $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($products as $product) {
        $sitemap .= '  <url>' . "\n";
        $sitemap .= '    <loc>' . getProductUrl($product['slug']) . '</loc>' . "\n";
        $lastmod = $product['updated_at'] ? date('Y-m-d', strtotime($product['updated_at'])) : date('Y-m-d');
        $sitemap .= '    <lastmod>' . $lastmod . '</lastmod>' . "\n";
        $sitemap .= '    <changefreq>weekly</changefreq>' . "\n";
        $sitemap .= '    <priority>0.7</priority>' . "\n";
        $sitemap .= '  </url>' . "\n";
    }
    
} catch (Exception $e) {
    echo "خطأ في إنشاء sitemap: " . $e->getMessage();
    exit();
}

// نهاية ملف XML
$sitemap .= '</urlset>';

// حفظ الملف
file_put_contents('sitemap.xml', $sitemap);

// عرض رسالة نجاح
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء Sitemap</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8fafc; }";
echo ".container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center; }";
echo "h1 { color: #38a169; margin-bottom: 20px; }";
echo ".success { color: #38a169; font-size: 18px; margin: 20px 0; }";
echo ".info { color: #3182ce; margin: 15px 0; }";
echo ".btn { display: inline-block; padding: 12px 24px; background: #4299e1; color: white; text-decoration: none; border-radius: 8px; margin: 10px; }";
echo ".btn:hover { background: #3182ce; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>✅ تم إنشاء Sitemap بنجاح</h1>";
echo "<p class='success'>تم إنشاء ملف sitemap.xml بنجاح</p>";
echo "<p class='info'>تم إضافة " . count($categories) . " قسم و " . count($products) . " منتج</p>";
echo "<p class='info'>يمكنك الآن إرسال الـ sitemap لمحركات البحث</p>";

echo "<a href='sitemap.xml' class='btn' target='_blank'>عرض Sitemap</a>";
echo "<a href='test_url_rewrite.php' class='btn'>اختبار الروابط</a>";
echo "<a href='index.php' class='btn'>العودة للموقع</a>";

echo "<div style='margin-top: 30px; padding: 20px; background: #f7fafc; border-radius: 10px; text-align: right;'>";
echo "<h3 style='color: #2d3748; margin-bottom: 15px;'>📋 خطوات إضافية:</h3>";
echo "<ul style='color: #4a5568; line-height: 1.8;'>";
echo "<li>أضف الـ sitemap لـ Google Search Console</li>";
echo "<li>أضف الـ sitemap لـ Bing Webmaster Tools</li>";
echo "<li>تأكد من تحديث الـ sitemap دورياً</li>";
echo "<li>أضف رابط الـ sitemap في ملف robots.txt</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
