<?php
require_once __DIR__ . '/../config/database.php';

class Cart {
    private $conn;
    private $table_name = "cart";

    public $id;
    public $session_id;
    public $product_id;
    public $color_id;
    public $size_id;
    public $quantity;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        
        // إنشاء session_id إذا لم يكن موجوداً
        if (!isset($_SESSION['cart_session_id'])) {
            $_SESSION['cart_session_id'] = session_id();
        }
        $this->session_id = $_SESSION['cart_session_id'];
    }

    // إضافة منتج إلى السلة
    public function add_to_cart() {
        // التحقق من وجود المنتج في السلة
        $existing_item = $this->get_cart_item($this->product_id, $this->color_id, $this->size_id);
        
        if ($existing_item) {
            // تحديث الكمية
            $this->id = $existing_item['id'];
            $this->quantity = $existing_item['quantity'] + $this->quantity;
            return $this->update_quantity();
        } else {
            // إضافة منتج جديد
            $query = "INSERT INTO " . $this->table_name . "
                      SET session_id=:session_id, product_id=:product_id,
                          color_id=:color_id, size_id=:size_id, quantity=:quantity";

            $stmt = $this->conn->prepare($query);

            // تنظيف البيانات
            $this->session_id = htmlspecialchars(strip_tags($this->session_id));
            $this->product_id = htmlspecialchars(strip_tags($this->product_id));
            $this->quantity = htmlspecialchars(strip_tags($this->quantity));

            // معالجة color_id و size_id - تحويل القيم الفارغة إلى NULL
            $color_id_value = ($this->color_id && $this->color_id > 0) ? $this->color_id : null;
            $size_id_value = ($this->size_id && $this->size_id > 0) ? $this->size_id : null;

            // ربط القيم
            $stmt->bindParam(":session_id", $this->session_id);
            $stmt->bindParam(":product_id", $this->product_id);
            $stmt->bindParam(":color_id", $color_id_value, PDO::PARAM_INT);
            $stmt->bindParam(":size_id", $size_id_value, PDO::PARAM_INT);
            $stmt->bindParam(":quantity", $this->quantity);

            if($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }

            return false;
        }
    }

    // الحصول على عنصر من السلة
    private function get_cart_item($product_id, $color_id, $size_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE session_id = :session_id AND product_id = :product_id 
                  AND color_id = :color_id AND size_id = :size_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);
        $stmt->bindParam(":product_id", $product_id);
        $stmt->bindParam(":color_id", $color_id);
        $stmt->bindParam(":size_id", $size_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // الحصول على محتويات السلة
    public function get_cart_items() {
        $query = "SELECT c.*, p.title, p.price, p.sale_price, p.featured_image,
                         col.name as color_name, col.hex_code,
                         s.name as size_name
                  FROM " . $this->table_name . " c
                  LEFT JOIN products p ON c.product_id = p.id
                  LEFT JOIN colors col ON c.color_id = col.id
                  LEFT JOIN sizes s ON c.size_id = s.id
                  WHERE c.session_id = :session_id
                  ORDER BY c.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);
        $stmt->execute();

        return $stmt;
    }

    // تحديث كمية المنتج في السلة
    public function update_quantity() {
        $query = "UPDATE " . $this->table_name . "
                  SET quantity = :quantity
                  WHERE id = :id AND session_id = :session_id";

        $stmt = $this->conn->prepare($query);

        $this->quantity = htmlspecialchars(strip_tags($this->quantity));
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":quantity", $this->quantity);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":session_id", $this->session_id);

        return $stmt->execute();
    }

    // تحديث مقاس المنتج في السلة
    public function update_size() {
        $query = "UPDATE " . $this->table_name . "
                  SET size_id = :size_id
                  WHERE id = :id AND session_id = :session_id";

        $stmt = $this->conn->prepare($query);

        $this->size_id = htmlspecialchars(strip_tags($this->size_id));
        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":size_id", $this->size_id);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":session_id", $this->session_id);

        return $stmt->execute();
    }

    // حذف منتج من السلة
    public function remove_from_cart() {
        $query = "DELETE FROM " . $this->table_name . " 
                  WHERE id = :id AND session_id = :session_id";

        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":session_id", $this->session_id);

        return $stmt->execute();
    }

    // إفراغ السلة
    public function clear_cart() {
        $query = "DELETE FROM " . $this->table_name . " WHERE session_id = :session_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);

        return $stmt->execute();
    }

    // عدد العناصر في السلة
    public function get_cart_count() {
        $query = "SELECT SUM(quantity) as total_items FROM " . $this->table_name . " 
                  WHERE session_id = :session_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total_items'] ? $row['total_items'] : 0;
    }

    // إجمالي السلة
    public function get_cart_total() {
        $query = "SELECT SUM(
                    c.quantity * 
                    CASE 
                        WHEN p.sale_price IS NOT NULL AND p.sale_price > 0 
                        THEN p.sale_price 
                        ELSE p.price 
                    END
                  ) as total
                  FROM " . $this->table_name . " c
                  LEFT JOIN products p ON c.product_id = p.id
                  WHERE c.session_id = :session_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] ? $row['total'] : 0;
    }

    // التحقق من وجود منتج في السلة
    public function is_in_cart($product_id, $color_id = null, $size_id = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE session_id = :session_id AND product_id = :product_id";

        if ($color_id) {
            $query .= " AND color_id = :color_id";
        }

        if ($size_id) {
            $query .= " AND size_id = :size_id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":session_id", $this->session_id);
        $stmt->bindParam(":product_id", $product_id);

        if ($color_id) {
            $stmt->bindParam(":color_id", $color_id);
        }

        if ($size_id) {
            $stmt->bindParam(":size_id", $size_id);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['count'] > 0;
    }

    // تنظيف السلة من المنتجات القديمة (أكثر من 30 يوم)
    public function cleanup_old_cart_items() {
        $query = "DELETE FROM " . $this->table_name . " 
                  WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute();
    }

    // نقل السلة من session إلى customer
    public function transfer_cart_to_customer($customer_id) {
        // هذه الدالة يمكن استخدامها عند تسجيل دخول العميل
        // لنقل محتويات السلة من الجلسة إلى حساب العميل
        
        $query = "UPDATE " . $this->table_name . " 
                  SET session_id = :customer_session 
                  WHERE session_id = :current_session";

        $stmt = $this->conn->prepare($query);
        
        $customer_session = 'customer_' . $customer_id;
        
        $stmt->bindParam(":customer_session", $customer_session);
        $stmt->bindParam(":current_session", $this->session_id);

        if ($stmt->execute()) {
            $_SESSION['cart_session_id'] = $customer_session;
            $this->session_id = $customer_session;
            return true;
        }

        return false;
    }

    // الحصول على تفاصيل السلة للطلب
    public function get_cart_for_order() {
        $items = $this->get_cart_items();
        $cart_data = array();
        $total = 0;

        while ($row = $items->fetch(PDO::FETCH_ASSOC)) {
            $item_price = $row['sale_price'] && $row['sale_price'] > 0 ? $row['sale_price'] : $row['price'];
            $item_total = $item_price * $row['quantity'];
            $total += $item_total;

            $cart_data[] = array(
                'id' => $row['id'],
                'product_id' => $row['product_id'],
                'title' => $row['title'],
                'color_id' => $row['color_id'],
                'color_name' => $row['color_name'],
                'size_id' => $row['size_id'],
                'size_name' => $row['size_name'],
                'quantity' => $row['quantity'],
                'price' => $item_price,
                'total' => $item_total,
                'image' => $row['featured_image']
            );
        }

        return array(
            'items' => $cart_data,
            'total' => $total,
            'count' => count($cart_data)
        );
    }
}
?>
