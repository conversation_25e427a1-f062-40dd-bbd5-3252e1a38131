<?php
require_once __DIR__ . '/../config/database.php';

class Category {
    private $conn;
    private $table_name = "categories";

    public $id;
    public $name;
    public $slug;
    public $seo_title;
    public $description;
    public $seo_content;
    public $image;
    public $parent_id;
    public $sort_order;
    public $is_active;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // إنشاء قسم جديد
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name=:name, slug=:slug, seo_title=:seo_title, 
                      description=:description, seo_content=:seo_content, 
                      image=:image, parent_id=:parent_id, sort_order=:sort_order, 
                      is_active=:is_active";

        $stmt = $this->conn->prepare($query);

        // تنظيف البيانات
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->slug = htmlspecialchars(strip_tags($this->slug));
        $this->seo_title = htmlspecialchars(strip_tags($this->seo_title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->seo_content = htmlspecialchars(strip_tags($this->seo_content));
        $this->image = htmlspecialchars(strip_tags($this->image));
        $this->parent_id = htmlspecialchars(strip_tags($this->parent_id));
        $this->sort_order = htmlspecialchars(strip_tags($this->sort_order));

        // ربط القيم
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":seo_title", $this->seo_title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":seo_content", $this->seo_content);
        $stmt->bindParam(":image", $this->image);
        $stmt->bindParam(":parent_id", $this->parent_id);
        $stmt->bindParam(":sort_order", $this->sort_order);
        $stmt->bindParam(":is_active", $this->is_active);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // قراءة جميع الأقسام
    public function read($parent_id = null) {
        $query = "SELECT c.*, parent.name as parent_name 
                  FROM " . $this->table_name . " c
                  LEFT JOIN " . $this->table_name . " parent ON c.parent_id = parent.id
                  WHERE c.is_active = 1";

        if($parent_id !== null) {
            if($parent_id == 0) {
                $query .= " AND c.parent_id IS NULL";
            } else {
                $query .= " AND c.parent_id = :parent_id";
            }
        }

        $query .= " ORDER BY c.sort_order ASC, c.name ASC";

        $stmt = $this->conn->prepare($query);

        if($parent_id !== null && $parent_id != 0) {
            $stmt->bindParam(":parent_id", $parent_id);
        }

        $stmt->execute();

        return $stmt;
    }

    // قراءة قسم واحد
    public function read_single() {
        $query = "SELECT c.*, parent.name as parent_name 
                  FROM " . $this->table_name . " c
                  LEFT JOIN " . $this->table_name . " parent ON c.parent_id = parent.id
                  WHERE c.id = :id AND c.is_active = 1
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->name = $row['name'];
            $this->slug = $row['slug'];
            $this->seo_title = $row['seo_title'];
            $this->description = $row['description'];
            $this->seo_content = $row['seo_content'];
            $this->image = $row['image'];
            $this->parent_id = $row['parent_id'];
            $this->sort_order = $row['sort_order'];
            $this->is_active = $row['is_active'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }

        return false;
    }

    // قراءة قسم بواسطة slug
    public function read_by_slug($slug) {
        $query = "SELECT c.*, parent.name as parent_name 
                  FROM " . $this->table_name . " c
                  LEFT JOIN " . $this->table_name . " parent ON c.parent_id = parent.id
                  WHERE c.slug = :slug AND c.is_active = 1
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":slug", $slug);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->id = $row['id'];
            $this->name = $row['name'];
            $this->slug = $row['slug'];
            $this->seo_title = $row['seo_title'];
            $this->description = $row['description'];
            $this->seo_content = $row['seo_content'];
            $this->image = $row['image'];
            $this->parent_id = $row['parent_id'];
            $this->sort_order = $row['sort_order'];
            $this->is_active = $row['is_active'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }

        return false;
    }

    // تحديث قسم
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name, slug = :slug, seo_title = :seo_title,
                      description = :description, seo_content = :seo_content,
                      image = :image, parent_id = :parent_id, sort_order = :sort_order,
                      is_active = :is_active
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // تنظيف البيانات
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->slug = htmlspecialchars(strip_tags($this->slug));
        $this->seo_title = htmlspecialchars(strip_tags($this->seo_title));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->seo_content = htmlspecialchars(strip_tags($this->seo_content));
        $this->image = htmlspecialchars(strip_tags($this->image));
        $this->parent_id = htmlspecialchars(strip_tags($this->parent_id));
        $this->sort_order = htmlspecialchars(strip_tags($this->sort_order));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // ربط القيم
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":seo_title", $this->seo_title);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":seo_content", $this->seo_content);
        $stmt->bindParam(":image", $this->image);
        $stmt->bindParam(":parent_id", $this->parent_id);
        $stmt->bindParam(":sort_order", $this->sort_order);
        $stmt->bindParam(":is_active", $this->is_active);
        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // حذف قسم
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // الحصول على الأقسام الرئيسية
    public function get_main_categories() {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE parent_id IS NULL AND is_active = 1 
                  ORDER BY sort_order ASC, name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt;
    }

    // الحصول على الأقسام الفرعية
    public function get_subcategories($parent_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE parent_id = :parent_id AND is_active = 1 
                  ORDER BY sort_order ASC, name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":parent_id", $parent_id);
        $stmt->execute();

        return $stmt;
    }

    // التحقق من وجود أقسام فرعية
    public function has_subcategories($category_id) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE parent_id = :category_id AND is_active = 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":category_id", $category_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }

    // عدد المنتجات في القسم
    public function get_products_count($category_id) {
        $query = "SELECT COUNT(*) as count FROM products 
                  WHERE category_id = :category_id AND is_active = 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":category_id", $category_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'];
    }

    // الحصول على مسار القسم (breadcrumb)
    public function get_category_path($category_id) {
        $path = array();
        $current_id = $category_id;

        while($current_id) {
            $query = "SELECT id, name, slug, parent_id FROM " . $this->table_name . " 
                      WHERE id = :id AND is_active = 1";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":id", $current_id);
            $stmt->execute();
            
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if($row) {
                array_unshift($path, $row);
                $current_id = $row['parent_id'];
            } else {
                break;
            }
        }

        return $path;
    }

    // عدد الأقسام
    public function count($parent_id = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE is_active = 1";

        if($parent_id !== null) {
            if($parent_id == 0) {
                $query .= " AND parent_id IS NULL";
            } else {
                $query .= " AND parent_id = :parent_id";
            }
        }

        $stmt = $this->conn->prepare($query);

        if($parent_id !== null && $parent_id != 0) {
            $stmt->bindParam(":parent_id", $parent_id);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'];
    }
}
?>
