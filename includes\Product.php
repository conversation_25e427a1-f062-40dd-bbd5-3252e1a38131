<?php
require_once __DIR__ . '/../config/database.php';

class Product {
    private $conn;
    private $table_name = "products";

    public $id;
    public $title;
    public $slug;
    public $description;
    public $price;
    public $sale_price;
    public $category_id;
    public $stock_status;
    public $featured_image;
    public $video_url;
    public $is_featured;
    public $is_active;
    public $views_count;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // إنشاء منتج جديد
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET title=:title, slug=:slug, description=:description, 
                      price=:price, sale_price=:sale_price, category_id=:category_id,
                      stock_status=:stock_status, featured_image=:featured_image,
                      video_url=:video_url, is_featured=:is_featured, is_active=:is_active";

        $stmt = $this->conn->prepare($query);

        // تنظيف البيانات
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->slug = htmlspecialchars(strip_tags($this->slug));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->sale_price = htmlspecialchars(strip_tags($this->sale_price));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->stock_status = htmlspecialchars(strip_tags($this->stock_status));
        $this->featured_image = htmlspecialchars(strip_tags($this->featured_image));
        $this->video_url = htmlspecialchars(strip_tags($this->video_url));

        // ربط القيم
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":sale_price", $this->sale_price);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":stock_status", $this->stock_status);
        $stmt->bindParam(":featured_image", $this->featured_image);
        $stmt->bindParam(":video_url", $this->video_url);
        $stmt->bindParam(":is_featured", $this->is_featured);
        $stmt->bindParam(":is_active", $this->is_active);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // قراءة جميع المنتجات
    public function read($limit = 20, $offset = 0, $category_id = null, $featured = null) {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE p.is_active = 1";

        if($category_id) {
            $query .= " AND p.category_id = :category_id";
        }

        if($featured !== null) {
            $query .= " AND p.is_featured = :featured";
        }

        $query .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        if($category_id) {
            $stmt->bindParam(":category_id", $category_id);
        }

        if($featured !== null) {
            $stmt->bindParam(":featured", $featured);
        }

        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);

        $stmt->execute();

        return $stmt;
    }

    // قراءة منتج واحد
    public function read_single() {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE p.id = :id AND p.is_active = 1
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->title = $row['title'];
            $this->slug = $row['slug'];
            $this->description = $row['description'];
            $this->price = $row['price'];
            $this->sale_price = $row['sale_price'];
            $this->category_id = $row['category_id'];
            $this->stock_status = $row['stock_status'];
            $this->featured_image = $row['featured_image'];
            $this->video_url = $row['video_url'];
            $this->is_featured = $row['is_featured'];
            $this->is_active = $row['is_active'];
            $this->views_count = $row['views_count'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }

        return false;
    }

    // قراءة منتج بواسطة slug
    public function read_by_slug($slug) {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE p.slug = :slug AND p.is_active = 1
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":slug", $slug);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->id = $row['id'];
            $this->title = $row['title'];
            $this->slug = $row['slug'];
            $this->description = $row['description'];
            $this->price = $row['price'];
            $this->sale_price = $row['sale_price'];
            $this->category_id = $row['category_id'];
            $this->stock_status = $row['stock_status'];
            $this->featured_image = $row['featured_image'];
            $this->video_url = $row['video_url'];
            $this->is_featured = $row['is_featured'];
            $this->is_active = $row['is_active'];
            $this->views_count = $row['views_count'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            
            return true;
        }

        return false;
    }

    // تحديث منتج
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET title = :title, slug = :slug, description = :description,
                      price = :price, sale_price = :sale_price, category_id = :category_id,
                      stock_status = :stock_status, featured_image = :featured_image,
                      video_url = :video_url, is_featured = :is_featured, is_active = :is_active
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // تنظيف البيانات
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->slug = htmlspecialchars(strip_tags($this->slug));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->sale_price = htmlspecialchars(strip_tags($this->sale_price));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->stock_status = htmlspecialchars(strip_tags($this->stock_status));
        $this->featured_image = htmlspecialchars(strip_tags($this->featured_image));
        $this->video_url = htmlspecialchars(strip_tags($this->video_url));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // ربط القيم
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":sale_price", $this->sale_price);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":stock_status", $this->stock_status);
        $stmt->bindParam(":featured_image", $this->featured_image);
        $stmt->bindParam(":video_url", $this->video_url);
        $stmt->bindParam(":is_featured", $this->is_featured);
        $stmt->bindParam(":is_active", $this->is_active);
        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // حذف منتج
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));

        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // زيادة عدد المشاهدات
    public function increment_views() {
        $query = "UPDATE " . $this->table_name . " SET views_count = views_count + 1 WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        
        return $stmt->execute();
    }

    // البحث في المنتجات
    public function search($keywords, $limit = 20, $offset = 0) {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE p.is_active = 1 AND (p.title LIKE :keywords OR p.description LIKE :keywords)
                  ORDER BY p.created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        $keywords = "%{$keywords}%";
        $stmt->bindParam(":keywords", $keywords);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);

        $stmt->execute();

        return $stmt;
    }

    // عدد المنتجات
    public function count($category_id = null, $featured = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE is_active = 1";

        if($category_id) {
            $query .= " AND category_id = :category_id";
        }

        if($featured !== null) {
            $query .= " AND is_featured = :featured";
        }

        $stmt = $this->conn->prepare($query);

        if($category_id) {
            $stmt->bindParam(":category_id", $category_id);
        }

        if($featured !== null) {
            $stmt->bindParam(":featured", $featured);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        return $row['total'];
    }
}
?>
