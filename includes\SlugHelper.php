<?php
/**
 * مساعد إنشاء الروابط الصديقة للمحركات
 * يدعم النصوص العربية والإنجليزية
 */

class SlugHelper {
    
    /**
     * تحويل النص العربي والإنجليزي إلى رابط صديق
     */
    public static function createSlug($text) {
        // إزالة المسافات الزائدة
        $text = trim($text);
        
        // تحويل الأحرف العربية إلى نسخ مبسطة
        $arabic_map = [
            'ا' => 'a', 'أ' => 'a', 'إ' => 'a', 'آ' => 'a',
            'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
            'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'th',
            'ر' => 'r', 'ز' => 'z', 'س' => 's', 'ش' => 'sh',
            'ص' => 's', 'ض' => 'd', 'ط' => 't', 'ظ' => 'z',
            'ع' => 'a', 'غ' => 'gh', 'ف' => 'f', 'ق' => 'q',
            'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
            'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ى' => 'a',
            'ة' => 'h', 'ء' => 'a'
        ];
        
        // تحويل الأحرف العربية
        $text = strtr($text, $arabic_map);
        
        // تحويل إلى أحرف صغيرة
        $text = strtolower($text);
        
        // إزالة الأحرف الخاصة والاحتفاظ بالأحرف والأرقام والشرطات
        $text = preg_replace('/[^a-z0-9\s\-]/', '', $text);
        
        // تحويل المسافات إلى شرطات
        $text = preg_replace('/\s+/', '-', $text);
        
        // إزالة الشرطات المتعددة
        $text = preg_replace('/-+/', '-', $text);
        
        // إزالة الشرطات من البداية والنهاية
        $text = trim($text, '-');
        
        // إذا كان النتيجة فارغة، استخدم timestamp
        if (empty($text)) {
            $text = 'product-' . time();
        }
        
        return $text;
    }
    
    /**
     * التحقق من تفرد الرابط في قاعدة البيانات
     */
    public static function ensureUniqueSlug($slug, $table, $column, $excludeId = null) {
        require_once __DIR__ . '/../config/database.php';
        
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            $originalSlug = $slug;
            $counter = 1;
            
            while (true) {
                // التحقق من وجود الرابط
                $query = "SELECT id FROM $table WHERE $column = ?";
                $params = [$slug];
                
                if ($excludeId) {
                    $query .= " AND id != ?";
                    $params[] = $excludeId;
                }
                
                $stmt = $conn->prepare($query);
                $stmt->execute($params);
                
                if ($stmt->rowCount() == 0) {
                    // الرابط فريد
                    return $slug;
                }
                
                // إضافة رقم للرابط
                $slug = $originalSlug . '-' . $counter;
                $counter++;
                
                // منع الحلقة اللانهائية
                if ($counter > 100) {
                    $slug = $originalSlug . '-' . time();
                    break;
                }
            }
            
            return $slug;
            
        } catch (Exception $e) {
            // في حالة الخطأ، إرجاع الرابط مع timestamp
            return $slug . '-' . time();
        }
    }
    
    /**
     * إنشاء رابط فريد للمنتج
     */
    public static function createProductSlug($title, $excludeId = null) {
        $slug = self::createSlug($title);
        return self::ensureUniqueSlug($slug, 'products', 'slug', $excludeId);
    }
    
    /**
     * إنشاء رابط فريد للتصنيف
     */
    public static function createCategorySlug($name, $excludeId = null) {
        $slug = self::createSlug($name);
        return self::ensureUniqueSlug($slug, 'categories', 'slug', $excludeId);
    }
    
    /**
     * أمثلة على الاستخدام
     */
    public static function getExamples() {
        return [
            'فستان سهرة أحمر' => self::createSlug('فستان سهرة أحمر'),
            'Evening Dress Red' => self::createSlug('Evening Dress Red'),
            'فستان زفاف أبيض مطرز' => self::createSlug('فستان زفاف أبيض مطرز'),
            'Cocktail Dress Blue' => self::createSlug('Cocktail Dress Blue'),
            'فستان كوكتيل أزرق' => self::createSlug('فستان كوكتيل أزرق')
        ];
    }
}

// تم إزالة الدوال المساعدة لتجنب التضارب مع config/database.php
// استخدم SlugHelper::createSlug() مباشرة
?>
