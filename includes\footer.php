</main> <!-- End main-content -->

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>خدمة العملاء</h4>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>/contact.php">اتصل بنا</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/faq.php">الأسئلة الشائعة</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/orders.php">تتبع الطلب</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/returns.php">سياسة الإرجاع</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/size-guide.php">دليل المقاسات</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>معلومات الشركة</h4>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>/about.php">من نحن</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/careers.php">الوظائف</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/press.php">الصحافة</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/responsibility.php">المسؤولية الاجتماعية</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/terms.php">شروط الاستخدام</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تابعينا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                        <a href="#"><i class="fab fa-snapchat"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                    <div class="app-download">
                        <p>حملي التطبيق</p>
                        <div class="app-buttons">
                            <a href="#"><img src="https://via.placeholder.com/120x40/ff69b4/ffffff?text=App+Store" alt="App Store"></a>
                            <a href="#"><img src="https://via.placeholder.com/120x40/ff69b4/ffffff?text=Google+Play" alt="Google Play"></a>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>طرق الدفع</h4>
                    <div class="payment-methods">
                        <img src="https://via.placeholder.com/40x25/ff69b4/ffffff?text=VISA" alt="Visa">
                        <img src="https://via.placeholder.com/40x25/ff69b4/ffffff?text=MC" alt="Mastercard">
                        <img src="https://via.placeholder.com/40x25/ff69b4/ffffff?text=MADA" alt="Mada">
                        <img src="https://via.placeholder.com/40x25/ff69b4/ffffff?text=STC" alt="STC Pay">
                    </div>
                    <div class="security-badges">
                        <img src="https://via.placeholder.com/60x30/ff69b4/ffffff?text=SSL" alt="SSL">
                        <img src="https://via.placeholder.com/60x30/ff69b4/ffffff?text=SECURE" alt="Secure">
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> شي بوكس. جميع الحقوق محفوظة.</p>
                <div class="footer-links">
                    <a href="<?php echo SITE_URL; ?>/privacy.php">سياسة الخصوصية</a>
                    <a href="<?php echo SITE_URL; ?>/terms.php">شروط الاستخدام</a>
                    <a href="<?php echo SITE_URL; ?>/cookies.php">ملفات تعريف الارتباط</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Quick View Modal -->
    <div class="modal" id="quickViewModal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>عرض سريع</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="quickViewContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- JavaScript Files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo SITE_URL; ?>/js/main.js"></script>

    <!-- Custom Scripts -->
    <script>
        // Site URL for JavaScript
        const SITE_URL = '<?php echo SITE_URL; ?>';
        
        // Initialize cart count
        let cartCount = <?php echo $cart_count ?? 0; ?>;
        
        // Update cart count display
        function updateCartCount(count) {
            cartCount = count;
            document.querySelector('.cart-count').textContent = count;
        }
        
        // Add to cart function
        function addToCart(productId, colorId = null, sizeId = null, quantity = 1) {
            showLoading();
            
            fetch('ajax/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    color_id: colorId,
                    size_id: sizeId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    updateCartCount(data.cart_count);
                    showToast('تم إضافة المنتج إلى السلة بنجاح', 'success');
                } else {
                    showToast(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showToast('حدث خطأ في الشبكة', 'error');
            });
        }
        
        // Show loading
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'flex';
        }
        
        // Hide loading
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }
        
        // Show toast notification
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
                <button class="toast-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            
            // Auto remove
            setTimeout(() => {
                toast.remove();
            }, duration);
        }

        // Banner close functionality
        document.addEventListener('DOMContentLoaded', function() {
            const bannerClose = document.querySelector('.banner-close');
            if (bannerClose) {
                bannerClose.addEventListener('click', function() {
                    document.querySelector('.top-banner').style.display = 'none';
                });
            }

            // Mobile menu toggle
            const menuToggle = document.querySelector('.menu-toggle-btn');
            const mobileNav = document.querySelector('.mobile-navigation');
            const mobileOverlay = document.querySelector('.mobile-nav-overlay');
            const mobileClose = document.querySelector('.mobile-nav-close');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    mobileNav.classList.add('active');
                    mobileOverlay.classList.add('active');
                });
            }

            if (mobileClose) {
                mobileClose.addEventListener('click', function() {
                    mobileNav.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                });
            }

            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function() {
                    mobileNav.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                });
            }

            // Back to top button
            const backToTop = document.getElementById('backToTop');
            if (backToTop) {
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 300) {
                        backToTop.style.display = 'flex';
                    } else {
                        backToTop.style.display = 'none';
                    }
                });

                backToTop.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Dropdown functionality
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const dropdown = this.nextElementSibling;
                    dropdown.classList.toggle('show');
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.account-dropdown')) {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });
        });
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

</body>
</html>
