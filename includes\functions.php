<?php
/**
 * ملف الدوال المساعدة للمتجر
 */

// استدعاء ملف قاعدة البيانات والإعدادات
require_once __DIR__ . '/../config/database.php';

// ملاحظة: الدوال الأساسية موجودة في config/database.php
// sanitize_input, format_price, create_slug, is_active_page, get_stock_status_text, get_order_status_text, generate_order_number

// تنسيق التاريخ
function format_date($date) {
    return date('d/m/Y', strtotime($date));
}

// تنسيق التاريخ والوقت
function format_datetime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

// ملاحظة: دالة create_slug موجودة في config/database.php

// اختصار النص
function truncate_text($text, $length = 100) {
    if (strlen($text) > $length) {
        return substr($text, 0, $length) . '...';
    }
    return $text;
}

// التحقق من صحة البريد الإلكتروني
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// التحقق من صحة رقم الهاتف السعودي
function validate_saudi_phone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من الطول والبداية
    if (strlen($phone) == 10 && substr($phone, 0, 2) == '05') {
        return true;
    }
    
    // التحقق من الرقم الدولي
    if (strlen($phone) == 13 && substr($phone, 0, 4) == '9665') {
        return true;
    }
    
    return false;
}

// تنسيق رقم الهاتف
function format_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 10) {
        return substr($phone, 0, 3) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6);
    }
    
    return $phone;
}

// ملاحظة: دالة generate_order_number موجودة في config/database.php

// حساب نسبة الخصم
function calculate_discount_percentage($original_price, $sale_price) {
    if ($original_price <= 0 || $sale_price <= 0) {
        return 0;
    }
    
    return round((($original_price - $sale_price) / $original_price) * 100);
}

// التحقق من وجود الصورة
function check_image_exists($image_path) {
    if (empty($image_path)) {
        return false;
    }
    
    $full_path = 'uploads/' . $image_path;
    return file_exists($full_path);
}

// الحصول على رابط الصورة
function get_image_url($image_path, $default = 'assets/images/no-image.jpg') {
    if (check_image_exists($image_path)) {
        return 'uploads/' . $image_path;
    }
    
    return $default;
}

// تحويل حالة المخزون إلى نص
function get_stock_status_text($status) {
    switch ($status) {
        case 'in_stock':
            return 'متوفر';
        case 'out_of_stock':
            return 'غير متوفر';
        case 'low_stock':
            return 'كمية قليلة';
        default:
            return 'غير محدد';
    }
}

// تحويل حالة الطلب إلى نص
function get_order_status_text($status) {
    switch ($status) {
        case 'pending':
            return 'في الانتظار';
        case 'confirmed':
            return 'مؤكد';
        case 'processing':
            return 'قيد المعالجة';
        case 'shipped':
            return 'تم الشحن';
        case 'delivered':
            return 'تم التسليم';
        case 'cancelled':
            return 'ملغي';
        default:
            return 'غير محدد';
    }
}

// تحويل طريقة الدفع إلى نص
function get_payment_method_text($method) {
    switch ($method) {
        case 'cod':
            return 'الدفع عند الاستلام';
        case 'bank_transfer':
            return 'تحويل بنكي';
        case 'online':
            return 'دفع إلكتروني';
        default:
            return 'غير محدد';
    }
}

// إنشاء breadcrumb
function generate_breadcrumb($items) {
    $breadcrumb = '<nav class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        if ($index > 0) {
            $breadcrumb .= '<span class="separator">/</span>';
        }
        
        if (isset($item['url']) && !empty($item['url'])) {
            $breadcrumb .= '<a href="' . $item['url'] . '">' . $item['title'] . '</a>';
        } else {
            $breadcrumb .= '<span class="current">' . $item['title'] . '</span>';
        }
    }
    
    $breadcrumb .= '</nav>';
    return $breadcrumb;
}

// تسجيل الأخطاء
function log_error($message, $file = '', $line = '') {
    $log_message = date('Y-m-d H:i:s') . ' - ' . $message;
    
    if (!empty($file)) {
        $log_message .= ' في الملف: ' . $file;
    }
    
    if (!empty($line)) {
        $log_message .= ' السطر: ' . $line;
    }
    
    error_log($log_message . PHP_EOL, 3, 'logs/error.log');
}

// إنشاء رسالة تنبيه
function create_alert($message, $type = 'info') {
    $icons = [
        'success' => 'fas fa-check-circle',
        'error' => 'fas fa-exclamation-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'info' => 'fas fa-info-circle'
    ];
    
    $icon = isset($icons[$type]) ? $icons[$type] : $icons['info'];
    
    return '<div class="alert alert-' . $type . '">
                <i class="' . $icon . '"></i>
                <span>' . $message . '</span>
            </div>';
}

// تحويل الوقت إلى نص نسبي
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return 'منذ ' . $minutes . ' دقيقة';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return 'منذ ' . $hours . ' ساعة';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return 'منذ ' . $days . ' يوم';
    } else {
        return format_date($datetime);
    }
}

// تحديد لون حالة الطلب
function get_order_status_color($status) {
    switch ($status) {
        case 'pending':
            return '#fbbf24'; // أصفر
        case 'confirmed':
            return '#3b82f6'; // أزرق
        case 'processing':
            return '#8b5cf6'; // بنفسجي
        case 'shipped':
            return '#06b6d4'; // سماوي
        case 'delivered':
            return '#10b981'; // أخضر
        case 'cancelled':
            return '#ef4444'; // أحمر
        default:
            return '#6b7280'; // رمادي
    }
}

// تحديد أيقونة حالة الطلب
function get_order_status_icon($status) {
    switch ($status) {
        case 'pending':
            return 'fas fa-clock';
        case 'confirmed':
            return 'fas fa-check';
        case 'processing':
            return 'fas fa-cog fa-spin';
        case 'shipped':
            return 'fas fa-truck';
        case 'delivered':
            return 'fas fa-check-circle';
        case 'cancelled':
            return 'fas fa-times-circle';
        default:
            return 'fas fa-question-circle';
    }
}

// تحويل الأرقام إلى أرقام عربية
function convert_to_arabic_numbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $string);
}

// تحويل الأرقام إلى أرقام إنجليزية
function convert_to_english_numbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($arabic_numbers, $english_numbers, $string);
}

// إنشاء مجلد إذا لم يكن موجوداً
function create_directory_if_not_exists($path) {
    if (!is_dir($path)) {
        mkdir($path, 0777, true);
        return true;
    }
    return false;
}

// حذف ملف بأمان
function safe_delete_file($file_path) {
    if (file_exists($file_path) && is_file($file_path)) {
        return unlink($file_path);
    }
    return false;
}

// الحصول على امتداد الملف
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

// التحقق من نوع الصورة المسموح
function is_allowed_image_type($filename) {
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $extension = get_file_extension($filename);
    
    return in_array($extension, $allowed_types);
}

// تحويل حجم الملف إلى نص قابل للقراءة
function format_file_size($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// إنشاء كلمة مرور عشوائية
function generate_random_password($length = 8) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

// تشفير كلمة المرور
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// التحقق من كلمة المرور
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

// جميع الثوابت معرفة في config/database.php

// رسائل النظام
define('MSG_SUCCESS_ADD_TO_CART', 'تم إضافة المنتج للسلة بنجاح');
define('MSG_ERROR_ADD_TO_CART', 'حدث خطأ في إضافة المنتج للسلة');
define('MSG_SUCCESS_UPDATE_CART', 'تم تحديث السلة بنجاح');
define('MSG_ERROR_UPDATE_CART', 'حدث خطأ في تحديث السلة');
define('MSG_SUCCESS_ORDER_PLACED', 'تم تسجيل طلبك بنجاح');
define('MSG_ERROR_ORDER_FAILED', 'حدث خطأ في تسجيل الطلب');



// دالة لجلب عدد العناصر في السلة
function get_cart_count() {
    if (!isset($_SESSION)) {
        session_start();
    }

    try {
        require_once 'config/database.php';
        $database = new Database();
        $conn = $database->getConnection();

        $session_id = session_id();
        $query = "SELECT SUM(quantity) as total FROM cart WHERE session_id = :session_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':session_id', $session_id);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;

    } catch (Exception $e) {
        return 0;
    }
}

// دالة لجلب إجمالي السلة
function get_cart_total() {
    if (!isset($_SESSION)) {
        session_start();
    }

    try {
        require_once 'config/database.php';
        $database = new Database();
        $conn = $database->getConnection();

        $session_id = session_id();
        $query = "SELECT SUM(
                    CASE
                        WHEN p.sale_price > 0 THEN p.sale_price * c.quantity
                        ELSE p.price * c.quantity
                    END
                ) as total
                FROM cart c
                INNER JOIN products p ON c.product_id = p.id
                WHERE c.session_id = :session_id";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':session_id', $session_id);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;

    } catch (Exception $e) {
        return 0;
    }
}

// دالة لإنشاء رابط آمن
function secure_url($url) {
    return htmlspecialchars($url, ENT_QUOTES, 'UTF-8');
}

// دالة لتنظيف HTML
function clean_html($html) {
    return strip_tags($html, '<p><br><strong><em><ul><ol><li>');
}

// دالة للتحقق من صحة CSRF Token
function verify_csrf_token($token) {
    if (!isset($_SESSION)) {
        session_start();
    }

    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لإنشاء CSRF Token
function generate_csrf_token() {
    if (!isset($_SESSION)) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }

    return $_SESSION['csrf_token'];
}

// دالة لعرض رسائل الجلسة
function display_session_messages() {
    if (!isset($_SESSION)) {
        session_start();
    }

    $messages = '';

    if (isset($_SESSION['success_message'])) {
        $messages .= create_alert($_SESSION['success_message'], 'success');
        unset($_SESSION['success_message']);
    }

    if (isset($_SESSION['error_message'])) {
        $messages .= create_alert($_SESSION['error_message'], 'error');
        unset($_SESSION['error_message']);
    }

    if (isset($_SESSION['warning_message'])) {
        $messages .= create_alert($_SESSION['warning_message'], 'warning');
        unset($_SESSION['warning_message']);
    }

    if (isset($_SESSION['info_message'])) {
        $messages .= create_alert($_SESSION['info_message'], 'info');
        unset($_SESSION['info_message']);
    }

    return $messages;
}

// دالة لتعيين رسالة في الجلسة
function set_session_message($message, $type = 'info') {
    if (!isset($_SESSION)) {
        session_start();
    }

    $_SESSION[$type . '_message'] = $message;
}

?>
