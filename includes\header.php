<?php
// تضمين إعدادات الموقع
require_once __DIR__ . '/settings.php';

// جلب الأقسام الرئيسية للقائمة
if (!isset($category)) {
    require_once __DIR__ . '/Category.php';
    $category = new Category();
}

try {
    $main_categories_stmt = $category->get_main_categories();
    $nav_categories = $main_categories_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $nav_categories = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo getSiteName(); ?></title>

    <!-- Meta Tags -->
    <meta name="description" content="<?php echo isset($page_description) && !empty($page_description) ? htmlspecialchars($page_description) : htmlspecialchars(getSiteDescription()); ?>">
    <meta name="keywords" content="<?php echo isset($page_keywords) && !empty($page_keywords) ? htmlspecialchars($page_keywords) : htmlspecialchars(getSiteKeywords()); ?>">
    <meta name="author" content="<?php echo htmlspecialchars(getSiteName()); ?>">

    <!-- Open Graph Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?><?php echo htmlspecialchars(getSiteName()); ?>">
    <meta property="og:description" content="<?php echo isset($page_description) && !empty($page_description) ? htmlspecialchars($page_description) : htmlspecialchars(getSiteDescription()); ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/images/og-image.jpg">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:type" content="website">
    
    <!-- CSS Files -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/images/favicon.ico">
</head>
<body>
    <!-- Top Banner -->
    <div class="top-banner">
        <div class="container">
            <div class="banner-content">
                <span class="banner-text">🎉 خصم يصل إلى 70% على جميع فساتين السهرة! شحن مجاني للطلبات أكثر من 150 ر.س</span>
                <button class="banner-close">×</button>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Mobile Header -->
            <div class="mobile-header d-lg-none">
                <div class="mobile-header-top">
                    <div class="logo">
                        <?php
                        $logo_path = getLogoPath();
                        if (!empty($logo_path)):
                        ?>
                            <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars(getSiteName()); ?>">
                        <?php endif; ?>
                    </div>
                    
                    <div class="mobile-actions">
                        <a href="<?php echo SITE_URL; ?>/wishlist.php" class="action-item">
                            <i class="far fa-heart"></i>
                            <span class="action-text">المفضلة</span>
                            <span class="badge">0</span>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/cart.php" class="action-item">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="action-text">السلة</span>
                            <span class="badge"><?php echo $cart_count ?? 0; ?></span>
                        </a>
                        <a href="<?php echo SITE_URL; ?>/account.php" class="action-item">
                            <i class="far fa-user"></i>
                            <span class="action-text">حسابي</span>
                        </a>
                        <button class="menu-toggle-btn">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mobile-search">
                    <div class="search-container">
                        <div class="search-box">
                            <input type="text" placeholder="ابحثي عن فساتين، أحذية، إكسسوارات..." name="search" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desktop Header -->
            <div class="desktop-header d-none d-lg-block">
                <div class="header-content">
                    <div class="header-left">
                        <div class="logo">
                            <?php
                            $logo_path = getLogoPath();
                            if (!empty($logo_path)):
                            ?>
                                <img src="<?php echo $logo_path; ?>" alt="<?php echo htmlspecialchars(getSiteName()); ?>">
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="header-center">
                        <div class="search-container">
                            <div class="search-box">
                                <input type="text" placeholder="ابحثي عن فساتين، أحذية، إكسسوارات..." name="search" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                                <button class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="header-right">
                        <div class="header-actions">
                            <a href="<?php echo SITE_URL; ?>/wishlist.php" class="action-item">
                                <i class="far fa-heart"></i>
                                <span class="action-text">المفضلة</span>
                                <span class="badge">0</span>
                            </a>
                            <a href="<?php echo SITE_URL; ?>/cart.php" class="action-item">
                                <i class="fas fa-shopping-bag"></i>
                                <span class="action-text">السلة</span>
                                <span class="badge"><?php echo $cart_count ?? 0; ?></span>
                            </a>
                            <a href="<?php echo SITE_URL; ?>/account.php" class="action-item user-menu">
                                <i class="far fa-user"></i>
                                <span class="action-text">حسابي</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <div class="mobile-nav-overlay"></div>
    <div class="mobile-navigation">
        <div class="mobile-nav-header">
            <div class="nav-user">
                <div class="user-avatar">
                    <i class="far fa-user"></i>
                </div>
                <div class="user-info">
                    <h4>مرحباً بك</h4>
                    <p>سجلي دخولك للحصول على عروض حصرية</p>
                </div>
            </div>
            <button class="nav-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="mobile-nav-content">
            <div class="nav-section">
                <h5>التسوق</h5>
                <ul class="nav-list">
                    <li><a href="<?php echo SITE_URL; ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <?php foreach ($nav_categories as $cat): ?>
                    <li>
                        <a href="<?php echo getCategoryUrl($cat['slug']); ?>">
                            <i class="fas fa-tag"></i> 
                            <?php echo htmlspecialchars($cat['name']); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                    <li><a href="<?php echo SITE_URL; ?>/offers.php"><i class="fas fa-fire"></i> تخفيضات</a></li>
                </ul>
            </div>
            
            <div class="nav-section">
                <h5>حسابي</h5>
                <ul class="nav-list">
                    <li><a href="<?php echo SITE_URL; ?>/account.php"><i class="far fa-user"></i> الملف الشخصي</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/orders.php"><i class="fas fa-box"></i> طلباتي</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/wishlist.php"><i class="far fa-heart"></i> المفضلة</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/addresses.php"><i class="fas fa-map-marker-alt"></i> العناوين</a></li>
                </ul>
            </div>
            
            <div class="nav-section">
                <h5>خدمة العملاء</h5>
                <ul class="nav-list">
                    <li><a href="<?php echo SITE_URL; ?>/contact.php"><i class="fas fa-headset"></i> اتصل بنا</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/faq.php"><i class="fas fa-question-circle"></i> الأسئلة الشائعة</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/returns.php"><i class="fas fa-undo"></i> سياسة الإرجاع</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/size-guide.php"><i class="fas fa-ruler"></i> دليل المقاسات</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-menu">
                    <a href="<?php echo SITE_URL; ?>" class="nav-item <?php echo is_active_page('index'); ?>">الرئيسية</a>
                    <?php foreach ($nav_categories as $cat): ?>
                    <a href="<?php echo getCategoryUrl($cat['slug']); ?>" class="nav-item">
                        <?php echo htmlspecialchars($cat['name']); ?>
                    </a>
                    <?php endforeach; ?>
                    <a href="<?php echo SITE_URL; ?>/offers.php" class="nav-item sale">تخفيضات</a>
                </div>
                <div class="nav-extras">
                    <div class="currency">ر.س</div>
                    <div class="language">العربية</div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        
        <!-- Breadcrumb -->
        <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
        <div class="breadcrumb-section">
            <div class="container">
                <nav class="breadcrumb">
                    <a href="<?php echo SITE_URL; ?>" class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <?php foreach ($breadcrumb as $item): ?>
                    <?php if (isset($item['url'])): ?>
                    <a href="<?php echo $item['url']; ?>" class="breadcrumb-item"><?php echo $item['name']; ?></a>
                    <?php else: ?>
                    <span class="breadcrumb-item active"><?php echo $item['name']; ?></span>
                    <?php endif; ?>
                    <?php endforeach; ?>
                </nav>
            </div>
        </div>
        <?php endif; ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Banner close functionality
        document.addEventListener('DOMContentLoaded', function() {
            const bannerClose = document.querySelector('.banner-close');
            if (bannerClose) {
                bannerClose.addEventListener('click', function() {
                    document.querySelector('.top-banner').style.display = 'none';
                });
            }

            // Mobile navigation functionality
            const menuToggle = document.querySelector('.menu-toggle-btn');
            const mobileNav = document.querySelector('.mobile-navigation');
            const mobileOverlay = document.querySelector('.mobile-nav-overlay');
            const navClose = document.querySelector('.nav-close');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    mobileNav.classList.add('active');
                    mobileOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (navClose) {
                navClose.addEventListener('click', function() {
                    mobileNav.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function() {
                    mobileNav.classList.remove('active');
                    mobileOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                });
            }

            // Search functionality
            const searchBoxes = document.querySelectorAll('.search-box input');
            const searchBtns = document.querySelectorAll('.search-btn');
            
            searchBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const searchBox = this.parentElement.querySelector('input');
                    const searchTerm = searchBox.value.trim();
                    if (searchTerm) {
                        window.location.href = '<?php echo SITE_URL; ?>/products.php?search=' + encodeURIComponent(searchTerm);
                    }
                });
            });

            searchBoxes.forEach(box => {
                box.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const searchTerm = this.value.trim();
                        if (searchTerm) {
                            window.location.href = '<?php echo SITE_URL; ?>/products.php?search=' + encodeURIComponent(searchTerm);
                        }
                    }
                });
            });

            // Hide/Show search box on mobile scroll
            let lastScrollTop = 0;
            let isHeaderHidden = false;
            const mobileSearch = document.querySelector('.mobile-search');
            const mobileHeader = document.querySelector('.mobile-header');
            const scrollThreshold = 100;
            const scrollBuffer = 20; // منطقة عازلة لمنع الاهتزاز
            
            if (mobileSearch && mobileHeader && window.innerWidth <= 768) {
                window.addEventListener('scroll', function() {
                    let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    let scrollDifference = Math.abs(scrollTop - lastScrollTop);
                    
                    // تجاهل التغييرات الصغيرة في التمرير
                    if (scrollDifference < 5) {
                        return;
                    }
                    
                    if (scrollTop > lastScrollTop && scrollTop > scrollThreshold && !isHeaderHidden) {
                        // Scrolling down - hide search and reduce header height
                        mobileSearch.classList.add('hidden');
                        mobileHeader.classList.add('compact');
                        isHeaderHidden = true;
                    } else if (scrollTop < lastScrollTop - scrollBuffer && isHeaderHidden) {
                        // Scrolling up with buffer - show search and restore header height
                        mobileSearch.classList.remove('hidden');
                        mobileHeader.classList.remove('compact');
                        isHeaderHidden = false;
                    }
                    
                    lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
                }, false);
            }
        });
    </script>
