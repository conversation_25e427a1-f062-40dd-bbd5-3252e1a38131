<?php
/**
 * URL Functions for Pretty URLs
 * وظائف إدارة الروابط الجميلة
 */

/**
 * إنشاء رابط للقسم
 * @param string $slug معرف القسم
 * @return string الرابط الكامل
 */
function getCategoryUrl($slug) {
    return SITE_URL . '/category.php?slug=' . urlencode($slug);
}

/**
 * إنشاء رابط للمنتج
 * @param string $slug معرف المنتج
 * @return string الرابط الكامل
 */
function getProductUrl($slug) {
    return SITE_URL . '/product.php?slug=' . urlencode($slug);
}

/**
 * إنشاء رابط للصفحة
 * @param string $page اسم الصفحة
 * @param array $params معاملات إضافية
 * @return string الرابط الكامل
 */
function getPageUrl($page, $params = []) {
    $url = SITE_URL . '/' . $page;
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * إنشاء رابط للبحث
 * @param string $query نص البحث
 * @param array $filters فلاتر إضافية
 * @return string الرابط الكامل
 */
function getSearchUrl($query = '', $filters = []) {
    $params = [];
    
    if (!empty($query)) {
        $params['q'] = $query;
    }
    
    if (!empty($filters)) {
        $params = array_merge($params, $filters);
    }
    
    return getPageUrl('search.php', $params);
}

/**
 * إنشاء رابط للصفحة مع رقم الصفحة
 * @param string $baseUrl الرابط الأساسي
 * @param int $page رقم الصفحة
 * @param array $params معاملات إضافية
 * @return string الرابط الكامل
 */
function getPaginationUrl($baseUrl, $page, $params = []) {
    $params['page'] = $page;
    
    // إزالة معاملات فارغة
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });
    
    if (!empty($params)) {
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        return $baseUrl . $separator . http_build_query($params);
    }
    
    return $baseUrl;
}

/**
 * الحصول على الرابط الحالي
 * @return string الرابط الحالي
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    
    return $protocol . '://' . $host . $uri;
}

/**
 * إنشاء رابط canonical للصفحة
 * @param string $url الرابط
 * @return string رابط canonical
 */
function getCanonicalUrl($url = null) {
    if ($url === null) {
        $url = getCurrentUrl();
    }
    
    // إزالة معاملات غير ضرورية
    $parsed = parse_url($url);
    $canonical = $parsed['scheme'] . '://' . $parsed['host'] . $parsed['path'];
    
    // إضافة معاملات مهمة فقط
    if (isset($parsed['query'])) {
        parse_str($parsed['query'], $params);
        
        // معاملات مهمة يجب الاحتفاظ بها
        $importantParams = ['page', 'sort', 'filter'];
        $filteredParams = array_intersect_key($params, array_flip($importantParams));
        
        if (!empty($filteredParams)) {
            $canonical .= '?' . http_build_query($filteredParams);
        }
    }
    
    return $canonical;
}

/**
 * إنشاء breadcrumb للصفحة
 * @param array $items عناصر breadcrumb
 * @return string HTML للـ breadcrumb
 */
function generateBreadcrumb($items) {
    if (empty($items)) {
        return '';
    }
    
    $html = '<nav aria-label="breadcrumb" class="breadcrumb-nav">';
    $html .= '<ol class="breadcrumb">';
    
    // الصفحة الرئيسية
    $html .= '<li class="breadcrumb-item">';
    $html .= '<a href="' . SITE_URL . '"><i class="fas fa-home"></i> الرئيسية</a>';
    $html .= '</li>';
    
    foreach ($items as $index => $item) {
        $isLast = ($index === count($items) - 1);
        
        $html .= '<li class="breadcrumb-item' . ($isLast ? ' active' : '') . '"';
        if ($isLast) {
            $html .= ' aria-current="page"';
        }
        $html .= '>';
        
        if (!$isLast && isset($item['url'])) {
            $html .= '<a href="' . htmlspecialchars($item['url']) . '">';
        }
        
        $html .= htmlspecialchars($item['title']);
        
        if (!$isLast && isset($item['url'])) {
            $html .= '</a>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * إنشاء meta tags للصفحة
 * @param array $data بيانات الصفحة
 * @return string HTML للـ meta tags
 */
function generateMetaTags($data) {
    $html = '';
    
    // Canonical URL
    if (isset($data['canonical'])) {
        $html .= '<link rel="canonical" href="' . htmlspecialchars($data['canonical']) . '">' . "\n";
    }
    
    // Open Graph tags
    if (isset($data['og_title'])) {
        $html .= '<meta property="og:title" content="' . htmlspecialchars($data['og_title']) . '">' . "\n";
    }
    
    if (isset($data['og_description'])) {
        $html .= '<meta property="og:description" content="' . htmlspecialchars($data['og_description']) . '">' . "\n";
    }
    
    if (isset($data['og_image'])) {
        $html .= '<meta property="og:image" content="' . htmlspecialchars($data['og_image']) . '">' . "\n";
    }
    
    if (isset($data['og_url'])) {
        $html .= '<meta property="og:url" content="' . htmlspecialchars($data['og_url']) . '">' . "\n";
    }
    
    // Twitter Card tags
    if (isset($data['twitter_card'])) {
        $html .= '<meta name="twitter:card" content="' . htmlspecialchars($data['twitter_card']) . '">' . "\n";
    }
    
    return $html;
}

/**
 * تنظيف وتحسين الـ slug
 * @param string $text النص المراد تحويله
 * @return string الـ slug المحسن
 */
function createSlug($text) {
    // تحويل النص إلى أحرف صغيرة
    $text = strtolower($text);
    
    // استبدال المسافات والرموز بـ dash
    $text = preg_replace('/[^a-z0-9\-_]/', '-', $text);
    
    // إزالة الـ dashes المتكررة
    $text = preg_replace('/-+/', '-', $text);
    
    // إزالة الـ dashes من البداية والنهاية
    $text = trim($text, '-');
    
    return $text;
}

/**
 * التحقق من صحة الـ slug
 * @param string $slug الـ slug المراد التحقق منه
 * @return bool صحيح إذا كان الـ slug صالح
 */
function isValidSlug($slug) {
    return preg_match('/^[a-zA-Z0-9\-_]+$/', $slug) && strlen($slug) > 0;
}
?>
