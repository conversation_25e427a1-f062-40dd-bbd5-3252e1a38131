<?php
require_once 'config/database.php';
require_once 'includes/Product.php';
require_once 'includes/Category.php';
require_once 'includes/Cart.php';

$page_title = 'الصفحة الرئيسية';

// إنشاء كائنات الكلاسات
$product = new Product();
$category = new Category();
$cart = new Cart();

// جلب المنتجات المميزة
try {
    $featured_products_stmt = $product->read(8, 0, null, 1);
    $featured_products = $featured_products_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $featured_products = [];
}

// جلب أحدث المنتجات
try {
    $latest_products_stmt = $product->read(8, 0);
    $latest_products = $latest_products_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $latest_products = [];
}

// إذا لم توجد منتجات مميزة، استخدم أحدث المنتجات كمنتجات مميزة
if (empty($featured_products) && !empty($latest_products)) {
    $featured_products = array_slice($latest_products, 0, 4);
}

// جلب الأقسام الرئيسية
$main_categories_stmt = $category->get_main_categories();
$main_categories = $main_categories_stmt->fetchAll(PDO::FETCH_ASSOC);

// عدد العناصر في السلة
$cart_count = $cart->get_cart_count();

include 'includes/header.php';
?>





<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2>تسوقي حسب الفئة</h2>
            <p>اكتشفي أحدث صيحات الموضة</p>
        </div>
        
        <div class="categories-grid">
            <?php foreach ($main_categories as $cat): ?>
            <div class="category-item">
                <a href="<?php echo getCategoryUrl($cat['slug']); ?>" class="category-card">
                    <?php if ($cat['image']): ?>
                        <img src="uploads/categories/<?php echo htmlspecialchars($cat['image']); ?>" alt="<?php echo htmlspecialchars($cat['name']); ?>">
                    <?php else: ?>
                        <img src="uploads/products/sample_dress.jpg" alt="<?php echo htmlspecialchars($cat['name']); ?>">
                    <?php endif; ?>
                    <div class="category-info">
                        <h3><?php echo htmlspecialchars($cat['name']); ?></h3>
                        <span class="item-count"><?php echo rand(50, 200); ?>+ منتج</span>
                    </div>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Trending Products -->
<?php if (!empty($featured_products)): ?>
<section class="trending-section">
    <div class="container">
        <div class="section-header">
            <h2>الأكثر رواجاً</h2>
            <p>المنتجات الأكثر مبيعاً هذا الأسبوع</p>
        </div>
        <div class="products-grid">
            <?php foreach ($featured_products as $prod): ?>
            <div class="product-card">
                <div class="product-image">
                    <?php if ($prod['featured_image']): ?>
                        <img src="<?php echo UPLOAD_URL . $prod['featured_image']; ?>" alt="<?php echo $prod['title']; ?>">
                    <?php else: ?>
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                        </div>
                    <?php endif; ?>
                    <div class="product-badges">
                        <?php if ($prod['is_featured']): ?>
                            <span class="badge trending">ترند</span>
                        <?php endif; ?>
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="badge new">جديد</span>
                        <?php endif; ?>
                    </div>
                    <div class="quick-actions">
                        <button class="quick-btn wishlist"><i class="far fa-heart"></i></button>
                        <button class="quick-btn quick-view"><i class="fas fa-eye"></i></button>
                        <button class="quick-btn compare"><i class="fas fa-random"></i></button>
                    </div>
                    <div class="size-guide">
                        <span>دليل المقاسات</span>
                    </div>
                </div>
                <div class="product-info">
                    <div class="product-category">فساتين سهرة</div>
                    <h3><a href="<?php echo getProductUrl($prod['slug']); ?>"><?php echo htmlspecialchars($prod['title']); ?></a></h3>
                    <div class="rating">
                        <div class="stars">★★★★★</div>
                        <span class="rating-count">(4.8) <?php echo rand(100, 300); ?> تقييم</span>
                    </div>
                    <div class="price">
                        <?php if ($prod['sale_price'] && $prod['sale_price'] > 0): ?>
                            <span class="current"><?php echo format_price($prod['sale_price']); ?></span>
                            <span class="original"><?php echo format_price($prod['price']); ?></span>
                            <span class="discount">-<?php echo round((($prod['price'] - $prod['sale_price']) / $prod['price']) * 100); ?>%</span>
                        <?php else: ?>
                            <span class="current"><?php echo format_price($prod['price']); ?></span>
                        <?php endif; ?>
                    </div>
                    
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="section-footer">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn-view-all">عرض جميع المنتجات</a>
        </div>
    </div>
</section>
<?php endif; ?>



<?php include 'includes/footer.php'; ?>
