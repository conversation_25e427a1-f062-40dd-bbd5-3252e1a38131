// Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    
    // Sidebar functionality
    initSidebar();
    
    // Data tables
    initDataTables();
    
    // Form validation
    initFormValidation();
    
    // File uploads
    initFileUploads();
    
    // Rich text editors
    initRichTextEditors();
    
    // Charts and statistics
    initCharts();
    
    // Notifications
    initNotifications();
});

// Sidebar
function initSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('adminSidebar');
    const overlay = document.getElementById('sidebarOverlay');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            // للموبايل: إظهار/إخفاء السايت بار
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('active');
                if (overlay) {
                    overlay.classList.toggle('active');
                }
            } else {
                // للديسكتوب: طي/توسيع السايت بار
                sidebar.classList.toggle('collapsed');
            }
        });
    }

    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });
    }
    
    // Submenu toggles
    const submenuToggles = document.querySelectorAll('[data-toggle="submenu"]');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            const parent = this.parentElement;
            const submenu = parent.querySelector('.nav-submenu');
            const arrow = this.querySelector('.nav-arrow');
            
            // Close other submenus
            submenuToggles.forEach(otherToggle => {
                if (otherToggle !== this) {
                    const otherParent = otherToggle.parentElement;
                    const otherSubmenu = otherParent.querySelector('.nav-submenu');
                    const otherArrow = otherToggle.querySelector('.nav-arrow');
                    
                    otherParent.classList.remove('active');
                    if (otherSubmenu) otherSubmenu.style.maxHeight = '0';
                    if (otherArrow) otherArrow.style.transform = 'rotate(0deg)';
                }
            });
            
            // Toggle current submenu
            parent.classList.toggle('active');
            
            if (parent.classList.contains('active')) {
                submenu.style.maxHeight = submenu.scrollHeight + 'px';
                arrow.style.transform = 'rotate(-90deg)';
            } else {
                submenu.style.maxHeight = '0';
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
}

// Data Tables
function initDataTables() {
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        // Add search functionality
        addTableSearch(table);
        
        // Add sorting functionality
        addTableSorting(table);
        
        // Add row selection
        addRowSelection(table);
    });
}

function addTableSearch(table) {
    const searchInput = document.querySelector(`[data-table="${table.id}"]`);
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

function addTableSorting(table) {
    const headers = table.querySelectorAll('th[data-sort]');
    
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const column = this.dataset.sort;
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            const isAscending = this.classList.contains('sort-asc');
            
            // Remove sort classes from all headers
            headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            
            // Add appropriate class to current header
            this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
            
            rows.sort((a, b) => {
                const aVal = a.querySelector(`[data-value="${column}"]`)?.textContent || 
                           a.cells[parseInt(column)].textContent;
                const bVal = b.querySelector(`[data-value="${column}"]`)?.textContent || 
                           b.cells[parseInt(column)].textContent;
                
                if (isAscending) {
                    return bVal.localeCompare(aVal, 'ar', { numeric: true });
                } else {
                    return aVal.localeCompare(bVal, 'ar', { numeric: true });
                }
            });
            
            rows.forEach(row => tbody.appendChild(row));
        });
    });
}

function addRowSelection(table) {
    const selectAllCheckbox = table.querySelector('thead input[type="checkbox"]');
    const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }
    
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = table.querySelectorAll('tbody input[type="checkbox"]:checked').length;
            selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
            updateBulkActions();
        });
    });
}

function updateBulkActions() {
    const selectedRows = document.querySelectorAll('tbody input[type="checkbox"]:checked');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        bulkActions.style.display = selectedRows.length > 0 ? 'flex' : 'none';
        
        const countElement = bulkActions.querySelector('.selected-count');
        if (countElement) {
            countElement.textContent = selectedRows.length;
        }
    }
}

// Form Validation
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    });
}

function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    let isValid = true;
    let message = '';
    
    // Required validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // Email validation
    if (type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        message = 'البريد الإلكتروني غير صحيح';
    }
    
    // URL validation
    if (type === 'url' && value && !isValidURL(value)) {
        isValid = false;
        message = 'الرابط غير صحيح';
    }
    
    // Number validation
    if (type === 'number' && value) {
        const min = field.getAttribute('min');
        const max = field.getAttribute('max');
        const numValue = parseFloat(value);
        
        if (min && numValue < parseFloat(min)) {
            isValid = false;
            message = `القيمة يجب أن تكون أكبر من ${min}`;
        }
        
        if (max && numValue > parseFloat(max)) {
            isValid = false;
            message = `القيمة يجب أن تكون أقل من ${max}`;
        }
    }
    
    // Update field appearance
    field.classList.toggle('error', !isValid);
    
    // Show/hide error message
    let errorElement = field.parentNode.querySelector('.field-error');
    if (!isValid && message) {
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
    } else if (errorElement) {
        errorElement.remove();
    }
    
    return isValid;
}

// File Uploads
function initFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFileUpload(this);
        });
    });
    
    // Drag and drop
    const dropZones = document.querySelectorAll('.upload-area');
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        zone.addEventListener('dragleave', function() {
            this.classList.remove('dragover');
        });
        
        zone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            const input = this.parentNode.querySelector('input[type="file"]');
            if (input && files.length > 0) {
                input.files = files;
                handleFileUpload(input);
            }
        });
    });
}

function handleFileUpload(input) {
    const files = input.files;
    const previewContainer = input.parentNode.querySelector('.preview-container');
    
    if (!previewContainer) return;
    
    previewContainer.innerHTML = '';
    
    Array.from(files).forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = createImagePreview(e.target.result, file.name, index);
                previewContainer.appendChild(preview);
            };
            reader.readAsDataURL(file);
        } else {
            const preview = createFilePreview(file.name, index);
            previewContainer.appendChild(preview);
        }
    });
}

function createImagePreview(src, name, index) {
    const div = document.createElement('div');
    div.className = 'file-preview';
    div.innerHTML = `
        <img src="${src}" alt="${name}">
        <div class="file-info">
            <span class="file-name">${name}</span>
            <button type="button" class="remove-file" data-index="${index}">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    div.querySelector('.remove-file').addEventListener('click', function() {
        div.remove();
    });
    
    return div;
}

function createFilePreview(name, index) {
    const div = document.createElement('div');
    div.className = 'file-preview';
    div.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file"></i>
        </div>
        <div class="file-info">
            <span class="file-name">${name}</span>
            <button type="button" class="remove-file" data-index="${index}">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    div.querySelector('.remove-file').addEventListener('click', function() {
        div.remove();
    });
    
    return div;
}

// Rich Text Editors
function initRichTextEditors() {
    const textareas = document.querySelectorAll('textarea[data-editor]');
    
    textareas.forEach(textarea => {
        // Initialize rich text editor (can use libraries like TinyMCE or CKEditor)
        // For now, just add basic formatting toolbar
        addFormattingToolbar(textarea);
    });
}

function addFormattingToolbar(textarea) {
    const toolbar = document.createElement('div');
    toolbar.className = 'formatting-toolbar';
    toolbar.innerHTML = `
        <button type="button" data-command="bold"><i class="fas fa-bold"></i></button>
        <button type="button" data-command="italic"><i class="fas fa-italic"></i></button>
        <button type="button" data-command="underline"><i class="fas fa-underline"></i></button>
        <button type="button" data-command="insertOrderedList"><i class="fas fa-list-ol"></i></button>
        <button type="button" data-command="insertUnorderedList"><i class="fas fa-list-ul"></i></button>
    `;
    
    textarea.parentNode.insertBefore(toolbar, textarea);
    
    toolbar.addEventListener('click', function(e) {
        if (e.target.closest('[data-command]')) {
            const command = e.target.closest('[data-command]').dataset.command;
            document.execCommand(command, false, null);
        }
    });
}

// Charts and Statistics
function initCharts() {
    // Initialize charts using Chart.js or similar library
    const chartElements = document.querySelectorAll('[data-chart]');
    
    chartElements.forEach(element => {
        const chartType = element.dataset.chart;
        const chartData = JSON.parse(element.dataset.chartData || '{}');
        
        // Create chart based on type and data
        createChart(element, chartType, chartData);
    });
}

function createChart(element, type, data) {
    // Placeholder for chart creation
    // In a real implementation, you would use Chart.js or similar
    element.innerHTML = `<div class="chart-placeholder">Chart: ${type}</div>`;
}

// Notifications
function initNotifications() {
    // Auto-hide alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
    
    // Real-time notifications (WebSocket or polling)
    // checkForNotifications();
}

// Utility functions
function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function isValidURL(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

function showLoadingState(element) {
    element.disabled = true;
    element.dataset.originalText = element.innerHTML;
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
}

function hideLoadingState(element) {
    element.disabled = false;
    element.innerHTML = element.dataset.originalText;
}

// Auto-save functionality
function autoSave(formId, interval = 30000) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    setInterval(() => {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم حفظ المسودة تلقائياً', 'info', 2000);
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }, interval);
}

// Export functions
window.adminUtils = {
    validateForm,
    showConfirmDialog,
    showLoadingState,
    hideLoadingState,
    autoSave
};
