// Cart functionality for Evening Dresses Store

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize cart functionality
    initCartButtons();
    initQuantityControls();
    initWishlistButtons();
    
});

// Initialize add to cart buttons
function initCartButtons() {
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productId = this.dataset.productId;
            if (!productId) {
                showToast('معرف المنتج غير صحيح', 'error');
                return;
            }
            
            // Check if we're on product page with options
            const productForm = document.getElementById('addToCartForm');
            if (productForm) {
                // Get selected options from product page
                const colorId = getSelectedOption('color_id');
                const sizeId = getSelectedOption('size_id');
                const quantity = getQuantity();
                
                // Validate required options
                const colorOptions = document.querySelectorAll('input[name="color_id"]');
                const sizeOptions = document.querySelectorAll('input[name="size_id"]');
                
                if (colorOptions.length > 0 && !colorId) {
                    showToast('يرجى اختيار اللون', 'error');
                    return;
                }
                
                if (sizeOptions.length > 0 && !sizeId) {
                    showToast('يرجى اختيار المقاس', 'error');
                    return;
                }
                
                addToCart(productId, colorId, sizeId, quantity);
            } else {
                // Simple add to cart from product grid
                addToCart(productId);
            }
        });
    });
}

// Initialize quantity controls
function initQuantityControls() {
    const quantityControls = document.querySelectorAll('.quantity-selector');
    
    quantityControls.forEach(control => {
        const minusBtn = control.querySelector('.quantity-btn.minus');
        const plusBtn = control.querySelector('.quantity-btn.plus');
        const input = control.querySelector('.quantity-input');
        
        if (minusBtn) {
            minusBtn.addEventListener('click', function() {
                const currentValue = parseInt(input.value);
                const minValue = parseInt(input.min) || 1;
                
                if (currentValue > minValue) {
                    input.value = currentValue - 1;
                    input.dispatchEvent(new Event('change'));
                }
            });
        }
        
        if (plusBtn) {
            plusBtn.addEventListener('click', function() {
                const currentValue = parseInt(input.value);
                const maxValue = parseInt(input.max) || 10;
                
                if (currentValue < maxValue) {
                    input.value = currentValue + 1;
                    input.dispatchEvent(new Event('change'));
                }
            });
        }
        
        if (input) {
            input.addEventListener('change', function() {
                const value = parseInt(this.value);
                const min = parseInt(this.min) || 1;
                const max = parseInt(this.max) || 10;
                
                if (value < min) {
                    this.value = min;
                } else if (value > max) {
                    this.value = max;
                }
                
                // Update cart if this is a cart page
                const cartId = this.dataset.cartId;
                if (cartId) {
                    updateCartQuantity(cartId, this.value);
                }
            });
        }
    });
}

// Initialize wishlist buttons
function initWishlistButtons() {
    const wishlistButtons = document.querySelectorAll('.add-to-wishlist, .wishlist-btn');
    
    wishlistButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productId = this.dataset.productId;
            if (!productId) {
                showToast('معرف المنتج غير صحيح', 'error');
                return;
            }
            
            toggleWishlist(productId, this);
        });
    });
}

// Get selected option value
function getSelectedOption(name) {
    const selected = document.querySelector(`input[name="${name}"]:checked`);
    return selected ? selected.value : null;
}

// Get quantity value
function getQuantity() {
    const quantityInput = document.querySelector('.quantity-input');
    return quantityInput ? parseInt(quantityInput.value) : 1;
}

// Add product to cart
function addToCart(productId, colorId = null, sizeId = null, quantity = 1) {
    showLoading();
    
    const data = {
        product_id: productId,
        quantity: quantity
    };
    
    if (colorId) data.color_id = colorId;
    if (sizeId) data.size_id = sizeId;
    
    fetch('ajax/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            showToast(data.message, 'success');
            
            // Update cart count in header
            updateCartCount(data.cart_count);
            
            // Update cart total if available
            if (data.cart_total !== undefined) {
                updateCartTotal(data.cart_total);
            }
            
            // Add visual feedback
            addCartAnimation();
            
        } else {
            showToast(data.message || 'فشل في إضافة المنتج إلى السلة', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Cart error:', error);
        showToast('حدث خطأ في الشبكة', 'error');
    });
}

// Update cart quantity
function updateCartQuantity(cartId, quantity) {
    const data = {
        action: 'update_quantity',
        cart_id: cartId,
        quantity: quantity
    };
    
    fetch('ajax/update-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update item total
            const itemTotalElement = document.querySelector(`[data-cart-id="${cartId}"] .item-total`);
            if (itemTotalElement) {
                itemTotalElement.textContent = data.item_total;
            }
            
            // Update cart totals
            updateCartTotals(data.cart_totals);
            
            showToast('تم تحديث الكمية', 'success', 2000);
        } else {
            showToast(data.message || 'فشل في تحديث الكمية', 'error');
        }
    })
    .catch(error => {
        console.error('Update cart error:', error);
        showToast('حدث خطأ في الشبكة', 'error');
    });
}

// Remove item from cart
function removeFromCart(cartId) {
    if (!confirm('هل أنت متأكدة من حذف هذا المنتج من السلة؟')) {
        return;
    }
    
    const data = {
        action: 'remove_item',
        cart_id: cartId
    };
    
    fetch('ajax/update-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove item from DOM
            const cartItem = document.querySelector(`[data-cart-id="${cartId}"]`);
            if (cartItem) {
                cartItem.remove();
            }
            
            // Update cart count and totals
            updateCartCount(data.cart_count);
            updateCartTotals(data.cart_totals);
            
            showToast('تم حذف المنتج من السلة', 'success');
            
            // Check if cart is empty
            if (data.cart_count === 0) {
                location.reload(); // Reload to show empty cart state
            }
        } else {
            showToast(data.message || 'فشل في حذف المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Remove cart error:', error);
        showToast('حدث خطأ في الشبكة', 'error');
    });
}

// Toggle wishlist
function toggleWishlist(productId, button) {
    const isAdded = button.classList.contains('added');
    const action = isAdded ? 'remove' : 'add';
    
    const data = {
        product_id: productId,
        action: action
    };
    
    fetch('ajax/toggle-wishlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.classList.toggle('added');
            
            const icon = button.querySelector('i');
            if (button.classList.contains('added')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showToast('تم إضافة المنتج للمفضلة', 'success', 2000);
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showToast('تم حذف المنتج من المفضلة', 'info', 2000);
            }
        } else {
            showToast(data.message || 'حدث خطأ', 'error');
        }
    })
    .catch(error => {
        console.error('Wishlist error:', error);
        showToast('حدث خطأ في الشبكة', 'error');
    });
}

// Update cart count in header
function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
        
        // Add animation
        element.classList.add('updated');
        setTimeout(() => {
            element.classList.remove('updated');
        }, 300);
    });
}

// Update cart total
function updateCartTotal(total) {
    const cartTotalElements = document.querySelectorAll('.cart-total');
    cartTotalElements.forEach(element => {
        element.textContent = formatPrice(total);
    });
}

// Update cart totals (for cart page)
function updateCartTotals(totals) {
    if (totals.subtotal !== undefined) {
        const subtotalElement = document.getElementById('subtotal');
        if (subtotalElement) {
            subtotalElement.textContent = formatPrice(totals.subtotal);
        }
    }
    
    if (totals.shipping !== undefined) {
        const shippingElement = document.getElementById('shipping-cost');
        if (shippingElement) {
            shippingElement.textContent = formatPrice(totals.shipping);
        }
    }
    
    if (totals.total !== undefined) {
        const totalElement = document.getElementById('total-amount');
        if (totalElement) {
            totalElement.textContent = formatPrice(totals.total);
        }
    }
}

// Add cart animation
function addCartAnimation() {
    const cartIcon = document.querySelector('.cart-icon');
    if (cartIcon) {
        cartIcon.classList.add('bounce');
        setTimeout(() => {
            cartIcon.classList.remove('bounce');
        }, 600);
    }
}

// Format price
function formatPrice(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount).replace('SAR', 'ريال');
}

// Quick add to cart (for product grids)
function quickAddToCart(productId) {
    addToCart(productId);
}

// Apply coupon
function applyCoupon() {
    const couponInput = document.getElementById('coupon-code');
    if (!couponInput) return;
    
    const couponCode = couponInput.value.trim();
    if (!couponCode) {
        showToast('يرجى إدخال كود الخصم', 'error');
        return;
    }
    
    const data = {
        action: 'apply_coupon',
        coupon_code: couponCode
    };
    
    fetch('ajax/apply-coupon.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            updateCartTotals(data.cart_totals);
            
            // Show applied coupon
            const appliedCoupon = document.getElementById('applied-coupon');
            if (appliedCoupon) {
                appliedCoupon.style.display = 'block';
                appliedCoupon.querySelector('.coupon-code').textContent = couponCode;
                appliedCoupon.querySelector('.coupon-discount').textContent = formatPrice(data.discount);
            }
            
            couponInput.value = '';
        } else {
            showToast(data.message || 'كود الخصم غير صحيح', 'error');
        }
    })
    .catch(error => {
        console.error('Coupon error:', error);
        showToast('حدث خطأ في الشبكة', 'error');
    });
}

// Remove coupon
function removeCoupon() {
    const data = {
        action: 'remove_coupon'
    };
    
    fetch('ajax/apply-coupon.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم إلغاء كود الخصم', 'info');
            updateCartTotals(data.cart_totals);
            
            // Hide applied coupon
            const appliedCoupon = document.getElementById('applied-coupon');
            if (appliedCoupon) {
                appliedCoupon.style.display = 'none';
            }
        }
    })
    .catch(error => {
        console.error('Remove coupon error:', error);
    });
}

// Export functions for global use
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateCartQuantity = updateCartQuantity;
window.toggleWishlist = toggleWishlist;
window.quickAddToCart = quickAddToCart;
window.applyCoupon = applyCoupon;
window.removeCoupon = removeCoupon;
