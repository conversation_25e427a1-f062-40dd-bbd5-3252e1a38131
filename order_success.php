<?php
session_start();
require_once 'config/database.php';

$page_title = 'تم تأكيد طلبك';

// التحقق من وجود رقم الطلب
if (!isset($_GET['order']) || empty($_GET['order'])) {
    header('Location: index.php');
    exit();
}

$order_number = sanitize_input($_GET['order']);

// جلب تفاصيل الطلب
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $order_query = "SELECT o.*, c.name as city_name, a.name as area_name 
                    FROM orders o 
                    LEFT JOIN cities c ON o.city_id = c.id 
                    LEFT JOIN areas a ON o.area_id = a.id 
                    WHERE o.order_number = :order_number";
    $order_stmt = $conn->prepare($order_query);
    $order_stmt->bindParam(':order_number', $order_number);
    $order_stmt->execute();
    $order = $order_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        header('Location: index.php');
        exit();
    }
    
    // جلب عناصر الطلب
    $items_query = "SELECT oi.*, c.name as color_name, s.name as size_name 
                    FROM order_items oi 
                    LEFT JOIN colors c ON oi.color_id = c.id 
                    LEFT JOIN sizes s ON oi.size_id = s.id 
                    WHERE oi.order_id = :order_id";
    $items_stmt = $conn->prepare($items_query);
    $items_stmt->bindParam(':order_id', $order['id']);
    $items_stmt->execute();
    $order_items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب تفاصيل الطلب";
    $order = null;
    $order_items = [];
}

include 'includes/header.php';
?>

<div class="order-success-container">
    <div class="container">
        
        <?php if ($order): ?>
        
        <!-- Success Message -->
        <div class="success-header">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1>تم تأكيد طلبك بنجاح!</h1>
            <p>شكراً لك على ثقتك بنا. سنقوم بمعالجة طلبك في أقرب وقت ممكن.</p>
        </div>
        
        <!-- Order Details -->
        <div class="order-details-section">
            <div class="order-summary-card">
                <div class="card-header">
                    <h2><i class="fas fa-receipt"></i> تفاصيل الطلب</h2>
                    <span class="order-number">رقم الطلب: #<?php echo $order['order_number']; ?></span>
                </div>
                
                <div class="order-info-grid">
                    <div class="info-section">
                        <h3><i class="fas fa-user"></i> بيانات العميل</h3>
                        <div class="info-details">
                            <p><strong>الاسم:</strong> <?php echo $order['customer_name']; ?></p>
                            <p><strong>البريد الإلكتروني:</strong> <?php echo $order['customer_email']; ?></p>
                            <p><strong>رقم الهاتف:</strong> <?php echo $order['customer_phone']; ?></p>
                            <?php if ($order['customer_phone_alt']): ?>
                                <p><strong>رقم هاتف بديل:</strong> <?php echo $order['customer_phone_alt']; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h3><i class="fas fa-map-marker-alt"></i> عنوان التوصيل</h3>
                        <div class="info-details">
                            <p><strong>المدينة:</strong> <?php echo $order['city_name']; ?></p>
                            <p><strong>المنطقة:</strong> <?php echo $order['area_name']; ?></p>
                            <p><strong>العنوان:</strong> <?php echo $order['shipping_address']; ?></p>
                            <?php if ($order['landmark']): ?>
                                <p><strong>علامة مميزة:</strong> <?php echo $order['landmark']; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h3><i class="fas fa-credit-card"></i> طريقة الدفع</h3>
                        <div class="info-details">
                            <p>
                                <?php 
                                switch($order['payment_method']) {
                                    case 'cod': echo 'الدفع عند الاستلام'; break;
                                    case 'bank_transfer': echo 'تحويل بنكي'; break;
                                    case 'online': echo 'الدفع الإلكتروني'; break;
                                    default: echo $order['payment_method'];
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h3><i class="fas fa-clock"></i> حالة الطلب</h3>
                        <div class="info-details">
                            <span class="status-badge <?php echo $order['status']; ?>">
                                <?php 
                                switch($order['status']) {
                                    case 'pending': echo 'قيد المراجعة'; break;
                                    case 'processing': echo 'قيد المعالجة'; break;
                                    case 'shipped': echo 'تم الشحن'; break;
                                    case 'completed': echo 'مكتمل'; break;
                                    case 'cancelled': echo 'ملغي'; break;
                                    default: echo $order['status'];
                                }
                                ?>
                            </span>
                            <p><strong>تاريخ الطلب:</strong> <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Items -->
            <div class="order-items-card">
                <div class="card-header">
                    <h2><i class="fas fa-shopping-bag"></i> عناصر الطلب</h2>
                </div>
                
                <div class="order-items-list">
                    <?php foreach ($order_items as $item): ?>
                    <div class="order-item">
                        <div class="item-details">
                            <h4><?php echo $item['title']; ?></h4>
                            <div class="item-options">
                                <?php if ($item['color_name']): ?>
                                    <span class="option">اللون: <?php echo $item['color_name']; ?></span>
                                <?php endif; ?>
                                <?php if ($item['size_name']): ?>
                                    <span class="option">المقاس: <?php echo $item['size_name']; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="item-quantity">
                            <span>الكمية: <?php echo $item['quantity']; ?></span>
                        </div>
                        <div class="item-price">
                            <span><?php echo format_price($item['total']); ?></span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="order-totals">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span><?php echo format_price($order['subtotal']); ?></span>
                    </div>
                    <div class="total-row">
                        <span>الشحن:</span>
                        <span><?php echo format_price($order['shipping_cost']); ?></span>
                    </div>
                    <div class="total-row">
                        <span>ضريبة القيمة المضافة:</span>
                        <span><?php echo format_price($order['tax_amount']); ?></span>
                    </div>
                    <div class="total-row total">
                        <span>الإجمالي:</span>
                        <span><?php echo format_price($order['total_amount']); ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="next-steps-section">
            <h2>الخطوات التالية</h2>
            <div class="steps-grid">
                <div class="step">
                    <div class="step-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="step-content">
                        <h3>تأكيد الطلب</h3>
                        <p>ستصلك رسالة تأكيد على بريدك الإلكتروني خلال دقائق</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="step-content">
                        <h3>معالجة الطلب</h3>
                        <p>سنقوم بمراجعة ومعالجة طلبك خلال 24 ساعة</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="step-content">
                        <h3>الشحن</h3>
                        <p>سيتم شحن طلبك خلال 2-3 أيام عمل</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="step-content">
                        <h3>التوصيل</h3>
                        <p>ستستلمين طلبك في العنوان المحدد</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="order-actions">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i>
                العودة للصفحة الرئيسية
            </a>
            <a href="products.php" class="btn btn-outline">
                <i class="fas fa-shopping-bag"></i>
                متابعة التسوق
            </a>
            <button onclick="window.print()" class="btn btn-outline">
                <i class="fas fa-print"></i>
                طباعة الطلب
            </button>
        </div>
        
        <?php else: ?>
        
        <!-- Error Message -->
        <div class="error-message">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1>لم يتم العثور على الطلب</h1>
            <p>عذراً، لم نتمكن من العثور على تفاصيل الطلب المطلوب.</p>
            <a href="index.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>
        </div>
        
        <?php endif; ?>
    </div>
</div>

<style>
.order-success-container {
    padding: 40px 0;
    background: #f8fafc;
    min-height: 80vh;
}

.success-header {
    text-align: center;
    margin-bottom: 40px;
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.success-icon {
    width: 80px;
    height: 80px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.success-icon i {
    font-size: 40px;
    color: white;
}

.success-header h1 {
    font-size: 32px;
    color: #1a202c;
    margin-bottom: 12px;
}

.success-header p {
    color: #6b7280;
    font-size: 16px;
}

.order-details-section {
    display: grid;
    gap: 30px;
    margin-bottom: 40px;
}

.order-summary-card,
.order-items-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e5e7eb;
}

.card-header h2 {
    font-size: 20px;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-number {
    background: #6366f1;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.info-section h3 {
    font-size: 16px;
    color: #374151;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-details p {
    margin-bottom: 8px;
    color: #6b7280;
}

.info-details strong {
    color: #1a202c;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.processing {
    background: #dbeafe;
    color: #1e40af;
}

.status-badge.shipped {
    background: #e0e7ff;
    color: #3730a3;
}

.status-badge.completed {
    background: #d1fae5;
    color: #065f46;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #e5e7eb;
}

.order-item:last-child {
    border-bottom: none;
}

.item-details h4 {
    font-size: 16px;
    color: #1a202c;
    margin-bottom: 4px;
}

.item-options {
    display: flex;
    gap: 12px;
}

.option {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.item-quantity,
.item-price {
    font-weight: 600;
    color: #374151;
}

.order-totals {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #e5e7eb;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.total-row.total {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
}

.next-steps-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.next-steps-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #1a202c;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
}

.step {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    background: #f8fafc;
}

.step-icon {
    width: 60px;
    height: 60px;
    background: #6366f1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
}

.step-icon i {
    font-size: 24px;
    color: white;
}

.step-content h3 {
    font-size: 16px;
    color: #1a202c;
    margin-bottom: 8px;
}

.step-content p {
    color: #6b7280;
    font-size: 14px;
}

.order-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.error-message {
    text-align: center;
    background: white;
    padding: 60px 40px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.error-icon {
    width: 80px;
    height: 80px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.error-icon i {
    font-size: 40px;
    color: white;
}

@media (max-width: 768px) {
    .order-info-grid {
        grid-template-columns: 1fr;
    }
    
    .steps-grid {
        grid-template-columns: 1fr;
    }
    
    .order-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

@media print {
    .order-actions {
        display: none;
    }
    
    .next-steps-section {
        display: none;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
