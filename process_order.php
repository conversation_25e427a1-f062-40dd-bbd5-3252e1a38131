<?php
session_start();
require_once 'config/database.php';
require_once 'includes/Cart.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: cart.php');
    exit();
}

// التحقق من وجود عناصر في السلة
$cart = new Cart();
$cart_data = $cart->get_cart_for_order();
$cart_items = $cart_data['items'];

// تسجيل محتويات السلة للتشخيص
error_log("محتويات السلة: " . print_r($cart_items, true));

if (empty($cart_items)) {
    $_SESSION['error_message'] = 'السلة فارغة';
    header('Location: cart.php');
    exit();
}

// التحقق من صحة عناصر السلة
foreach ($cart_items as $item) {
    if (!isset($item['product_id']) || !isset($item['title']) || !isset($item['price']) || !isset($item['quantity'])) {
        error_log("عنصر غير صحيح في السلة: " . print_r($item, true));
        $_SESSION['error_message'] = 'توجد عناصر غير صحيحة في السلة';
        header('Location: cart.php');
        exit();
    }
}

// جمع البيانات من النموذج
$customer_name = sanitize_input($_POST['customer_name']);
$customer_email = sanitize_input($_POST['customer_email']);
$customer_phone = sanitize_input($_POST['customer_phone']);
$customer_phone_alt = isset($_POST['customer_phone_alt']) ? sanitize_input($_POST['customer_phone_alt']) : '';
$city_id = (int)sanitize_input($_POST['city_id']);
$area_id = (int)sanitize_input($_POST['area_id']);
$address = sanitize_input($_POST['address']);
$landmark = isset($_POST['landmark']) ? sanitize_input($_POST['landmark']) : '';
$delivery_time = isset($_POST['delivery_time']) ? sanitize_input($_POST['delivery_time']) : '';
$delivery_date = isset($_POST['delivery_date']) ? sanitize_input($_POST['delivery_date']) : '';
$notes = isset($_POST['notes']) ? sanitize_input($_POST['notes']) : '';
$payment_method = sanitize_input($_POST['payment_method']);
$shipping_company_id = isset($_POST['shipping_company_id']) ? (int)sanitize_input($_POST['shipping_company_id']) : 0;

// معالجة رفع إيصال التحويل
$receipt_filename = null;
if ($payment_method === 'bank_transfer' && isset($_FILES['transfer_receipt']) && $_FILES['transfer_receipt']['error'] === UPLOAD_ERR_OK) {
    $upload_result = handleReceiptUpload($_FILES['transfer_receipt']);
    if ($upload_result['success']) {
        $receipt_filename = $upload_result['filename'];
    } else {
        $_SESSION['error_message'] = $upload_result['error'];
        header('Location: checkout.php');
        exit();
    }
} elseif ($payment_method === 'bank_transfer') {
    $_SESSION['error_message'] = 'يجب رفع إيصال التحويل البنكي';
    header('Location: checkout.php');
    exit();
}

// حفظ البيانات في الجلسة للحفاظ عليها في حالة الخطأ
$_SESSION['form_data'] = [
    'customer_name' => $customer_name,
    'customer_email' => $customer_email,
    'customer_phone' => $customer_phone,
    'customer_phone_alt' => $customer_phone_alt,
    'city_id' => $city_id,
    'area_id' => $area_id,
    'address' => $address,
    'landmark' => $landmark,
    'delivery_time' => $delivery_time,
    'delivery_date' => $delivery_date,
    'notes' => $notes,
    'payment_method' => $payment_method,
    'shipping_company_id' => $shipping_company_id
];

// التحقق من البيانات المطلوبة
$errors = [];

if (empty($customer_name)) {
    $errors[] = 'الاسم الكامل مطلوب';
}

// التحقق من البريد الإلكتروني فقط إذا تم إدخاله
if (!empty($customer_email) && !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'البريد الإلكتروني غير صحيح';
}

if (empty($customer_phone)) {
    $errors[] = 'رقم الهاتف مطلوب';
}

if (empty($city_id)) {
    $errors[] = 'المدينة مطلوبة';
}

if (empty($area_id)) {
    $errors[] = 'المنطقة مطلوبة';
}

if (empty($address)) {
    $errors[] = 'العنوان التفصيلي مطلوب';
}

if (empty($payment_method)) {
    $errors[] = 'طريقة الدفع مطلوبة';
}

if (empty($shipping_company_id)) {
    $errors[] = 'شركة الشحن مطلوبة';
}

// تم إلغاء شرط الموافقة على الشروط والأحكام

// إذا كانت هناك أخطاء، العودة لصفحة الدفع
if (!empty($errors)) {
    $_SESSION['error_message'] = implode('<br>', $errors);
    $_SESSION['form_data'] = $_POST;
    header('Location: checkout.php');
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // بدء المعاملة
    $conn->beginTransaction();
    
    // حساب الإجماليات
    $subtotal = 0;
    foreach ($cart_items as $item) {
        $price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
        $subtotal += $price * $item['quantity'];
    }

    // الحصول على تكلفة الشحن من شركة الشحن المختارة
    $shipping_cost = 0;
    $shipping_company_name = '';
    if ($shipping_company_id > 0) {
        $shipping_query = "SELECT name, cost FROM shipping_companies WHERE id = :shipping_id";
        $shipping_stmt = $conn->prepare($shipping_query);
        $shipping_stmt->bindParam(':shipping_id', $shipping_company_id);
        $shipping_stmt->execute();
        $shipping_data = $shipping_stmt->fetch(PDO::FETCH_ASSOC);

        if ($shipping_data) {
            $shipping_cost = (float)$shipping_data['cost'];
            $shipping_company_name = $shipping_data['name'];
        } else {
            // إذا لم توجد شركة الشحن، استخدم التكلفة الافتراضية
            $shipping_cost = 50;
            $shipping_company_name = 'الشحن العادي';
        }
    } else {
        // تكلفة افتراضية إذا لم يتم اختيار شركة شحن
        $shipping_cost = 50;
        $shipping_company_name = 'الشحن العادي';
    }

    $tax_rate = 0.0; // لا توجد ضريبة
    $tax_amount = 0;
    $total = $subtotal + $shipping_cost;
    
    // إنشاء رقم الطلب
    $order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // إدراج الطلب
    $order_query = "INSERT INTO orders (
        order_number, customer_name, customer_email, customer_phone, customer_phone_alt,
        city_id, area_id, shipping_address, landmark, delivery_time, delivery_date,
        subtotal, shipping_cost, tax_amount, total_amount, payment_method, notes,
        shipping_company_id, status, created_at
    ) VALUES (
        :order_number, :customer_name, :customer_email, :customer_phone, :customer_phone_alt,
        :city_id, :area_id, :shipping_address, :landmark, :delivery_time, :delivery_date,
        :subtotal, :shipping_cost, :tax_amount, :total_amount, :payment_method, :notes,
        :shipping_company_id, 'pending', NOW()
    )";
    
    $order_stmt = $conn->prepare($order_query);
    $order_stmt->bindParam(':order_number', $order_number);
    $order_stmt->bindParam(':customer_name', $customer_name);
    $order_stmt->bindParam(':customer_email', $customer_email);
    $order_stmt->bindParam(':customer_phone', $customer_phone);
    $order_stmt->bindParam(':customer_phone_alt', $customer_phone_alt);
    $order_stmt->bindParam(':city_id', $city_id);
    $order_stmt->bindParam(':area_id', $area_id);
    $order_stmt->bindParam(':shipping_address', $address);
    $order_stmt->bindParam(':landmark', $landmark);
    $order_stmt->bindParam(':delivery_time', $delivery_time);
    $order_stmt->bindParam(':delivery_date', $delivery_date);
    $order_stmt->bindParam(':subtotal', $subtotal);
    $order_stmt->bindParam(':shipping_cost', $shipping_cost);
    $order_stmt->bindParam(':tax_amount', $tax_amount);
    $order_stmt->bindParam(':total_amount', $total);
    $order_stmt->bindParam(':payment_method', $payment_method);
    $order_stmt->bindParam(':notes', $notes);
    $order_stmt->bindParam(':shipping_company_id', $shipping_company_id);
    
    if (!$order_stmt->execute()) {
        throw new Exception('فشل في إنشاء الطلب');
    }
    
    $order_id = $conn->lastInsertId();
    
    // إدراج عناصر الطلب
    $order_item_query = "INSERT INTO order_items (
        order_id, product_id, title, price, quantity, color_id, size_id, total
    ) VALUES (
        :order_id, :product_id, :title, :price, :quantity, :color_id, :size_id, :total
    )";
    
    $order_item_stmt = $conn->prepare($order_item_query);
    
    foreach ($cart_items as $item) {
        $item_price = ($item['sale_price'] && $item['sale_price'] > 0) ? $item['sale_price'] : $item['price'];
        $item_total = $item_price * $item['quantity'];

        $order_item_stmt->bindParam(':order_id', $order_id);
        $order_item_stmt->bindParam(':product_id', $item['product_id']);
        $order_item_stmt->bindParam(':title', $item['title']);
        $order_item_stmt->bindParam(':price', $item_price);
        $order_item_stmt->bindParam(':quantity', $item['quantity']);
        $order_item_stmt->bindParam(':color_id', $item['color_id']);
        $order_item_stmt->bindParam(':size_id', $item['size_id']);
        $order_item_stmt->bindParam(':total', $item_total);

        if (!$order_item_stmt->execute()) {
            // تسجيل تفاصيل الخطأ
            $error_info = $order_item_stmt->errorInfo();
            error_log("فشل في إضافة عنصر الطلب: " . print_r($error_info, true));
            error_log("بيانات العنصر: " . print_r($item, true));
            throw new Exception('فشل في إضافة عناصر الطلب: ' . $error_info[2]);
        }
    }
    
    // تأكيد المعاملة
    $conn->commit();
    
    // مسح السلة
    $cart->clear_cart();
    
    // إرسال بريد إلكتروني للعميل (اختياري)
    send_order_confirmation_email($customer_email, $order_number, $total);
    
    // حفظ إيصال التحويل إذا كان متوفراً
    if ($receipt_filename && $payment_method === 'bank_transfer') {
        $receipt_query = "INSERT INTO transfer_receipts (
            order_id, receipt_filename, original_filename, file_size, file_type
        ) VALUES (?, ?, ?, ?, ?)";

        $receipt_stmt = $conn->prepare($receipt_query);
        $receipt_stmt->execute([
            $order_id,
            $receipt_filename,
            $_FILES['transfer_receipt']['name'],
            $_FILES['transfer_receipt']['size'],
            $_FILES['transfer_receipt']['type']
        ]);
    }

    // إرسال إشعار للإدارة (اختياري)
    send_admin_notification($order_id, $order_number);

    // حفظ معرف الطلب في الجلسة
    $_SESSION['order_id'] = $order_id;
    $_SESSION['order_number'] = $order_number;
    $_SESSION['success_message'] = 'تم إنشاء طلبك بنجاح! رقم الطلب: ' . $order_number;
    
    // التوجه لصفحة تأكيد الطلب
    header('Location: order_success.php?order=' . $order_number);
    exit();
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if (isset($conn)) {
        $conn->rollback();
    }
    
    $_SESSION['error_message'] = 'حدث خطأ أثناء معالجة طلبك: ' . $e->getMessage();
    header('Location: checkout.php');
    exit();
}

// دوال مساعدة
function send_order_confirmation_email($email, $order_number, $total) {
    // يمكن تطوير هذه الدالة لإرسال بريد إلكتروني فعلي
    // باستخدام PHPMailer أو أي مكتبة أخرى
    return true;
}

function send_admin_notification($order_id, $order_number) {
    // يمكن تطوير هذه الدالة لإرسال إشعار للإدارة
    // عبر البريد الإلكتروني أو SMS أو أي وسيلة أخرى
    return true;
}

function handleReceiptUpload($file) {
    $upload_dir = 'uploads/transfer_receipts/';

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // التحقق من نوع الملف - قبول جميع أنواع الصور و PDF
    $allowed_types = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp',
        'image/webp', 'image/tiff', 'image/svg+xml',
        'application/pdf'
    ];
    $file_type = $file['type'];

    // التحقق الإضافي بناءً على امتداد الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'pdf'];

    if (!in_array($file_type, $allowed_types) && !in_array($file_extension, $allowed_extensions)) {
        return ['success' => false, 'error' => 'نوع الملف غير مدعوم. يُقبل جميع أنواع الصور (JPG, PNG, GIF, BMP, WEBP, TIFF, SVG) و PDF'];
    }

    // التحقق من حجم الملف (10MB)
    $max_size = 10 * 1024 * 1024;
    if ($file['size'] > $max_size) {
        return ['success' => false, 'error' => 'حجم الملف كبير جداً. الحد الأقصى 10 ميجا'];
    }

    // إنشاء اسم ملف فريد
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_filename = 'receipt_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.' . $file_extension;
    $target_path = $upload_dir . $unique_filename;

    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $target_path)) {
        return [
            'success' => true,
            'filename' => $unique_filename,
            'original_name' => $file['name'],
            'size' => $file['size'],
            'type' => $file_type
        ];
    } else {
        return ['success' => false, 'error' => 'فشل في رفع الملف'];
    }
}
?>
