<?php
require_once 'config/database.php';

echo "<h1>🏦 إعداد إعدادات البنك</h1>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // إنشاء جدول إعدادات البنك
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS `bank_settings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `setting_value` text DEFAULT NULL,
      `description` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $conn->exec($create_table_sql);
    echo "<div style='color: green;'>✅ تم إنشاء جدول bank_settings بنجاح</div>";
    
    // إدراج البيانات الافتراضية
    $bank_data = [
        ['bank_name', 'الراجحي', 'اسم البنك'],
        ['account_name', 'مؤسسة اكنان', 'اسم الحساب'],
        ['account_number', '***************', 'رقم الحساب'],
        ['iban', 'SA4980000***************', 'رقم الآيبان'],
        ['transfer_instructions', 'يرجى إرسال إيصال التحويل بعد إتمام العملية', 'تعليمات التحويل']
    ];
    
    $insert_sql = "INSERT IGNORE INTO bank_settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($insert_sql);
    
    $inserted_count = 0;
    foreach ($bank_data as $setting) {
        if ($stmt->execute($setting)) {
            if ($stmt->rowCount() > 0) {
                $inserted_count++;
                echo "<div style='color: green;'>✅ تم إدراج الإعداد: {$setting[0]}</div>";
            } else {
                echo "<div style='color: blue;'>ℹ️ الإعداد موجود بالفعل: {$setting[0]}</div>";
            }
        }
    }
    
    echo "<div style='color: green; margin: 20px 0;'>✅ تم إدراج {$inserted_count} إعداد جديد</div>";
    
    // عرض الإعدادات الحالية
    echo "<h2>📋 الإعدادات الحالية:</h2>";
    
    $select_sql = "SELECT * FROM bank_settings ORDER BY setting_key";
    $stmt = $conn->prepare($select_sql);
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px; border: 1px solid #dee2e6; text-align: right;'>المفتاح</th>";
    echo "<th style='padding: 10px; border: 1px solid #dee2e6; text-align: right;'>القيمة</th>";
    echo "<th style='padding: 10px; border: 1px solid #dee2e6; text-align: right;'>الوصف</th>";
    echo "</tr>";
    
    foreach ($settings as $setting) {
        echo "<tr>";
        echo "<td style='padding: 10px; border: 1px solid #dee2e6;'>" . htmlspecialchars($setting['setting_key']) . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #dee2e6;'>" . htmlspecialchars($setting['setting_value']) . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #dee2e6;'>" . htmlspecialchars($setting['description']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // إنشاء جدول إيصالات التحويل
    $create_receipts_table = "
    CREATE TABLE IF NOT EXISTS `transfer_receipts` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `order_id` int(11) NOT NULL,
      `receipt_filename` varchar(255) NOT NULL,
      `original_filename` varchar(255) NOT NULL,
      `file_size` int(11) NOT NULL,
      `file_type` varchar(50) NOT NULL,
      `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `order_id` (`order_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $conn->exec($create_receipts_table);
    echo "<div style='color: green;'>✅ تم إنشاء جدول transfer_receipts بنجاح</div>";
    
    // إنشاء مجلد الرفع
    $upload_dir = 'uploads/transfer_receipts';
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<div style='color: green;'>✅ تم إنشاء مجلد الرفع: {$upload_dir}</div>";
        } else {
            echo "<div style='color: red;'>❌ فشل في إنشاء مجلد الرفع: {$upload_dir}</div>";
        }
    } else {
        echo "<div style='color: blue;'>ℹ️ مجلد الرفع موجود بالفعل: {$upload_dir}</div>";
    }
    
    // إنشاء ملف .htaccess لحماية المجلد
    $htaccess_content = "Options -Indexes\n<Files *.php>\nDeny from all\n</Files>";
    file_put_contents($upload_dir . '/.htaccess', $htaccess_content);
    echo "<div style='color: green;'>✅ تم إنشاء ملف الحماية .htaccess</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h2>🔗 الروابط</h2>";
echo "<p><a href='checkout.php' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin: 10px;'>💳 صفحة الدفع</a></p>";
echo "<p><a href='admin/bank_settings.php' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold; margin: 10px;'>⚙️ إدارة إعدادات البنك</a></p>";
?>
