<?php
/**
 * ملف إعداد جدول إعدادات الموقع
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div style='font-family: Arial; padding: 20px; max-width: 800px; margin: 0 auto;'>";
    echo "<h2>إعداد جدول إعدادات الموقع</h2>";
    
    // إنشاء جدول site_settings
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS `site_settings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `setting_value` text DEFAULT NULL,
      `setting_type` enum('text','textarea','number','boolean','json') DEFAULT 'text',
      `category` varchar(50) DEFAULT 'general',
      `description` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $conn->exec($create_table_sql);
    echo "<div style='color: green;'>✅ تم إنشاء جدول site_settings بنجاح</div>";
    
    // إدراج البيانات الافتراضية
    $settings_data = [
        ['site_name', 'متجر فساتين السهرة', 'text', 'general', 'اسم الموقع'],
        ['site_description', 'متجر متخصص في فساتين السهرة الأنيقة والراقية', 'textarea', 'general', 'وصف الموقع'],
        ['site_keywords', 'فساتين سهرة, فساتين أنيقة, فساتين راقية', 'textarea', 'seo', 'الكلمات المفتاحية'],
        ['contact_email', '<EMAIL>', 'text', 'contact', 'البريد الإلكتروني للتواصل'],
        ['contact_phone', '+966500000000', 'text', 'contact', 'رقم الهاتف'],
        ['free_shipping_threshold', '500', 'number', 'shipping', 'الحد الأدنى للشحن المجاني'],
        ['tax_rate', '0.15', 'number', 'general', 'معدل الضريبة'],
        ['currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة'],
        ['items_per_page', '12', 'number', 'general', 'عدد العناصر في الصفحة'],
        ['maintenance_mode', '0', 'boolean', 'general', 'وضع الصيانة']
    ];
    
    $insert_sql = "INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_sql);
    
    $inserted_count = 0;
    foreach ($settings_data as $setting) {
        if ($stmt->execute($setting)) {
            if ($stmt->rowCount() > 0) {
                $inserted_count++;
                echo "<div style='color: green;'>✅ تم إدراج الإعداد: {$setting[0]}</div>";
            } else {
                echo "<div style='color: blue;'>ℹ️ الإعداد موجود بالفعل: {$setting[0]}</div>";
            }
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #2d5a2d;'>تم الانتهاء من إعداد جدول الإعدادات!</h3>";
    echo "<p>تم إدراج $inserted_count إعداد جديد</p>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li>استخدام دالة get_site_setting() للحصول على الإعدادات</li>";
    echo "<li>إضافة صفحة إعدادات في لوحة التحكم</li>";
    echo "<li>تخصيص إعدادات الموقع حسب احتياجاتك</li>";
    echo "</ul>";
    echo "</div>";
    
    // عرض الإعدادات الحالية
    echo "<h3>الإعدادات الحالية:</h3>";
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>المفتاح</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>القيمة</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>النوع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: right;'>الفئة</th>";
    echo "</tr>";
    
    $settings_query = $conn->query("SELECT * FROM site_settings ORDER BY category, setting_key");
    while ($setting = $settings_query->fetch()) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$setting['setting_key']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$setting['setting_value']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$setting['setting_type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$setting['category']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; font-family: Arial;'>";
    echo "<h3>خطأ!</h3>";
    echo "<p>حدث خطأ أثناء إعداد جدول الإعدادات: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جدول الإعدادات</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            text-decoration: none;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إعداد جدول إعدادات الموقع</h1>
        
        <div class="nav-links">
            <h3>الخطوات التالية:</h3>
            <a href="admin/login.php">لوحة التحكم</a>
            <a href="index.php">الموقع الرئيسي</a>
            <a href="admin/setup_images_table.php">إعداد نظام الصور</a>
        </div>
    </div>
</body>
</html>
