<?php
/**
 * تحديث الروابط في قاعدة البيانات
 * هذا الملف يحدث أي روابط قديمة موجودة في قاعدة البيانات
 */

require_once 'config/database.php';
require_once 'includes/settings.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تحديث الروابط في قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8fafc; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo "h1 { color: #2d3748; text-align: center; margin-bottom: 30px; }";
echo ".step { margin: 20px 0; padding: 20px; background: #f7fafc; border-radius: 10px; border-left: 4px solid #4299e1; }";
echo ".success { color: #38a169; }";
echo ".error { color: #e53e3e; }";
echo ".info { color: #3182ce; }";
echo ".warning { color: #d69e2e; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تحديث الروابط في قاعدة البيانات</h1>";

try {
    // إنشاء اتصال قاعدة البيانات
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div class='step'>";
    echo "<h2>الخطوة 1: التحقق من الجداول</h2>";
    
    // التحقق من وجود الجداول المطلوبة
    $tables_to_check = ['categories', 'products', 'settings'];
    foreach ($tables_to_check as $table) {
        $check_query = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_query);
        if ($result->rowCount() > 0) {
            echo "<p class='success'>✅ جدول $table موجود</p>";
        } else {
            echo "<p class='error'>❌ جدول $table غير موجود</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>الخطوة 2: تحديث إعدادات الموقع</h2>";
    
    // تحديث رابط الموقع في الإعدادات
    $site_url = SITE_URL;
    
    // التحقق من وجود إعداد site_url
    $check_setting = "SELECT * FROM settings WHERE setting_key = 'site_url'";
    $stmt = $conn->prepare($check_setting);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        // تحديث الإعداد الموجود
        $update_setting = "UPDATE settings SET setting_value = ? WHERE setting_key = 'site_url'";
        $stmt = $conn->prepare($update_setting);
        $stmt->execute([$site_url]);
        echo "<p class='success'>✅ تم تحديث رابط الموقع: $site_url</p>";
    } else {
        // إضافة إعداد جديد
        $insert_setting = "INSERT INTO settings (setting_key, setting_value) VALUES ('site_url', ?)";
        $stmt = $conn->prepare($insert_setting);
        $stmt->execute([$site_url]);
        echo "<p class='success'>✅ تم إضافة رابط الموقع: $site_url</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>الخطوة 3: التحقق من slugs الأقسام</h2>";
    
    // التحقق من أن جميع الأقسام لها slugs
    $categories_without_slug = "SELECT id, name FROM categories WHERE slug IS NULL OR slug = ''";
    $stmt = $conn->prepare($categories_without_slug);
    $stmt->execute();
    $missing_slugs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($missing_slugs)) {
        echo "<p class='warning'>⚠️ تم العثور على " . count($missing_slugs) . " قسم بدون slug</p>";
        
        foreach ($missing_slugs as $category) {
            // إنشاء slug من اسم القسم
            $slug = createSlug($category['name']);
            
            // التأكد من أن الـ slug فريد
            $check_unique = "SELECT COUNT(*) as count FROM categories WHERE slug = ? AND id != ?";
            $stmt = $conn->prepare($check_unique);
            $stmt->execute([$slug, $category['id']]);
            $count = $stmt->fetch()['count'];
            
            if ($count > 0) {
                $slug = $slug . '-' . $category['id'];
            }
            
            // تحديث الـ slug
            $update_slug = "UPDATE categories SET slug = ? WHERE id = ?";
            $stmt = $conn->prepare($update_slug);
            $stmt->execute([$slug, $category['id']]);
            
            echo "<p class='success'>✅ تم إنشاء slug للقسم '{$category['name']}': $slug</p>";
        }
    } else {
        echo "<p class='success'>✅ جميع الأقسام لها slugs</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>الخطوة 4: التحقق من slugs المنتجات</h2>";
    
    // التحقق من أن جميع المنتجات لها slugs
    $products_without_slug = "SELECT id, title FROM products WHERE slug IS NULL OR slug = ''";
    $stmt = $conn->prepare($products_without_slug);
    $stmt->execute();
    $missing_product_slugs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($missing_product_slugs)) {
        echo "<p class='warning'>⚠️ تم العثور على " . count($missing_product_slugs) . " منتج بدون slug</p>";
        
        foreach ($missing_product_slugs as $product) {
            // إنشاء slug من عنوان المنتج
            $slug = createSlug($product['title']);
            
            // التأكد من أن الـ slug فريد
            $check_unique = "SELECT COUNT(*) as count FROM products WHERE slug = ? AND id != ?";
            $stmt = $conn->prepare($check_unique);
            $stmt->execute([$slug, $product['id']]);
            $count = $stmt->fetch()['count'];
            
            if ($count > 0) {
                $slug = $slug . '-' . $product['id'];
            }
            
            // تحديث الـ slug
            $update_slug = "UPDATE products SET slug = ? WHERE id = ?";
            $stmt = $conn->prepare($update_slug);
            $stmt->execute([$slug, $product['id']]);
            
            echo "<p class='success'>✅ تم إنشاء slug للمنتج '{$product['title']}': $slug</p>";
        }
    } else {
        echo "<p class='success'>✅ جميع المنتجات لها slugs</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>الخطوة 5: إحصائيات النظام</h2>";
    
    // إحصائيات الأقسام
    $categories_count = "SELECT COUNT(*) as count FROM categories WHERE is_active = 1";
    $stmt = $conn->prepare($categories_count);
    $stmt->execute();
    $active_categories = $stmt->fetch()['count'];
    echo "<p class='info'>📂 عدد الأقسام النشطة: $active_categories</p>";
    
    // إحصائيات المنتجات
    $products_count = "SELECT COUNT(*) as count FROM products WHERE is_active = 1";
    $stmt = $conn->prepare($products_count);
    $stmt->execute();
    $active_products = $stmt->fetch()['count'];
    echo "<p class='info'>🛍️ عدد المنتجات النشطة: $active_products</p>";
    
    // إحصائيات الروابط
    $total_urls = $active_categories + $active_products + 4; // 4 للصفحات الثابتة
    echo "<p class='info'>🔗 إجمالي الروابط في الموقع: $total_urls</p>";
    
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ تم التحديث بنجاح</h2>";
    echo "<p class='success'>تم تحديث جميع الروابط في قاعدة البيانات بنجاح</p>";
    echo "<p class='info'>يمكنك الآن استخدام النظام الجديد للروابط</p>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='test_url_rewrite.php' style='display: inline-block; padding: 12px 24px; background: #4299e1; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار الروابط</a>";
    echo "<a href='generate_sitemap.php' style='display: inline-block; padding: 12px 24px; background: #38a169; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>إنشاء Sitemap</a>";
    echo "<a href='index.php' style='display: inline-block; padding: 12px 24px; background: #805ad5; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>العودة للموقع</a>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step'>";
    echo "<h2 class='error'>❌ خطأ في التحديث</h2>";
    echo "<p class='error'>حدث خطأ أثناء تحديث قاعدة البيانات:</p>";
    echo "<p class='error'><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p class='error'><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p class='error'><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
